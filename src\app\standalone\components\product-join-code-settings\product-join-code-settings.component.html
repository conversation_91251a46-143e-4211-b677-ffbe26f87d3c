@if (productJoinCodeSettings) {
  @for (setting of productJoinCodeSettings; track setting) {
    <ion-card>
      <div class="header-row">
        <div class="header-left">
          <h6>{{ setting.typeName | translate | async }}</h6>
          <span>{{
            setting.typeName === 'Product'
              ? ('Allow people to request to join your organization and receive access to this product.' | translate | async)
              : ('Allow people to request to join your class at your organization.' | translate | async)
          }}</span>
        </div>
        <div class="header-right">
          <ion-item>
            <ion-toggle [(ngModel)]="setting.isActive" (ngModelChange)="setProductJoinCodesChanged()"></ion-toggle>
          </ion-item>
        </div>
      </div>
      <div class="body">
        <p>
          {{
            setting.typeName === 'Product'
              ? ('Anyone at your organization, with access to this product, will find the join code on their Product tab. This allows them to invite other people at your organization to use the features included in this product.' | translate | async)
              : ('Employees with access to the Classroom feature will see a Join code listed on each of their Classes. These codes can be shared and allow people to request to join the class.' | translate | async)
          }}
        </p>
        @if (setting.isActive) {
          <div class="checkbox-row">
            <ion-checkbox [disabled]="!setting.isActive" [(ngModel)]="setting.isApprovalRequired" (ngModelChange)="setProductJoinCodesChanged()"></ion-checkbox>
            <p>{{ 'Require approval for all instructor join requests' | translate | async }} <span class="light-grey-text">{{ '(recommended)' | translate | async }}</span>. {{ 'If this is unchecked instructor access requests will be immediately approved.' | translate | async }}</p>
          </div>
          @if (setting.typeName === 'Product') {
            <div class="checkbox-row">
              <ion-checkbox [disabled]="!setting.isActive" [(ngModel)]="setting.isLearnerApprovalRequired" (ngModelChange)="setProductJoinCodesChanged()"></ion-checkbox>
              <p>{{ 'Require approval for all student join requests' | translate | async }} <span class="light-grey-text">{{ '(recommended)' | translate | async }}</span>. {{ 'If this is unchecked student access requests will be immediately approved.' | translate | async }}</p>
            </div>
          }
          <!-- <div class="checkbox-row">
          <ion-checkbox [disabled]="!setting.isActive" [(ngModel)]="setting.isGuestCode" (ngModelChange)="setProductJoinCodesChanged()"></ion-checkbox> <p>Allow
          guest access <span class="light-grey-text">(recommended)</span>.
          Provides
          people with immediate access to the
          platform.
          No personal data is shared untill
        you approve their join request.</p>
      </div> -->
          <div class="checkbox-row">
            <ion-checkbox [disabled]="!setting.isActive" [(ngModel)]="setting.isMonthlyRegenCode" (ngModelChange)="setProductJoinCodesChanged()"></ion-checkbox>
            <p>{{ 'Reset join codes every month' | translate | async }} <span class="light-grey-text">{{ '(recommended)' | translate | async }}</span></p>
          </div>
          <div class="checkbox-row">
            <ion-checkbox [disabled]="!setting.isActive" [(ngModel)]="setting.isSelectUserNotificationCode" (ngModelChange)="setProductJoinCodesChanged()"></ion-checkbox>
            {{ 'Choose select people to handle product join requests. This will ensure other product admins are not inundated by join requests.' | translate | async }}
          </div>
          <ng-container>
            @if (users && setting.isSelectUserNotificationCode) {
              <ion-select multiple style="--background-color:{{ backgroundColor }};" interface="popover" [(ngModel)]="setting.users" (ngModelChange)="setProductJoinCodesChanged()">
                @for (user of users; track user) {
                  <ion-select-option [value]="user.id">
                    {{ user.value }}
                  </ion-select-option>
                }
              </ion-select>
            }
          </ng-container>
        }
      </div>
    </ion-card>
  }
}
