<div class="parent-container">
  @for (bullet of bulletList; track bullet; let i = $index) {
    <div class="inner-container">
      <div class="bullet-row">
        <div class="bullet">
          <div [ngClass]="[numbered ? 'circle-numbered' : 'circle-bullet']">
            @if (numbered) {
              <span>{{ i + 1 }}</span>
            }
          </div>
        </div>
        <div>
          <div class="text">{{ bullet.text | translate | async }}</div>
        </div>
      </div>
    </div>
  }
</div>
