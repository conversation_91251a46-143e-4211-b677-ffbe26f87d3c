import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { NetworkOrganization } from '@app/core/contracts/contract';
import { MatChipListbox, MatChipOption, MatChipRemove } from '@angular/material/chips';
import { MatIcon } from '@angular/material/icon';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-chip-list',
    templateUrl: './chip-list.component.html',
    styleUrls: ['./chip-list.component.scss'],
    imports: [MatChipListbox, MatChipOption, MatChipRemove, MatIcon, AsyncPipe, TranslatePipe]
})
export class ChipListComponent implements OnInit {
  @Input() listItems: any[];
  @Input() type: string;
  @Input() disableChipDelete = false;
  @Output() selectedToRemoveOut: EventEmitter<{selected: any, chip: any | null}> = new EventEmitter();

  parentOrgs: NetworkOrganization[] = [];
  childOrgs: NetworkOrganization[] = [];
  constructor() {}

  ngOnInit() {
    if (this.type === 'Organization Networks') {
      this.setOrgNetworkLists();
    }
  }

  setOrgNetworkLists() {
    if (this.listItems.length > 0) {
      this.parentOrgs = this.listItems.filter(x => x.type === 'ParentOf');
      this.childOrgs = this.listItems.filter(x => x.type === 'ChildOf');
    }
  }

  emitRemovedSelected(selected: any, event?: any) {
    const chip = event.target?.offsetParent ?? null;
    this.selectedToRemoveOut.emit({selected: selected, chip: chip});
  }
}
