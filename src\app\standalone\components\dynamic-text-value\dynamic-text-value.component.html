@if (valueIsEmptyString === true && builderPreviewView === true) {
  <div class="parent-container">
    <ion-card class="card-content-container">
      <app-authoring-header [componentName]="componentName"></app-authoring-header>
    </ion-card>
  </div>
}
@if (valueIsEmptyString === false) {
  <div class="parent-container">
    <quill-view [content]="(value === '' ? defaultText : value) | parsePipe: instanceId | async | translate | async" format="html" theme="snow"></quill-view>
  </div>
}
