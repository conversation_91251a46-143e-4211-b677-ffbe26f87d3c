<div class="parent-container">
  <form [formGroup]="imageForm">
    <ion-reorder-group formArrayName="items" [disabled]="false" (ionItemReorder)="handleReorder($any($event))">
      @for (item of imageFormArray.controls; track item; let i = $index) {
        <div [formGroupName]="i">
          @if (!disabled) {
            <div class="reorder-container-left">
              <ion-icon (click)="remove(i)" class="trash-icon" name="trash"></ion-icon>
              <div class="reorder-icon-container">
                <ion-reorder>
                  <ion-icon name="apps-outline"></ion-icon>
                </ion-reorder>
              </div>
            </div>
          }
          <ion-card class="card-container">
            <div class="upload-container">
              <app-file-upload-control
                [noContainer]="true"
                [disabled]="disabled"
                [formControlName]="'asset'"
                [label]="'Image'"
                [fileFormat]="'/*/'"
                [itemBackgroundColor]="'#1E1E1E'"
                [fileTypeBw]="component?.templateField?.fileTypeBw"
                [minFileSize]="component?.templateField?.minFileSize"
                [maxFileSize]="component?.templateField?.maxFileSize"
                [componentType]="component?.componentType?.name"
                [defaultImageUrl]="component?.templateField?.defaultImageUrl"
                [buttonText]="component?.templateField?.buttonText ?? ''"
                [component]="component"
                [placeHolderText]="component?.templateField?.placeHolderText ?? ''"></app-file-upload-control>
            </div>
            <div class="text-input-container">
              <app-text-input-control
                [noPadding]="true"
                [disabled]="disabled"
                [placeHolder]="'Optional, caption your photo'"
                [label]="'Caption'"
                formControlName="caption"
                [backgroundColor]="'#1E1E1E'"></app-text-input-control>
              <app-text-input-control
                [noPadding]="true"
                [disabled]="disabled"
                [placeHolder]="'Optional, height of your photo'"
                [label]="'Height'"
                formControlName="height"
                [backgroundColor]="'#1E1E1E'"></app-text-input-control>
              <app-text-input-control
                [noPadding]="true"
                [disabled]="disabled"
                [placeHolder]="'Optional, aspect ratio of your photo'"
                [label]="'Aspect ratio'"
                formControlName="aspectRatio"
                [backgroundColor]="'#1E1E1E'"></app-text-input-control>
              <app-text-input-control
                [noPadding]="true"
                [disabled]="disabled"
                [placeHolder]="'Optional, object fit styling of your photo'"
                [label]="'Object fit'"
                formControlName="objectFit"
                [backgroundColor]="'#1E1E1E'"></app-text-input-control>
              <mat-slide-toggle style="width: 100%; margin: 10px 0 0 0" color="primary" formControlName="noClickPreview">{{ 'Disable Click Preview' | translate | async }}</mat-slide-toggle>
            </div>
          </ion-card>
        </div>
      }

      @if (!disabled) {
        <div class="section-add-line">
          <div class="icon-container">
            <ion-icon (click)="add()" name="add-circle-outline"></ion-icon>
          </div>
        </div>
      }
    </ion-reorder-group>
  </form>
</div>
