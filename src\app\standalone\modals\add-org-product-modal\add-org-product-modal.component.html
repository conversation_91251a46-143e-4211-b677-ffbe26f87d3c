<div class="user-search-container">
  <form [formGroup]="searchForm">
    <ion-grid>
      <ion-row>
        <ion-col class="header-col">
          <div class="top-heading">{{ 'Select the product you want to add to this organization' | translate | async }}</div>
        </ion-col>
      </ion-row>
      <ion-row class="search-bar-row">
        <ion-col size="12">
          <ion-searchbar
            color="dark"
            formControlName="orgSearch"
            (ionChange)="searchProduct()"
            type="search"
            placeholder="{{ 'Search Products' | translate | async }}"
            showCancelButton="focus"
            debounce="600">
          </ion-searchbar>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
  <ion-content>
    <ion-grid>
      @if (products !== null && products.length > 0) {
        <mat-accordion>
          @for (prod of products; track prod.id) {
            <mat-expansion-panel>
              <mat-expansion-panel-header class="expansion-panel-header" (click)="selected(prod.id)">
                <div class="inner-container">
                  <mat-panel-title>
                    {{ prod.name | translate | async }}
                  </mat-panel-title>
                </div>
              </mat-expansion-panel-header>
              @if (productSelected == prod.id) {
                <app-renew-product
                  [identifier]="prod?.id"
                  (productRenew)="updateProduct($event, prod?.id)"
                  [title]="'Set Product Subscription'"
                  [currentSubscriptionExpires]="currentSubscriptionExpires"
                  [featureType]="type"
                  [newProduct]="true"></app-renew-product>
              }
            </mat-expansion-panel>
          }
        </mat-accordion>
      }
    </ion-grid>
  </ion-content>
  <ion-footer>
    <div class="footer-content">
      <ion-row>
        <ion-col class="cancel-col">
          <ion-button fill="clear" (click)="close()">Cancel</ion-button>
        </ion-col>
        <ion-col class="add-col">
          <div>
            <ion-button [disabled]="productSelected === null" (click)="add()"> Add </ion-button>
          </div>
        </ion-col>
      </ion-row>
    </div>
  </ion-footer>
</div>
