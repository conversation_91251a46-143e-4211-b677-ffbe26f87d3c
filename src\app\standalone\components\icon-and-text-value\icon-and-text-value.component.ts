import { Component, Input, OnInit } from '@angular/core';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { IIconAndText, IInstanceSectionComponent, ITemplateField } from '@app/core/contracts/contract';
import { environment } from '@env/environment';
import { IonicModule } from '@ionic/angular';

@Component({
    selector: 'app-icon-and-text-value',
    templateUrl: './icon-and-text-value.component.html',
    styleUrls: ['./icon-and-text-value.component.scss'],
    imports: [IonicModule, AsyncPipe, TranslatePipe]
})
export class IconAndTextValueComponent implements OnInit {
  @Input() templateField: ITemplateField | undefined;
  @Input() instanceComponent: IInstanceSectionComponent | undefined;
  iconAndText: IIconAndText;
  constructor() {}

  ngOnInit() {
    this.iconAndText = {
      text: this.instanceComponent?.value ?? this.templateField?.defaultText,
      icon: this.templateField?.defaultImageUrl ?? '',
    } as IIconAndText;
  }

  setIcon() {
    if (this.iconAndText.icon === '') {
      return 'assets/images/no-image.png';
    }
    return `${environment.contentUrl}asset/${this.iconAndText.icon}/content`;
  }
}
