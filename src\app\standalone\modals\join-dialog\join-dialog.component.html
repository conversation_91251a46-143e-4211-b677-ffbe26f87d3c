<div class="container">
  <ion-col>
    <mat-icon class="close-icon" svgIcon="close-solid" (click)="onClose()"></mat-icon>
  </ion-col>
  <p class="title">{{ "Don't miss out!" | translate | async }}</p>
  <p class="join-desc">
    {{ 'Enter your Join Code to join an Organization or a Class. Not sure what your join code is?' | translate | async }} <a href="">{{ 'Click Here' | translate | async }}</a>
  </p>
  <ion-input class="join-input" placeholder="{{ 'Enter 6-digit Join Code here' | translate | async }}" [(ngModel)]="code">
    <ion-button class="button" size="big" fill="solid" color="primary" (click)="onFinish()">{{ 'Join' | translate | async }}</ion-button>
  </ion-input>
  @if (userDomain?.organizations?.length) {
    <div class="divider-container">
      <div class="line"><mat-divider class="divider"></mat-divider></div>
      <div class="text mat-typography">{{ 'Or' | translate | async }}</div>
      <div class="line"><mat-divider class="divider"></mat-divider></div>
    </div>
  }
  @if (userDomain?.organizations?.length) {
    <div class="organization-container">
      <p class="title">{{ 'Choose your Organization' | translate | async }}</p>
      <p class="info">{{ "We don't know which school or organization you're from, so there are locked features in your account! Select your organization from this list:" | translate | async }}</p>
      <div class="organizations">
        @for (org of userDomain?.organizations; track org) {
          <div>
            <div class="org-container" [ngClass]="{ selected: org?.selected }" (click)="orgSelected(org.id)" slot="header">
              <ion-grid>
                <ion-row class="org-title">
                  <ion-label>{{ org?.name | translate | async }}</ion-label>
                </ion-row>
                <ion-row class="org-info">
                  <ion-label>{{ org?.type | translate | async }}</ion-label>
                  <span>&#183;</span>
                  <ion-label>{{ org?.city + ',' + org?.country | translate | async }}</ion-label>
                </ion-row>
              </ion-grid>
            </div>
          </div>
        }
      </div>
      <ion-grid>
        <ion-row class="button-row">
          <ion-col size="*" class="remind-col">
            <ion-row>
              <ion-col size="auto" class="checkbox">
                <ion-checkbox [(ngModel)]="userDomain.shouldReceiveDomainPopup"></ion-checkbox>
              </ion-col>
              <ion-col size="*">
                <ion-label>{{ 'Remind me again' | translate | async }}</ion-label>
              </ion-col>
            </ion-row>
          </ion-col>
          <ion-col size="auto" class="join-col">
            <ion-button size="big" fill="solid" color="primary" (click)="onFinish()">{{ 'Join' | translate | async }}</ion-button>
          </ion-col>
        </ion-row>
      </ion-grid>
    </div>
  }
</div>
