<ion-grid class="parent-container">
  <div class="top-header-container">
    <ng-container>
      <ion-row>
        <ion-label class="description-label"> {{ 'Select the products.' | translate | async }} </ion-label>
      </ion-row>
    </ng-container>
  </div>
  <div class="group-container">
    @for (product of products$ | async; track product; let i = $index) {
      <div class="property">
        <div class="heading-container">
          <ion-grid>
            <ion-row class="checkbox-col">
              <ion-col size="1">
                <ion-checkbox [checked]="product.hasRowProduct" (ionChange)="productSelection($event, product)"></ion-checkbox>
              </ion-col>
              <ion-col size="11" (click)="productSelection($event, product)">
                <div class="heading">{{ product?.name | translate | async }}</div>
              </ion-col>
            </ion-row>
          </ion-grid>
        </div>
      </div>
    }
  </div>
  <div class="bottom-container">
    <ion-row>
      <ion-col style="display: flex; justify-content: flex-end" size="12">
        <ion-button (click)="onFinish()">{{ 'Finish' | translate | async }}</ion-button>
      </ion-col>
    </ion-row>
  </div>
</ion-grid>
