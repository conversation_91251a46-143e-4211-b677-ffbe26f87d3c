<div class="container">
  <ion-item [title]="toolTip" #control style="--background:{{ backgroundColor }};">
    <ion-label position="stacked">
      {{ 'Row Manager Achievement Bank' | translate | async }}
      <span title="info" class="reqAsterisk">
        <span>* </span>
        <ion-icon name="information-circle-outline"></ion-icon>
      </span>
    </ion-label>
  </ion-item>
  @if (componentAchievements) {
    @for (achievement of componentAchievements; track achievement) {
      <div class="achievement">
        <ion-card>
          <ion-grid>
            <ion-row>
              <ion-col size="1" class="checkboxes">
                <ion-item>
                  <ion-checkbox color="primary" [(ngModel)]="achievement.isEnabled" (ngModelChange)="updateComponentAchievement(achievement)"></ion-checkbox>
                </ion-item>
              </ion-col>
              <ion-col style="align-items: center">
                <ion-row>
                  <div fxLayout="row" fxLayoutAlign="space-between start" class="title-container">
                    <ion-label class="title"> {{ achievement?.achievementInstance?.feature?.title | translate | async }} </ion-label>
                  </div>
                </ion-row>
                <ion-row>
                  <ion-label class="achievementDescription"> {{ achievement?.achievementInstance?.feature?.description | translate | async }} </ion-label>
                </ion-row>
              </ion-col>
              <ion-col>
                <ion-item>
                  <ion-toggle class="radio" (ngModelChange)="updateComponentAchievement(achievement)" [(ngModel)]="achievement.isRequired"></ion-toggle>
                  <ion-item>
                    <ion-label>{{ 'Required' | translate | async }}</ion-label>
                  </ion-item>
                </ion-item>
              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card>
      </div>
    }
  }
</div>
