@if ((!rowContent.content || rowContent.content?.length === 0) && row.rowType.typeBw >= rowTypes.ModifiablePackage) {
  <app-empty-default-assignment [notAssignment]="row.rowType.typeBw !== rowTypes.ModifiablePackage"></app-empty-default-assignment>
}
@if (rowContent.content) {
  <div class="list-view" cdkDropListGroup>
    @for (gridRow of grid; track gridRow) {
      <div cdkDropList cdkDropListOrientation="vertical" [cdkDropListData]="gridRow" (cdkDropListDropped)="dropListDropped($event)">
        @for (item of gridRow; track item) {
          <app-image-background-row
            cdkDrag
            [cdkDragDisabled]="readingMode"
            [attr.data-listRow-id]="item.title"
            [content]="item"
            [routeParams]="routeParams"
            [rowType]="row?.rowType?.name"
            [row]="row"
            [isEducator]="isEducator"
            [readingMode]="readingMode"
            (contentRemoved)="removedContent()"
            (selectedChanged)="setSelected($event, item)"
            (itemClicked)="navigate(item)">
          </app-image-background-row>
        }
      </div>
    }
    @if (rowContent.content && rowContent.content.length > 0) {
      @if (showLoadMore) {
        <ion-button color="primary" fill="clear" size="small" (click)="loadMore()">{{ 'Show more' | translate | async }} ></ion-button>
      }
    }
  </div>
}
