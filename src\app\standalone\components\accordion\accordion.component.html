<div class="parent-container">
  @if (accordionItemList.length > 0) {
    <ion-reorder-group (ionItemReorder)="setInstanceAccordionRowSortOrderDirect($event)" disabled="false">
      @for (item of accordionItemList; track item) {
        <div>
          @if (!disabled) {
            <div class="reorder-container-left">
              <ion-icon (click)="removeItem(item)" class="trash-icon" name="trash"></ion-icon>
              <div class="reorder-icon-container">
                <ion-reorder>
                  <ion-icon name="apps-outline"></ion-icon>
                </ion-reorder>
              </div>
            </div>
          }
          <ion-card class="card-container">
            <div class="count-heading">{{ item.heading | translate | async }}</div>
            @if (type !== 'Picture Flash Card') {
              <ion-item>
                <ion-label position="stacked">{{ 'Label' | translate | async }}</ion-label>
                <ion-input
                  [disabled]="disabled"
                  [value]="item.title"
                  (ionChange)="saveInputTextDirect($event, item, 'title')"
                  [placeholder]="'Type here...' | translate | async"
                  label="'Title'"></ion-input>
              </ion-item>
            }
            @if (type === 'Picture Flash Card') {
              <ion-item>
                <app-file-upload-control [disabled]="disabled" (valueUpdated)="saveInputTextDirect($event, item, 'picture')"></app-file-upload-control>
              </ion-item>
            }
            <ion-item>
              <ion-label position="stacked">{{ 'Description' | translate | async }}</ion-label>
              <ion-textarea
                [disabled]="disabled"
                [value]="item.description"
                (ionChange)="saveInputTextDirect($event, item, 'description')"
                [placeholder]="'Type here...' | translate | async"
                label="'Description'"></ion-textarea>
            </ion-item>
          </ion-card>
        </div>
      }
    </ion-reorder-group>
  }
  @if (!disabled) {
    <ion-row>
      <ion-col class="add-item-col">
        <ion-icon (click)="addItem()" name="add-circle"></ion-icon>
        <div class="add-button-text">{{ 'Add item' | translate | async }}</div>
      </ion-col>
    </ion-row>
  }
</div>
