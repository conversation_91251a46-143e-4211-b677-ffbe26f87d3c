import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { IComponent, IComponentAchievement, IComponentAchievementIn } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Subject, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { FormsModule } from '@angular/forms';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
  selector: 'app-achievement-bank',
  templateUrl: './achievement-bank.component.html',
  styleUrls: ['./achievement-bank.component.scss'],
  standalone: true,
  imports: [IonicModule, FormsModule, AsyncPipe, TranslatePipe],
})
export class AchievementBankComponent implements OnInit, OnDestroy {
  @Input() component: IComponent;
  @Input() toolTip!: string;
  @Input() backgroundColor = '#181818';
  @Input() itemBackgroundColor = '#4e4e4e';
  componentDestroyed$: Subject<boolean> = new Subject();
  componentAchievements: IComponentAchievement[];

  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.getComponentAchievements();
  }

  getComponentAchievements() {
    this.dataService.getComponentAchievements(this.component.id).subscribe(data => {
      this.componentAchievements = data;
    });
  }

  updateComponentAchievement(componentAchievement: IComponentAchievement) {
    const achievementIn = { ...componentAchievement, achievementInstanceId: componentAchievement.achievementInstance.id, componentId: this.component.id } as IComponentAchievementIn;

    this.dataService
      .updateComponentAchievement(achievementIn)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {});
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
