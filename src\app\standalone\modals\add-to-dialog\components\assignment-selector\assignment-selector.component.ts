import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IInstance } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { IonicModule, PopoverController } from '@ionic/angular';
import { ModalController } from '@ionic/angular/standalone';
import { Subject, takeUntil } from 'rxjs';
import { InstanceCardSelectModalComponent } from '../instance-card-select-modal/instance-card-select-modal.component';
import { InstanceCardComponent } from '../instance-card/instance-card.component';
import { NgClass, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-assignment-selector',
    imports: [IonicModule, FormsModule, InstanceCardComponent, NgClass, AsyncPipe, TranslatePipe],
    providers: [ModalController],
    templateUrl: './assignment-selector.component.html',
    styleUrl: './assignment-selector.component.scss'
})
export class AssignmentSelectorComponent implements OnInit {
  @Input() instanceId: string;
  @Input() height: number = 60;
  @Input() instances: IInstance[];
  @Input() lastModified: IInstance[];
  @Input() assignments: IInstance[];
  @Input() selectedInstance: IInstance;
  @Input() selectedAssignment?: IInstance;
  @Input() loading = false;
  @Output() instanceSelected = new EventEmitter<IInstance>();
  @Output() assignmentSelected = new EventEmitter<IInstance>();
  @Output() createClicked = new EventEmitter();
  @Output() closeClicked = new EventEmitter();

  query: string;
  featureTypeSelectedId: string | undefined;
  backgroundColor = '#292929';
  isInstanceModalOpen = false;
  featureId = '';
  isModalOpen = false;

  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private popOver: PopoverController,
    private dataService: DataService
  ) {}

  ngOnInit(): void {
    this.dataService
      .getFeatureByTypeName('Accredited Learning Container Pages')
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        this.featureId = data.id;
      });
  }

  async selectInstanceClicked(event: any) {
    const popover = await this.popOver.create({
      component: InstanceCardSelectModalComponent,
      cssClass: 'add-search-modal no-backdrop-popover',
      componentProps: { instances: [...this.instances, ...this.lastModified] },
      trigger: 'popover-ref',
    });
    this.isModalOpen = true;

    popover.onDidDismiss().then((result: any) => {
      this.isModalOpen = false;
      if (result.data) {
        this.selectedInstance = result.data;
        this.instanceId = this.selectedInstance.id;
        this.instanceSelected.next(this.selectedInstance);
      }
    });

    await popover.present();
  }

  public addInstance(event: any) {
    this.newAssignment();
  }

  newAssignment() {
    this.createClicked.next(this.selectedInstance.organizationId);
  }

  assign(assignment: IInstance) {
    this.assignmentSelected.next(assignment);
  }

  close() {
    this.closeClicked.next('');
  }

  selectInstance(instance: IInstance) {
    this.selectedAssignment = instance;
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
