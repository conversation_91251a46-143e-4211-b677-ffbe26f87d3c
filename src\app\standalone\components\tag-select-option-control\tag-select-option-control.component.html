<div [ngClass]="!isFilterRowContainer ? 'parent-container' : 'parent-container filter-row-container'" [title]="toolTip" #control>
  @if (multiple) {
    <div class="chiplist-inner-container">
      <ion-row>
        <ion-col class="chiplist-col" size="11">
          <div class="label-container">
            @if (label && showSelected !== true) {
              <ion-label position="stacked">
                {{ label | translate | async }}
                <span title="text" class="reqAsterisk">
                  @if (required) {
                    <span>* </span>
                  }
                  <ion-icon name="information-circle-outline"></ion-icon>
                </span>
                @if (identifierText) {
                  <span class="identifier">{{ identifierText | translate | async }}</span>
                }
              </ion-label>
            }
          </div>
          @if (filterChipList.length > 0 && !disabled) {
            <app-chip-list [type]="'ChipOptionSelect'" (selectedToRemoveOut)="removeChipById($event)" [listItems]="filterChipList" [disableChipDelete]="disableChipDelete"></app-chip-list>
          }
        </ion-col>
        <ion-col class="pop-over-trigger-col" size="1">
          <div class="trigger">
            @if (!disabled) {
              <ion-icon (click)="presentPopover($event)" name="chevron-down-outline"></ion-icon>
            }
          </div>
        </ion-col>
      </ion-row>
    </div>
  } @else {
    <ion-item class="inner-container" [ngClass]="{ 'no-padding': noPadding, 'no-border': noBorder, 'side-panel-input-padding': sidePanelPadding, selected: showSelected }">
      @if (label && showSelected !== true) {
        <ion-label position="stacked">
          {{ label | translate | async }}
          <span title="text" class="reqAsterisk">
            @if (required) {
              <span>* </span>
            }
            <ion-icon name="information-circle-outline"></ion-icon>
          </span>
          @if (identifierText) {
            <span class="identifier">{{ identifierText | translate | async }}</span>
          }
        </ion-label>
      }
      <ion-input #selection [value]="displayValue" [disabled]="disabled" [ngStyle]="{ 'background-color': backgroundColor }" placeholder="{{ placeHolder | translate | async }}" (click)="presentPopover($event)">
      </ion-input>
      @if (displayValue) {
        <ion-button class="clear-button" (click)="clear($event)" slot="end" size="small" fill="clear" color="medium">
          <ion-icon name="close"></ion-icon>
        </ion-button>
      }
    </ion-item>
  }
  <ion-popover class="tag-popover" #tagSelectionPopover [isOpen]="showPopover" (didDismiss)="showPopover = false" side="bottom" [alignment]="multiple ? 'end' : 'start'">
    <ng-template>
      <app-tag-tree-navigation-selector
        [options]="options"
        [noPadding]="noPadding"
        [noBorder]="noBorder"
        [sidePanelPadding]="sidePanelPadding"
        [multiple]="multiple"
        [limitTo]="limitTo"
        [displayValue]="displayValue"
        [textValue]="textValue ?? ''"
        [selectedOptions]="selectedOptions"
        [viewType]="viewType"
        [disabled]="disabled"
        (saveClicked)="saveTagList()"
        (singleTagSelected)="singleSelectTag($event)"
        (multipleTagsSelected)="selectMultipleTags($event)"></app-tag-tree-navigation-selector>
    </ng-template>
  </ion-popover>
</div>
