@if (!isCustom) {
  <div class="parent-container" [ngClass]="showSelected ? 'selected' : 'normal'">
    @if (type !== 'password') {
      <ion-item class="inner-container" [ngClass]="{ 'no-padding': noPadding, 'no-border': noBorder, 'side-panel-input-padding': sidePanelPadding }" [title]="toolTip" #control>
        @if (showSelected !== true) {
          <ion-label position="stacked" class="label-header">
            {{ label | translate | async }}
            <span title="text" class="reqAsterisk">
              @if (required) {
                <span>*</span>
              }
              @if (toolTip) {
                <ion-icon name="information-circle-outline"></ion-icon>
              }
            </span>
            @if (identifierText) {
              <span class="identifier">{{ identifierText }}</span>
            }
          </ion-label>
        }
        @if (type !== 'tel') {
          @if (!limit) {
            <ion-input
              [maxlength]="maxLength"
              [style]="'background: ' + backgroundColor + ';'"
              (keyup)="valueChanged($event)"
              [placeholder]="placeHolder | translate | async"
              [disabled]="disabled"
              [value]="textValue"
              [type]="type">
              @if (options && options.length > 0) {
                <ion-button fill="clear" slot="end" (click)="openOptionsDropdown($event)">
                  {{ selectedOption || ('DEFAULT_STYLE' | translate | async) }}
                </ion-button>
              }
            </ion-input>
          }
          @if (limit) {
            <ion-input
              [max]="limit"
              [min]="min"
              [style]="'background: ' + backgroundColor + ';'"
              (keyup)="valueChanged($event)"
              [placeholder]="placeHolder | translate | async"
              [disabled]="disabled"
              [value]="textValue"
              [type]="type">
              @if (options && options.length > 0) {
                <ion-button fill="clear" slot="end" (click)="openOptionsDropdown($event)">
                  {{ selectedOption || ('DEFAULT_STYLE' | translate | async) }}
                </ion-button>
              }
            </ion-input>
          }
        }
        @if (type === 'tel') {
          <ion-input
            maxlength="14"
            placeholder="{{ '(xxx) xxx-xxxx' | translate | async }}"
            [maskito]="phoneMask"
            [maskitoElement]="maskPredicate"
            [style]="'background: ' + backgroundColor + ';'"
            (keyup)="valueChanged($event)"
            [placeholder]="placeHolder | translate | async"
            [disabled]="disabled"
            [value]="textValue"
            [type]="type"
            (keypress)="numberOnlyValidation($event)">
          </ion-input>
        }
        @if (type === 'text-area') {
          <ion-textarea [value]="textValue" [placeholder]="placeHolder | translate | async"></ion-textarea>
        }
        <!-- @if (errorMessage && touched === true && !showSelected) {
          <ion-text>
            {{ errorMessage | translate | async }}
          </ion-text>
        } -->
      </ion-item>
    }
    @if (type === 'password') {
      <ion-item lines="none" class="inner-container" [ngClass]="{ 'no-padding': noPadding, 'no-border': noBorder, 'side-panel-input-padding': sidePanelPadding }" [title]="toolTip" #control>
        @if (showSelected !== true) {
          <ion-label position="stacked" class="label-header">
            {{ label }}
            <span title="text" class="reqAsterisk">
              @if (required) {
                <span>*</span>
              }
              @if (toolTip) {
                <ion-icon name="information-circle-outline"></ion-icon>
              }
            </span>
            @if (identifierText) {
              <span class="identifier">{{ identifierText }}</span>
            }
          </ion-label>
        }
        <ng-container>
          <div class="password-container" [style]="'background: ' + backgroundColor + '; border-color:' + borderColor">
            <ion-input
              [maxlength]="maxLength"
              (keyup)="valueChanged($event)"
              [placeholder]="placeHolder | translate | async"
              [disabled]="disabled"
              [value]="textValue"
              [type]="showPassword === true ? 'text' : 'password'">
            </ion-input>
            <a class="type-toggle" (click)="togglePasswordVisibility()">
              <ion-icon class="show-option" [title]="'SHOW_PASSWORD' | translate | async" [hidden]="showPassword" name="eye-off-outline"></ion-icon>
              <ion-icon class="hide-option" [title]="'HIDE_PASSWORD' | translate | async" [hidden]="!showPassword" name="eye-outline"></ion-icon>
            </a>
          </div>
        </ng-container>
      </ion-item>
    }
    @if (errorMessage && touched === true && showSelected) {
      <ion-text>
        {{ errorMessage | translate | async }}
      </ion-text>
    }
  </div>
}

@if (isCustom) {
  <ion-input
    style="--background-color:{{ backgroundColor }};"
    (keyup)="valueChanged($event)"
    [placeholder]="placeHolder | translate | async"
    [disabled]="disabled"
    [value]="textValue"
    [type]="type"></ion-input>
  @if (errorMessage && touched === true) {
    <ion-text>
      {{ errorMessage | translate | async }}
    </ion-text>
  }
}
