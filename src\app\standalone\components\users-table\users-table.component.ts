import { SelectionModel } from '@angular/cdk/collections';
import { Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { <PERSON><PERSON><PERSON>, Sort, MatSortHeader } from '@angular/material/sort';
import { MatTableDataSource, MatTable, MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, MatCell, MatHeaderRowDef, MatHeaderRow, MatRowDef, MatRow } from '@angular/material/table';
import { GlobalToastService } from '@app/core/services/global-toast.service';
import {
  IInstanceSectionComponent,
  IOrganizationTableUser,
  IProductOrganization,
  IProductTableUser,
  ITag,
  IUser,
  IUserClaimSearch,
  IUserRole,
  IUserSetup,
  OrganizationTableUserLite,
} from '@app/core/contracts/contract';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { AlertService } from '@app/core/services/alert-service';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { RolesService } from '@app/core/services/roles.service';
import { ModalController, PopoverController, IonicModule } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';
import { AuthService } from '@app/core/services/auth-service';
import { MatCheckbox } from '@angular/material/checkbox';
import { NgClass, DatePipe, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { PersonaChipListComponent } from '../persona-chip-list/persona-chip-list.component';
import { TagPopoverComponent } from '../tag-popover/tag-popover.component';
import { AddUserModalComponent } from '@app/standalone/modals/add-user-modal/add-user-modal.component';
import { ConfirmAddUserModalComponent } from '@app/standalone/modals/confirm-add-user-modal/confirm-add-user-modal..component';
import { CreateEntityModalComponent } from '@app/standalone/modals/create-entity-modal/create-entity-modal.component';
import { UpdateUserRoleComponent } from '@app/standalone/modals/update-user-role-popover/update-user-role-popover.component';
import { UserOrganizationsModalComponent } from '@app/standalone/modals/user-organizations-modal/user-organizations-modal.component';

@Component({
    selector: 'app-users-table',
    templateUrl: './users-table.component.html',
    styleUrls: ['./users-table.component.scss'],
    imports: [
        IonicModule,
        FormsModule,
        ReactiveFormsModule,
        MatTable,
        MatSort,
        MatColumnDef,
        MatHeaderCellDef,
        MatHeaderCell,
        MatCheckbox,
        MatCellDef,
        MatCell,
        MatSortHeader,
        NgClass,
        PersonaChipListComponent,
        TagPopoverComponent,
        MatHeaderRowDef,
        MatHeaderRow,
        MatRowDef,
        MatRow,
        DatePipe,
        AsyncPipe,
        TranslatePipe,
    ]
})
export class UsersTableComponent implements OnInit, OnDestroy {
  @ViewChild('sort', { static: true }) sort: MatSort;
  @ViewChild('parentAddPopover') parentAddPopover: any;
  @Input() instanceSectionComponent?: IInstanceSectionComponent;
  @Input() name: string | undefined | null;
  @Input() id: string | null | undefined;
  @Input() type: string;
  @Input() productOrg: IProductOrganization | undefined | null;
  userRoles: IUserRole[] = [];
  dataSource = new MatTableDataSource<any>();
  displayedColumns: string[] = ['select', 'id', 'name', 'details', 'email', 'designation', 'status', 'address', 'personas', 'lastActivity'];
  isParentPopoverOpen = false;
  currentAmount = 0;
  getAmount = 25;
  moreResults = false;
  searchForm: UntypedFormGroup;
  repoSearchValue: UntypedFormControl;
  selection = new SelectionModel<any>(true, []);
  componentDestroyed$: Subject<boolean> = new Subject();
  canManage: boolean | undefined = false;
  userCount: number;

  constructor(
    private dataService: DataService,
    public popoverController: PopoverController,
    public modalController: ModalController,
    private toast: GlobalToastService,
    private rolesService: RolesService,
    private alertService: AlertService,
    private instanceService: InstanceService,
    private authService: AuthService
  ) {}

  ngOnInit() {
    if (this.id) {
      this.getUserRoles();
      this.canManage = this.authService.userContext?.canManage;
      this.addManageTableColumns();
      this.getTableUsersData(this.id, false);
      if (this.type === 'Product Manager') {
        this.getProductTableUsersByProdOrgIdCount(this.id);
        this.displayedColumns.pop();
      } else if (this.type === 'Organization Manager') {
        this.getOrganizationTableUsersByOrgIdCount(this.id);
      }
      this.createFormControls();
      this.createForm();
    }
  }

  getOrganizationTableUsersByOrgIdCount(id: string) {
    this.dataService
      .getOrganizationTableUsersByOrgIdCount(id)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((userCount: number) => {
        this.userCount = userCount;
      });
  }

  getProductTableUsersByProdOrgIdCount(id: string) {
    this.dataService
      .getProductTableUsersByProdOrgIdCount(id)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((userCount: number) => {
        this.userCount = userCount;
      });
  }

  addManageTableColumns() {
    if (this.canManage) {
      if (this.type === 'Organization Manager') {
        this.displayedColumns.push('qlikUser');
      }
      this.displayedColumns.push('userTags');
    }
  }

  createFormControls() {
    this.repoSearchValue = new UntypedFormControl('');
  }
  createForm() {
    this.searchForm = new UntypedFormGroup({
      repoSearchValue: this.repoSearchValue,
    });
  }

  searchRepoValue() {
    this.currentAmount = 0;
    if (this.id) {
      this.getTableUsersData(this.id, false);
    }
  }

  getUserRoles() {
    this.dataService
      .getUserRoles()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((userRoles: IUserRole[]) => {
        this.userRoles = userRoles;
      });
  }

  getTableUsersData(id: string, loadMore: boolean, sort: Sort | null = null) {
    if (id !== undefined) {
      const currentAmount = loadMore ? this.currentAmount : 0;
      const getAmount = loadMore ? this.getAmount : this.currentAmount !== 0 ? this.currentAmount : this.getAmount;

      if (this.type === 'Product Manager') {
        this.dataService
          .getProductTableUsersByProdOrgId(id, currentAmount, getAmount, sort?.active, sort?.direction, this.repoSearchValue?.value)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe((tableUsers: IProductTableUser[]) => {
            if (tableUsers) {
              tableUsers = tableUsers.map(x => ({
                ...x,
                personas: x.personas?.map(t => {
                  return {
                    id: t.id,
                    name: t.name,
                    parentId: t.parentId,
                  } as ITag;
                }),
              }));

              //LoadMoreData
              if (!loadMore) {
                this.setDataSource(tableUsers);
                this.currentAmount += tableUsers.length;
              } else {
                tableUsers.forEach(user => {
                  this.dataSource.data = [...this.dataSource.data, user];
                });
                this.currentAmount += tableUsers.length;
              }

              if (tableUsers.length < this.getAmount) {
                this.moreResults = false;
              } else {
                this.moreResults = true;
              }
            } else {
              this.setDataSource([]);
            }
          });
      } else if (this.type === 'Organization Manager') {
        this.dataService
          .getOrganizationTableUsersByOrgId(id, currentAmount, getAmount, sort?.active, sort?.direction, this.repoSearchValue?.value)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe((tableUsers: IOrganizationTableUser[]) => {
            if (tableUsers) {
              tableUsers = tableUsers.map(x => ({
                ...x,
                personas: x.personas?.map(t => {
                  return {
                    id: t.id,
                    name: t.name,
                    parentId: t.parentId,
                  } as ITag;
                }),
              }));

              //LoadMoreData
              if (!loadMore) {
                this.setDataSource(tableUsers);
                this.currentAmount += tableUsers.length;
              } else {
                tableUsers.forEach(user => {
                  this.dataSource.data = [...this.dataSource.data, user];
                });
                this.currentAmount += tableUsers.length;
              }

              if (tableUsers.length < this.getAmount) {
                this.moreResults = false;
              } else {
                this.moreResults = true;
              }
            } else {
              this.setDataSource([]);
            }
          });
      }
    }
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    if (this.isAllSelected() === false) {
      this.dataSource.data.forEach(row => this.selection.select(row));
    } else {
      this.selection.clear();
    }
  }

  setDataSource(users: any[]) {
    this.dataSource = new MatTableDataSource(users);
    this.dataSource.sort = this.sort;
  }

  presentParentAddPopover(event: Event) {
    this.parentAddPopover.event = event;
    this.isParentPopoverOpen = true;
  }

  openBulkUpload() {
    if (this.type === 'organization manager') {
      this.instanceService.openInstance('bulk-uploader', this.id);
    }
  }

  async addUserModal() {
    if (this.type === 'Organization Manager') {
      const modal = await this.modalController.create({
        component: AddUserModalComponent,
        componentProps: { id: this.id, type: this.type },
      });

      modal.onDidDismiss().then(value => {
        if (value.data && value.data.length > 0) {
          this.confirmAddOrganizationUserModal(value.data);
        }
      });

      await modal.present();
    } else if (this.type === 'Product Manager') {
      const modal = await this.modalController.create({
        component: AddUserModalComponent,
        componentProps: { id: this.id, type: this.type },
      });

      modal.onDidDismiss().then(value => {
        if (value.data && value.data.length > 0) {
          this.confirmAddOrganizationUserModal(value.data);
        }
      });

      await modal.present();
    }
  }

  async createUserModal() {
    const modal = await this.modalController.create({
      component: CreateEntityModalComponent,
      cssClass: 'create-entity-modal',
      componentProps: { id: this.id, type: this.type, productOrg: this.productOrg },
    });

    modal.onDidDismiss().then(async value => {
      if (value.data) {
        const user = {
          email: value.data.email,
          username: value.data.username,
          firstName: value.data.firstName,
          lastName: value.data.lastName,
          organizationId: value.data.organizationId,
          notifyNewUser: value.data.notifyNewUser,
        } as IUser;
        this.addUser(user, value.data as IUserSetup);
      }
    });

    await modal.present();
  }

  async addUser(user: IUser, userSetup: IUserSetup) {
    this.dataService
      .addUser(user)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(id => {
        if (id) {
          this.dataService.setupUser(id, userSetup).subscribe();
        }
      });
  }

  async confirmAddOrganizationUserModal(selectedUsers: IUserClaimSearch[]) {
    const modal = await this.modalController.create({
      component: ConfirmAddUserModalComponent,
      componentProps: { selectedUsers: selectedUsers, name: this.name, type: this.type },
    });

    modal.onDidDismiss().then(value => {
      this.isParentPopoverOpen = false;
      if (value.data && value.data && value.data.length > 0) {
        if (this.type === 'Organization Manager') {
          this.saveOrganizationUsers(value.data);
        } else if (this.type === 'Product Manager') {
          this.saveProductOrganizationUsers(value.data);
        }
      }
    });

    await modal.present();
  }

  saveOrganizationUsers(orgUsers: IUserClaimSearch[]) {
    if (this.id) {
      this.dataService
        .addOrganizationUsers(this.id, orgUsers)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((addedUsers: IOrganizationTableUser[]) => {
          if (addedUsers.length > 0) {
            addedUsers.forEach(user => {
              this.dataSource.data = [...this.dataSource.data, user];
            });
          }
        });
    }
  }

  saveProductOrganizationUsers(productOrgUsers: IUserClaimSearch[]) {
    if (this.id) {
      this.dataService
        .addProductOrganizationUsers(this.id, productOrgUsers)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((addedUsers: IProductTableUser[]) => {
          const addedUsersLength = addedUsers.length;
          if (addedUsersLength > 0) {
            this.userCount += addedUsersLength;
            addedUsers.forEach(user => {
              this.dataSource.data = [...this.dataSource.data, user];
            });
          }
        });
    }
  }

  async updateProductOrgUserRole(element: any) {
    const userId = element.userId;
    const modal = await this.modalController.create({
      component: UserOrganizationsModalComponent,
      cssClass: 'user-organizations-modal',
      componentProps: { userId: userId, organizationId: this.id, featureType: this.type, name: element.name },
      backdropDismiss: false,
    });
    await modal.present();
  }

  async updateProductOrgUserRolePopover(event: any, id: string, roleId: string) {
    const popover = await this.popoverController.create({
      component: UpdateUserRoleComponent,
      cssClass: 'update-user-role-modal',
      componentProps: { roleId: roleId, userRoles: this.userRoles },
      event: event,
      side: 'bottom',
    });

    popover.onDidDismiss().then(result => {
      if (result.data) {
        if (this.type === 'Product Manager') {
          this.dataService
            .updateProductOrgUserRole(id, result.data)
            .pipe(takeUntil(this.componentDestroyed$))
            .subscribe(updated => {
              if (updated) {
                this.showAlert();
                const updatedRole = this.userRoles.find(x => x.id === result.data);
                const index = this.dataSource.data.findIndex(x => x.id === id);
                this.dataSource.data[index].roleName = updatedRole?.name;
              }
            });
        }
        if (this.type === 'Organization Manager') {
          this.dataService
            .updateOrgUserRole(id, result.data)
            .pipe(takeUntil(this.componentDestroyed$))
            .subscribe(updated => {
              if (updated) {
                this.showAlert();
                const updatedRole = this.userRoles.find(x => x.id === result.data);
                const index = this.dataSource.data.findIndex(x => x.id === id);
                this.dataSource.data[index].roleName = updatedRole?.name;
              }
            });
        }
      }
    });

    await popover.present();
  }

  showAlert() {
    this.alertService.presentAlert('', 'User role updated!');
  }

  updateStatus(status: string) {
    if (status === 'Deleted') {
      this.alertService
        .presentAlert(
          'Confirm Removal',
          this.type === 'Organization Manager' ? 'Are you sure you want to remove selected user/s from this Organization.' : 'Are you sure you want to remove selected user/s from this  Product.'
        )
        .then(() => {
          this.updateTableUsers(status);
        });
    } else {
      this.updateTableUsers(status);
    }
  }

  updateTableUsers(status: string) {
    if (this.selection.selected?.length === 0) {
      return;
    }

    this.selection.selected.forEach(user => {
      const index = this.dataSource.data.findIndex(x => x.id === user.id);
      this.dataSource.data[index].status = status;
      user.status = status === 'Deny' ? 'Suspended' : status;
    });

    if (this.type === 'Product Manager') {
      this.dataService
        .updateProductOrganizationTableUsers(this.selection.selected.map(x => ({ ...x }) as OrganizationTableUserLite))
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(data => {
          if (data) {
            this.toast.presentToast('Users updated successfully');
            if (status === 'Deleted' && this.id) {
              this.dataSource = new MatTableDataSource(this.dataSource.data.filter(x => x.status !== 'Deleted'));
              this.getProductTableUsersByProdOrgIdCount(this.id);
            }
          }
        });
    } else if (this.type === 'Organization Manager') {
      if (this.id) {
        this.dataService
          .updateOrganizationTableUsers(
            this.id,
            this.selection.selected.map(x => ({ ...x }) as OrganizationTableUserLite)
          )
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(data => {
            if (data) {
              this.toast.presentToast('Users updated successfully');
              if (status === 'Deleted' && this.id) {
                this.dataSource = new MatTableDataSource(this.dataSource.data.filter(x => x.status !== 'Deleted'));
                this.getOrganizationTableUsersByOrgIdCount(this.id);
              }
            }
          });
      }
    }
  }

  saveUserTags(tags: string | null, userId: string) {
    if (tags) {
      const tagIds = JSON.parse(tags) as string[];
      this.dataService.updateRemoveUserTags(tagIds, userId).pipe(takeUntil(this.componentDestroyed$)).subscribe();
    }
  }

  sortChanged(event: Sort) {
    if (this.id) {
      this.getTableUsersData(this.id, false, event);
    }
  }

  hasAdminAccess() {
    return this.rolesService.hasFeatureRoleAccess([ActionTypes.Manage, ActionTypes.Publish]);
  }

  updateQlikUser(element) {
    element.qlikUser = element.qlikUser !== 'true';
    this.dataService
      .updateOrganizationUserQlik(element.id, element.userId, element.qlikUser)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(qlikUserEnabled => {
        const index = this.dataSource.data.findIndex(x => x.id === element.id);
        this.dataSource.data[index].qlikUser = qlikUserEnabled;
      });
    return null;
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
