import { NgC<PERSON>, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { Component, Input, OnInit } from '@angular/core';
import { AbstractControl, FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { MatExpansionPanel, MatExpansionPanelHeader } from '@angular/material/expansion';
import { MatIcon } from '@angular/material/icon';
import { GlobalToastService } from '@app/core/services/global-toast.service';
import { IComponent, IPasswordResetIn } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { IonicModule } from '@ionic/angular';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';

@Component({
    selector: 'app-password-control',
    templateUrl: './password-control.component.html',
    styleUrls: ['./password-control.component.scss'],
    imports: [MatExpansionPanel, MatExpansionPanelHeader, IonicModule, FormsModule, ReactiveFormsModule, TextInputControlComponent, NgClass, MatIcon, AsyncPipe, TranslatePipe]
})
export class PasswordControlComponent implements OnInit {
  @Input() component!: IComponent;
  passwordForm: UntypedFormGroup;
  date = new Date();
  controlBackground = 'none';
  passwordReset: IPasswordResetIn;
  passwordMatchError = false;
  validPasswordError = false;
  passwordIncludesUppercaseRegex = new RegExp('^(?=.*?[A-Z])');
  passwordIncludesNumberRegex = new RegExp('^(?=.*?[0-9])');
  passwordLengthGreaterThanSevenRegex = new RegExp('^.{8,}$');
  passwordIncludesSpecialCharRegex = new RegExp('^(?=.*?[#?!@$%^&*-])');
  passwordIncludesUppercase = false;
  passwordIncludesNumber = false;
  passwordLengthGreaterThanSeven = false;
  passwordIncludesSpecialChar = false;
  passwordStrength: number;
  showPasswordInformation = false;

  constructor(
    private formBuilder: UntypedFormBuilder,
    private dataService: DataService,
    private toast: GlobalToastService
  ) {}

  get emptyPassword() {
    return this.passwordForm?.get('newPassword')?.value === '';
  }

  checkPasswords: ValidatorFn = (group: AbstractControl): ValidationErrors | null => {
    const pass = group?.get('newPassword')?.value;
    const confirmPass = group?.get('confirmPassword')?.value;
    this.passwordMatchError = pass === confirmPass ? false : true;
    return pass === confirmPass ? null : { notSame: true };
  };

  strongPasswordCheck: ValidatorFn = (group: AbstractControl): ValidationErrors | null => {
    if (this.emptyPassword === false) {
      this.passwordIncludesUppercase = this.passwordIncludesUppercaseRegex.test(group?.get('newPassword')?.value);
      this.passwordIncludesNumber = this.passwordIncludesNumberRegex.test(group?.get('newPassword')?.value);
      this.passwordLengthGreaterThanSeven = this.passwordLengthGreaterThanSevenRegex.test(group?.get('newPassword')?.value);
      this.passwordIncludesSpecialChar = this.passwordIncludesSpecialCharRegex.test(group?.get('newPassword')?.value);
      if (group.dirty === true) {
        this.setPasswordStrength();
      }
    } else {
      this.passwordStrength = -1;
    }

    const valid = !group?.get('newPassword')?.value.includes(' ');
    this.validPasswordError = !valid ? true : false;
    return !valid ? { formatError: true } : group?.get('newPassword')?.value;
  };

  setPasswordStrength() {
    this.passwordStrength = 0;
    if (this.passwordIncludesUppercase) {
      this.passwordStrength++;
    }
    if (this.passwordIncludesNumber) {
      this.passwordStrength++;
    }
    if (this.passwordLengthGreaterThanSeven) {
      this.passwordStrength++;
    }
    if (this.passwordIncludesUppercase && this.passwordIncludesNumber && this.passwordLengthGreaterThanSeven && this.passwordIncludesSpecialChar) {
      this.passwordStrength++;
    }
    return this.passwordStrength;
  }

  getBackgroundColor() {
    if (this.passwordStrength === -1) {
      return this.controlBackground;
    } else if (this.passwordMatchError === true || this.passwordStrength <= 1) {
      return '#311c1d';
    } else if (this.passwordStrength === 2) {
      return '#31291c';
    } else if (this.passwordStrength >= 3) {
      return '#112212';
    }
    return this.controlBackground;
  }

  getColor(borderColor = false) {
    if (this.passwordStrength === -1) {
      return '#4e4e4e';
    } else if (this.passwordStrength <= 1 || (borderColor === true && this.passwordMatchError === true)) {
      return '#fd3629';
    } else if (this.passwordStrength === 2) {
      return '#ff902b';
    } else if (this.passwordStrength >= 3) {
      return '#469009';
    }
    return '#4e4e4e';
  }

  clickedShowPasswordInformation() {
    this.showPasswordInformation = !this.showPasswordInformation;
  }

  ngOnInit() {
    this.createForm();
  }

  createForm() {
    this.passwordForm = this.formBuilder.nonNullable.group(
      {
        newPassword: ['', Validators.required],
        confirmPassword: ['', Validators.required],
      },
      { validators: [this.checkPasswords, this.strongPasswordCheck] }
    );
  }

  resetForm() {
    this.passwordStrength = -1;
    this.passwordMatchError = false;
    this.passwordForm.reset();
  }

  savePassword() {
    if (this.emptyPassword === true) {
      this.toast.presentToast('Please enter a new password');
    } else if (this.validPasswordError === true) {
      this.toast.presentToast('The new password is not valid please enter a stronger password');
    } else if (this.passwordMatchError === true) {
      this.toast.presentToast('Password does not match');
    } else {
      this.passwordReset = {
        newPassword: this.passwordForm.controls.newPassword.value,
        confirmPassword: this.passwordForm.controls.confirmPassword.value,
      };
      this.dataService.resetPassword(this.passwordReset).subscribe(() => {
        this.toast.presentToast('Password has been changed successfully');
        this.resetForm();
      });
    }
  }
}
