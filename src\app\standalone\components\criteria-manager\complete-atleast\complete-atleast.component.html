@if (completeAtleastForm) {
  <form [formGroup]="completeAtleastForm">
    <div class="parent-container">
      <ion-row>
        <ion-col size="auto" class="heading-col">{{ 'Complete at least:' | translate | async }}</ion-col>
        <ion-col class="min-value" size="1">
          <ion-input (ionChange)="checkInputLength($event)" min="0" type="number" type="number" formControlName="minValue"></ion-input>
        </ion-col>
        <ion-col size="6" class="ref-value">
          <span class="middle-span">{{ 'of' | translate | async }}</span>
          <app-select-option-control
            style="width: 100%"
            (emitSelected)="setSelectedContent($event)"
            formControlName="refId"
            [textValue]="getFormControlValue()"
            [multiple]="true"
            [backgroundColor]="'#242323'"
            [criteriaType]="type"
            [isCustom]="true"
            [linkTypeName]="'Features'">
          </app-select-option-control>
        </ion-col>
      </ion-row>
    </div>
  </form>
}
