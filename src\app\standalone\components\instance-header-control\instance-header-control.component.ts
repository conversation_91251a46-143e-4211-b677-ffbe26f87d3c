import { Component, forwardRef, Input, OnInit } from '@angular/core';
import { environment } from '@env/environment';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';
import { EntityTypes } from '@app/core/enums/entity-type';
import { IonicModule } from '@ionic/angular';
import { NgClass, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { ParseContentPipe } from '../../../shared/pipes/parse-content';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
@Component({
    selector: 'app-instance-header-control',
    templateUrl: './instance-header-control.component.html',
    styleUrls: ['./instance-header-control.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => InstanceHeaderControlComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => InstanceHeaderControlComponent),
        },
    ],
    imports: [IonicModule, NgClass, AsyncPipe, ParseContentPipe, TranslatePipe]
})
export class InstanceHeaderControlComponent extends BaseControlComponent implements OnInit {
  @Input() backgroundColor = '#181818';
  @Input() itemBackgroundColor = '';
  @Input() iconAssetId: string | undefined;
  @Input() status: string | undefined | null;
  @Input() instanceId: string | undefined;
  @Input() instanceTitle: string | undefined;
  @Input() featureTitle: string | undefined;
  @Input() instanceDescriptors: string | undefined;
  @Input() featureDescriptors: string | undefined;
  @Input() entityType: number | undefined;
  iconUrl: string;
  enityTypes = EntityTypes;
  isLocked = false;

  constructor() {
    super();
  }

  ngOnInit() {
    this.instanceTitle = this.instanceTitle ? this.instanceTitle : 'Instance Title Here..';
    this.featureTitle = this.featureTitle ? this.featureTitle : 'Feature Title Here..';

    if (this.iconAssetId) {
      this.iconUrl = `${environment.contentUrl}asset/${this.iconAssetId}/content`;
    } else if (this.instanceId) {
      this.iconUrl = 'assets/images/no-image.png'; // `${environment.contentUrl}asset/default/${this.instanceId}?systemProperty=${'Instance.IconAssetId'}`;
    }
  }

  updateLockStatus() {
    if (this.isLocked) {
      this.isLocked = false;
    } else {
      this.isLocked = true;
    }
  }

  displayNoImage() {
    this.iconUrl = 'assets/images/no-image.png';
  }
}
