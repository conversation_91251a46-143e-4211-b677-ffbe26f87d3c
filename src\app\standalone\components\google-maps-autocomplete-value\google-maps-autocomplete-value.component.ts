import { AfterViewInit, Component, ElementRef, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ITemplateField } from '@app/core/contracts/contract';
import { GoogleMapsService } from '@app/core/services/google-maps.service';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { Subject, takeUntil } from 'rxjs';
import { NgClass, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { MatIcon } from '@angular/material/icon';
import { TextValueComponent } from '../text-value/text-value.component';

@Component({
    selector: 'app-google-maps-autocomplete-value',
    templateUrl: './google-maps-autocomplete-value.component.html',
    styleUrls: ['./google-maps-autocomplete-value.component.scss'],
    imports: [NgClass, MatIcon, TextValueComponent, AsyncPipe, TranslatePipe]
})
export class GoogleMapsAutocompleteValueComponent implements OnInit, AfterViewInit, OnDestroy {
  @Input() featureType!: string | undefined;
  @Input() templateField: ITemplateField;

  @ViewChild('mapContainer') mapElement: ElementRef;
  componentDestroyed$: Subject<boolean> = new Subject();
  addressLine1: string | undefined;
  city: string | undefined;
  country: string | undefined;
  state: string | undefined;
  zip: string | undefined;
  geocoder: google.maps.Geocoder;
  mapSet = false;

  constructor(
    private systemPropertiesService: SystemPropertiesService,
    private googleMapsService: GoogleMapsService
  ) {}
  ngAfterViewInit(): void {
    if (this.templateField?.showMap) {
      this.googleMapsService.registerGoogleAsync().then(() => {
        this.geocoder = new google.maps.Geocoder();
        if (this.addressLine1 && this.city && this.state && this.country && this.zip) {
          this.geocodeAddress(`${this.addressLine1}, ${this.city},${this.state}, ${this.country}, ${this.zip}`);
        } else if (this.addressLine1 && this.state && this.country && this.zip) {
          this.geocodeAddress(`${this.addressLine1}, ${this.state}, ${this.country}, ${this.zip}`);
        }
      });
    }
  }

  ngOnInit() {
    this.loadData();
    this.systemPropertiesService.reload$.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.loadData();
      if (this.templateField?.showMap) {
        if (this.geocoder && this.addressLine1 && this.city && this.state && this.country && this.zip) {
          this.geocodeAddress(`${this.addressLine1}, ${this.city},${this.state}, ${this.country}, ${this.zip}`);
        } else if (this.geocoder && this.addressLine1 && this.state && this.country && this.zip) {
          this.geocodeAddress(`${this.addressLine1}, ${this.state}, ${this.country}, ${this.zip}`);
        }
      }
    });
  }

  geocodeAddress(address: string) {
    this.geocoder.geocode({ address }, (results, status) => {
      if (status === google.maps.GeocoderStatus.OK) {
        if (results) {
          const location = results[0].geometry.location;
          const lat = location.lat();
          const lng = location.lng();

          const map: google.maps.Map = new google.maps.Map(document.getElementById('mapContainer') as HTMLElement, {
            center: { lat, lng },
            zoom: 15,
            disableDefaultUI: true,
          });

          new google.maps.Marker({
            position: { lat, lng },
            map,
            title: 'My Marker',
          });
          this.mapSet = true;
        }
      } else {
        console.error('Geocode error:', status);
      }
    });
  }

  loadData() {
    if (this.featureType === 'Organization Manager') {
      const i1 = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('AddressLine1'));
      const i2 = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('City'));
      const i5 = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('Province'));
      const i6 = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('Country'));
      const i7 = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('PostalCode'));

      this.addressLine1 = i1?.value;
      this.city = i2?.value;
      this.country = i6?.value;
      this.state = i5?.value;
      this.zip = i7?.value;
    }

    if (
      this.featureType === 'Internal' ||
      this.featureType === 'Learning Objects' ||
      this.featureType === 'Marketing Objects' ||
      this.featureType === 'Training Objects' ||
      this.featureType === 'Landing Pages'
    ) {
      const i1 = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('AddressLine1'));
      const i2 = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('City'));
      const i5 = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('Province'));
      const i6 = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('Country'));
      const i7 = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('PostalCode'));

      this.addressLine1 = i1?.value;
      this.city = i2?.value;
      this.country = i6?.value;
      this.state = i5?.value;
      this.zip = i7?.value;
    }

    if (this.featureType === 'User Manager') {
      const i1 = this.systemPropertiesService.userProperties.find(x => x.key.includes('address'));
      const i2 = this.systemPropertiesService.userProperties.find(x => x.key.includes('stateorprovince'));
      const i3 = this.systemPropertiesService.userProperties.find(x => x.key.includes('country'));
      const i4 = this.systemPropertiesService.userProperties.find(x => x.key.includes('postalcode'));

      this.addressLine1 = i1?.value;
      this.country = i3?.value;
      this.state = i2?.value;
      this.zip = i4?.value;
    }
  }

  ngOnDestroy(): void {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
