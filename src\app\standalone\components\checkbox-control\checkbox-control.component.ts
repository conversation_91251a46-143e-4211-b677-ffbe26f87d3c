import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, forwardRef } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { IonicModule } from '@ionic/angular';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';

@Component({
    selector: 'app-checkbox-control',
    templateUrl: './checkbox-control.component.html',
    styleUrls: ['./checkbox-control.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => CheckboxControlComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => CheckboxControlComponent),
        },
    ],
    imports: [IonicModule, AsyncPipe, TranslatePipe]
})
export class CheckboxControlComponent extends BaseControlComponent implements OnInit, OnChanges, OnDestroy {
  @Input() value?: string;
  @Input() inheritedPropertyValue: string | null;
  @Input() labelPlacement = 'start';
  @Input() justify = 'start';
  @Input() indeterminate = false;
  @Input() override label!: string;
  @Output() checkboxChanged = new EventEmitter();
  checkboxValue = false;

  componentDestroyed$: Subject<boolean> = new Subject();

  constructor() {
    super();
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['inheritedPropertyValue']) {
      this.setCheckboxValue();
    }
  }

  ngOnInit() {
    this.setCheckboxValue();
    this.fieldValueChanged.pipe(takeUntil(this.componentDestroyed$)).subscribe(event => {
      if (event != '') {
        this.checkboxValue = event.toLowerCase() === 'true';
        this.checkboxChanged.emit(event);
      }
    });
  }

  setCheckboxValue() {
    if (this.inheritedPropertyValue && this.inheritedPropertyValue !== '') {
      this.checkboxValue = this.inheritedPropertyValue.toLowerCase() === 'true';
    } else if (this.textValue && this.textValue !== '') {
      this.checkboxValue = this.textValue.toLowerCase() === 'true';
    } else if (this.value && this.value !== '') {
      this.checkboxValue = this.value.toLowerCase() === 'true';
    }
  }

  checkboxInteraction(event: any) {
    event.target.value = event.detail.checked.toString();
    this.setValue(event);
  }

  ngOnDestroy(): void {
    this.componentDestroyed$.next(true);
  }
}
