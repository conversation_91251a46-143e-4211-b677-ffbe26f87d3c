<ion-row class="header-row">
  <ion-col class="header" size="9"> <h1>{{ 'SETTINGS' | translate | async }}</h1> </ion-col>
  @if (hasChanges) {
    <ion-col size="3" class="end-col-buttons">
      <ion-button (click)="saveOrganizationSettings()" color="warning">{{ 'SAVE' | translate | async }}</ion-button>
    </ion-col>
  }
</ion-row>

<mat-accordion>
  @for (organizationSetting of organizationSettings; track organizationSetting) {
    <mat-expansion-panel (opened)="openGroup(true)" (closed)="openGroup(false)">
      <mat-expansion-panel-header class="expansion-panel-header">
        <div class="inner-panel">
          <div class="heading">{{ (organizationSetting?.title ?? '') | translate | async }}</div>
          <div class="sub-heading">
            <span>{{ (organizationSetting?.description ?? '') | translate | async }} </span>
          </div>
        </div>
      </mat-expansion-panel-header>
      @if (organizationSetting.id === '1' && isParentPanelClosed) {
        <app-d2l-portal-settings
          [productSetting]="organizationSetting"
          [organizationId]="organizationId ?? ''"
          [userRoles]="userRoles"
          (orgSsoAuthChange)="setOrgSsoAuth($event)"></app-d2l-portal-settings>
      }
      @if (organizationSetting.id === '2' && isParentPanelClosed) {
        <app-orgainization-scorm-settings [organizationId]="organizationId ?? ''"></app-orgainization-scorm-settings>
      }
    </mat-expansion-panel>
  }
</mat-accordion>
