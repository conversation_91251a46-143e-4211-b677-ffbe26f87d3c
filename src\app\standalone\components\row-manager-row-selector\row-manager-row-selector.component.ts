import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { IRowLite } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Subject, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
  selector: 'app-row-manager-row-selector',
  templateUrl: './row-manager-row-selector.component.html',
  styleUrls: ['./row-manager-row-selector.component.scss'],
  standalone: true,
  imports: [IonicModule, AsyncPipe, TranslatePipe],
})
export class RowManagerRowSelectorComponent implements OnInit, OnDestroy {
  @Input() instanceId: string;
  @Output() rowSelected = new EventEmitter<string>();
  rows: IRowLite[];
  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.getInstanceRowManagerRows();
  }

  getInstanceRowManagerRows() {
    this.dataService
      .getInstanceRowManagerRows(this.instanceId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        this.rows = data;
      });
  }

  add(rowId: string) {
    this.rowSelected.next(rowId);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
