@if (rowContent.content && rowContent.content.length > 0) {
  <div class="player-view player-view-row" [id]="row.id">
    @if (!selectedUserId) {
      <div class="top-heading-container">
        @if (row?.title) {
          <div class="title-container">
            <h1 class="player-view-title">{{ row?.title | translate | async }}</h1>
          </div>
        }
      </div>
      @for (item of rowContent.content; track item) {
        <app-image-background-player-row
          [attr.data-page]="instance.title"
          [content]="item"
          [routeParams]="routeParams"
          [rowType]="row?.rowType?.name"
          [isPlayerSidePanel]="isPlayerSidePanel"
          [instanceDisplay]="row.instanceDisplay"
          [selectedUserId]="selectedUserId"
          [contentRowId]="row.id"
          (selectedChanged)="setSelected($event.event ?? '', item, $event.actionBw)">
        </app-image-background-player-row>
      }
      <div class="button-container">
        @if (showLoadMore) {
          <ion-button color="primary" fill="clear" size="small" (click)="loadMore()"> {{ 'Show more' | translate | async }} ></ion-button>
        }
      </div>
    }
    @if (selectedUserId && showGradingView) {
      <mat-expansion-panel [expanded]="expanded()" fill="clear">
        <mat-expansion-panel-header>
          <div class="top-heading-container">
            @if (row?.title) {
              <div class="title-container">
                <h1 class="player-view-title">{{ row?.title | translate | async }}</h1>
              </div>
            }
          </div>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          @for (item of rowContent.content; track item) {
            <app-image-background-player-row
              [attr.data-page]="instance.title"
              [content]="item"
              [routeParams]="routeParams"
              [rowType]="row?.rowType?.name"
              [isPlayerSidePanel]="isPlayerSidePanel"
              [instanceDisplay]="row.instanceDisplay"
              [selectedUserId]="selectedUserId"
              [contentRowId]="row.id"
              (selectedChanged)="setSelected($event.event ?? '', item, $event.actionBw)">
            </app-image-background-player-row>
          }
          <div class="button-container">
            @if (showLoadMore) {
              <ion-button color="primary" fill="clear" size="small" (click)="loadMore()">{{ 'Show more' | translate | async }} ></ion-button>
            }
          </div>
        </ng-template>
      </mat-expansion-panel>
    }
  </div>
}
