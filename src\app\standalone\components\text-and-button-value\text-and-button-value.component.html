<div class="parent-container">
  @if (textAndButton) {
    <div [ngClass]="['main-container', stylingDirection]">
      <ng-container [ngTemplateOutlet]="stylingDirection === 'Bottom' ? bottom : right" [ngTemplateOutletContext]="{ item: textAndButton }"> </ng-container>
    </div>

    <!-- Text Top and Button Bottom -->
    <ng-template #bottom let-item="item">
      <ng-container [ngTemplateOutlet]="text" [ngTemplateOutletContext]="{ item: item }"> </ng-container>
      <ng-container [ngTemplateOutlet]="button" [ngTemplateOutletContext]="{ item: item }"> </ng-container>
    </ng-template>

    <!-- Text Left and Button Right -->
    <ng-template #right let-item="item">
      <ng-container [ngTemplateOutlet]="text" [ngTemplateOutletContext]="{ item: item }"> </ng-container>
      <ng-container [ngTemplateOutlet]="button" [ngTemplateOutletContext]="{ item: item }"> </ng-container>
    </ng-template>

    <ng-template #button let-item="item">
      @if (textAndButton.buttonName && textAndButton.url) {
        <div [ngClass]="['button-container', stylingDirection]">
          <ion-button (click)="open()">{{ textAndButton.buttonName | translate | async }}</ion-button>
        </div>
      }
    </ng-template>

    <ng-template #text let-item="item">
      @if (textAndButton.heading || textAndButton.description) {
        <div [ngClass]="['text-main-container', stylingDirection]">
          <div [ngClass]="['text-container', stylingDirection]">
            @if (textAndButton.heading) {
              <span class="heading" [ngClass]="{ darkText: item.darkText }">{{ textAndButton.heading | translate | async }}</span>
            }
            @if (textAndButton.description) {
              <div [class]="stylingDirection" [ngClass]="{ paragraph: true, darkText: item.darkText }">
                <quill-view
                  [content]="
                    textAndButton.description === ''
                      ? (this.instanceComponent?.component?.templateField?.defaultText | translate | async)
                      : (textAndButton.description | parsePipe: instanceId | async | translate | async)
                  "
                  format="html"
                  theme="snow"></quill-view>
              </div>
            }
          </div>
        </div>
      }
    </ng-template>
  }
</div>
