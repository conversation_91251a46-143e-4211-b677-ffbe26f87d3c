import { Component, EventEmitter, Input, Output } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { Modal<PERSON>ontroller } from '@ionic/angular/standalone';

@Component({
    selector: 'app-success-message',
    imports: [IonicModule, AsyncPipe, TranslatePipe],
    providers: [ModalController],
    templateUrl: './success-message.component.html',
    styleUrl: './success-message.component.scss'
})
export class SuccessMessageComponent {
  @Input() itemName = 'Class';
  @Input() typeName = 'Class';
  @Input() userHeight = 75;
  @Output() closeClicked = new EventEmitter<string>();

  backgroundColor = '#292929';

  constructor() {}

  close(location: string = 'assignment') {
    this.closeClicked.next(location);
  }
}
