import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { IInstance } from '@app/core/contracts/contract';
import { IonicModule, PopoverController } from '@ionic/angular';
import { Subject } from 'rxjs';
import { InstanceCardSelectModalComponent } from '../instance-card-select-modal/instance-card-select-modal.component';
import { InstanceCardComponent } from '../instance-card/instance-card.component';

@Component({
    selector: 'app-class-selection-confirmation',
    imports: [IonicModule, FormsModule, InstanceCardComponent, AsyncPipe, TranslatePipe],
    templateUrl: './class-selection-confirmation.component.html',
    styleUrl: './class-selection-confirmation.component.scss'
})
export class ClassSelectionConfirmationComponent {
  @Input() instanceId: string;
  @Input() height: number = 5;
  @Input() instances: IInstance[];
  @Input() selectedInstance: IInstance;
  @Output() instanceSelected = new EventEmitter<string>();
  @Output() createClicked = new EventEmitter();
  @Output() closeClicked = new EventEmitter();

  lastModified?: IInstance[];
  query: string;
  featureTypeSelectedId: string | undefined;
  backgroundColor = '#292929';
  isInstanceModalOpen = false;
  isModalOpen = false;

  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private popOver: PopoverController) {}

  async selectInstanceClicked(event: any) {
    const popover = await this.popOver.create({
      component: InstanceCardSelectModalComponent,
      cssClass: 'add-search-modal no-backdrop-popover',
      componentProps: { instances: this.instances },
      event: event,
      side: 'bottom',
    });
    this.isModalOpen = true;

    popover.onDidDismiss().then((result: any) => {
      this.isModalOpen = false;
      if (result.data) {
        this.instanceId = result.data.id;
      }
    });

    await popover.present();
  }

  close() {
    this.closeClicked.next('');
  }

  assign(instance: IInstance) {
    this.instanceSelected.next(instance.id);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
