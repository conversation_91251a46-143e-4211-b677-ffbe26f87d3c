<div class="styling-container">
  <p class="styling-heading">{{ 'Text Alignment' | translate | async }}</p>
  <div class="styles-container">
    <div class="text-container" [ngClass]="isSelected('Left') ? 'selected' : null" (click)="checkboxChanged('Left')">
      <span>{{ 'Left' | translate | async }}</span>
    </div>
    <div class="text-container" [ngClass]="isSelected('Center') ? 'selected' : null" (click)="checkboxChanged('Center')">
      <span>{{ 'Center' | translate | async }}</span>
    </div>
    <div class="text-container" [ngClass]="isSelected('Right') ? 'selected' : null" (click)="checkboxChanged('Right')">
      <span>{{ 'Right' | translate | async }}</span>
    </div>
  </div>
</div>
