@if ($asset | async; as asset) {
  <div class="parent-container">
    <ion-grid>
      <ion-row class="row-container">
        <ion-col size="8" style="display: flex; justify-content: flex-start">
          <div style="display: flex; flex-direction: column; justify-content: center">
            <div class="description-container">
              <ion-label>{{ label | translate | async }}</ion-label>
              <ion-label class="asset-name-label">{{ asset?.name }}</ion-label>
            </div>
            <div>
              <div class="file-info">
                <ion-label class="asset-name-label">{{ asset.size | fileSize }}</ion-label>
              </div>
            </div>
          </div>
        </ion-col>
        <ion-col size="4" style="display: flex; justify-content: flex-end">
          @if (!isDisabled) {
            <div class="download-block-container">
              @if (layoutService.currentScreenSize === 'lg') {
                <ion-button fill="clear" (click)="downloadAsset(asset.name)"><mat-icon svgIcon="download"></mat-icon></ion-button>
              }
              @if (layoutService.currentScreenSize === 'xs') {
                <ion-button fill="clear" (click)="downloadAsset(asset.name)">
                  <ion-icon name="download-outline"></ion-icon>
                </ion-button>
              }
            </div>
          }
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>
}
