import { Ng<PERSON><PERSON>, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { Component, Input } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { MatDivider } from '@angular/material/divider';
import { MatIcon } from '@angular/material/icon';
import { GlobalToastService } from '@app/core/services/global-toast.service';
import { IUserDomain } from '@app/core/contracts/contract';
import { IonicModule, ModalController } from '@ionic/angular';

@Component({
    selector: 'app-join-dialog',
    templateUrl: './join-dialog.component.html',
    styleUrls: ['./join-dialog.component.scss'],
    imports: [IonicModule, MatIcon, FormsModule, MatDivider, NgClass, AsyncPipe, TranslatePipe]
})
export class JoinDialogComponent {
  @Input() userDomain: IUserDomain;
  code: string;
  selectedOrgId: string | undefined;

  constructor(
    private modalController: ModalController,
    private globalToast: GlobalToastService
  ) {}

  onClose() {
    this.modalController.dismiss();
  }

  orgSelected(orgId: string) {
    this.selectedOrgId = orgId;
    if (!this.userDomain.organizations) {
      this.globalToast.presentNotificationToast(`No Organizations linked to this User's domain: ${this.userDomain.domain}`, true);
      throw new Error(`No Organizations linked to this User's domain: ${this.userDomain.domain}`);
    }
    this.userDomain.organizations = this.userDomain?.organizations.map(org => {
      return { ...org, selected: false };
    });
    this.userDomain.organizations = this.userDomain?.organizations.map(org => {
      if (org.id === orgId) {
        return { ...org, selected: true };
      }
      return org;
    });
  }

  onFinish() {
    if (!this.userDomain.organizations) {
      this.globalToast.presentNotificationToast(`No Organizations linked to this User's domain: ${this.userDomain.domain}`);
      throw new Error(`No Organizations linked to this User's domain: ${this.userDomain.domain}`);
    }
    const data = {
      organization: {
        selectedOrgId: this.selectedOrgId,
        orgName: this.userDomain.organizations.find(x => x.id === this.selectedOrgId)?.name,
        domainRoleId: this.userDomain.organizations.find(x => x.id === this.selectedOrgId)?.domainRoleId,
      },
      code: this.code,
      domain: this.userDomain?.domain,
    };
    this.modalController.dismiss(data);
  }
}
