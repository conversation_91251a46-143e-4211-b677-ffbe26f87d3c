import { AsyncPipe, NgClass } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { Component, forwardRef, Input } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';
import { MatTooltip } from '@angular/material/tooltip';
import { IonicModule } from '@ionic/angular';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';

@Component({
    selector: 'app-text-area-input-control',
    templateUrl: './text-area-input-control.component.html',
    styleUrls: ['./text-area-input-control.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => TextAreaInputControlComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => TextAreaInputControlComponent),
        },
    ],
    imports: [IonicModule, NgClass, MatTooltip, AsyncPipe, TranslatePipe]
})
export class TextAreaInputControlComponent extends BaseControlComponent {
  @Input() toolTip: string;
  @Input() placeHolder: string;
  @Input() override label: string;
  @Input() override disabled = false;
  @Input() backgroundColor = '#181818';
  @Input() identifierText: string;
  @Input() noPadding = false;
  @Input() noBorder = false;
  @Input() sidePanelPadding = false;
  @Input() showSelected = false;

  constructor() {
    super();
  }
}
