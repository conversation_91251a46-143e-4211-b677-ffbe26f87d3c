import { Component, Input } from '@angular/core';
import { IonicModule, ModalController } from '@ionic/angular';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

import { MatIcon } from '@angular/material/icon';

@Component({
    selector: 'app-image-view-modal',
    templateUrl: './image-view-modal.component.html',
    styleUrls: ['./image-view-modal.component.scss'],
    imports: [IonicModule, MatIcon, AsyncPipe, TranslatePipe]
})
export class ImageViewModalComponent {
  @Input() imageUrl: string;
  @Input() caption: string;

  constructor(private modalController: ModalController) {}

  close() {
    this.modalController.dismiss();
  }
}
