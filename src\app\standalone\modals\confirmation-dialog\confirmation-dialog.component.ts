import { Component, Input } from '@angular/core';
import { IonicModule, ModalController } from '@ionic/angular';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-confirmation-dialog',
    templateUrl: './confirmation-dialog.component.html',
    styleUrls: ['./confirmation-dialog.component.scss'],
    imports: [IonicModule, AsyncPipe, TranslatePipe]
})
export class ConfirmationDialogComponent {
  @Input() headerText: string;
  @Input() bodyText: string;
  @Input() buttonText: string;
  @Input() cancelButtonText = 'Cancel';
  @Input() height = 18;

  constructor(private modalCtrl: ModalController) {}

  cancel() {
    return this.modalCtrl.dismiss(null, 'cancel');
  }

  confirm() {
    return this.modalCtrl.dismiss(null, 'confirm');
  }
}
