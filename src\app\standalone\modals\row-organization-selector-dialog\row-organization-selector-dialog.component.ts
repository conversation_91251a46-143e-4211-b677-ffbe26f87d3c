import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { UntypedFormGroup, UntypedFormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IOrganizationSearch, SelectedOrganizationIn } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { IonicModule, ModalController } from '@ionic/angular';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { Subject, takeUntil, debounceTime, Observable } from 'rxjs';

@Component({
    selector: 'app-row-organization-selector-dialog',
    templateUrl: './row-organization-selector-dialog.component.html',
    styleUrls: ['./row-organization-selector-dialog.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, AsyncPipe, TranslatePipe]
})
export class RowOrganizationSelectorDialogComponent implements OnInit, OnDestroy {
  @Input() multiSelect: boolean;
  @Input() formControlIn: UntypedFormControl;
  searchForm: UntypedFormGroup;
  searchValue: UntypedFormControl;
  showAddOrgs = false;

  currentAmount = 0;
  getAmount = 25;
  moreResults = false;

  orgs: IOrganizationSearch[] = [];
  selectedOrgIds: SelectedOrganizationIn[] = [];
  originalNetworkOrgs: IOrganizationSearch[] = [];
  noOrganizationsInTable: boolean;
  selectionChanged = false;

  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(
    private modalController: ModalController,
    private dataService: DataService
  ) {}

  ngOnInit() {
    this.setSelectedOrgIds();
    this.createFormControls();
    this.createForm();
    this.setOrgs(false);
  }

  setSelectedOrgIds() {
    const ids = this.formControlIn.value as string[];
    if (!ids || ids.includes('')) {
      return;
    }

    this.selectedOrgIds = ids.map(id => ({ id }));
  }

  setSelectedOrgs() {
    if (this.selectedOrgIds.length > 0) {
      this.orgs.map(x => {
        if (this.selectedOrgIds?.some(y => y.id === x.id)) {
          x.selected = true;
        }
      });
    } else if (!this.showAddOrgs) {
      this.noOrganizationsInTable = true;
    }
  }

  createFormControls() {
    this.searchValue = new UntypedFormControl('');
  }

  createForm() {
    this.searchForm = new UntypedFormGroup({
      searchValue: this.searchValue,
    });
  }

  searchRepoValue() {
    this.currentAmount = 0;
    this.setOrgs(false);
  }

  setOrgs(loadMore: boolean) {
    this.getOrgs(loadMore)
      .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
      .subscribe((orgs: IOrganizationSearch[]) => {
        if (orgs) {
          //LoadMoreData
          if (!loadMore) {
            this.noOrganizationsInTable = orgs.length === 0;
            this.orgs = orgs;
            this.currentAmount += orgs.length;
            this.setSelectedOrgs();
          } else {
            orgs.forEach(networkOrg => {
              this.orgs = [...this.orgs, networkOrg];
            });
            this.currentAmount += orgs.length;
          }

          if ((!this.showAddOrgs && this.selectedOrgIds.length == 0) || orgs.length < this.getAmount) {
            this.moreResults = false;
          } else {
            this.moreResults = true;
          }
        } else {
          this.orgs = [];
        }
      });
  }

  getOrgs(loadMore: boolean): Observable<IOrganizationSearch[]> {
    const currentAmount = loadMore ? this.currentAmount : 0;
    const getAmount = loadMore ? this.getAmount : this.currentAmount !== 0 ? this.currentAmount : this.getAmount;
    return this.dataService.getAllOrganizations(currentAmount, getAmount, this.searchValue.value, !this.showAddOrgs ? this.selectedOrgIds : undefined);
  }

  addOrgToList(event: any, selectedOrg: IOrganizationSearch) {
    if (event.detail.checked) {
      selectedOrg.selected = true;
      this.noOrganizationsInTable = false;
      this.selectedOrgIds.push({ id: selectedOrg.id });
    } else {
      const index = this.selectedOrgIds.findIndex(x => x.id === selectedOrg.id);
      selectedOrg.selected = true;
      this.selectedOrgIds.splice(index, 1);
      if (!this.showAddOrgs) {
        this.orgs.splice(index, 1);
      }
      if (this.selectedOrgIds.length == 0) {
        this.noOrganizationsInTable = true;
      }
    }
    this.selectionChanged = true;
  }
  addOrg(event: any, selectedOrg: IOrganizationSearch) {
    if (!this.multiSelect) {
      this.orgs.map(org => (org.selected = false));
      this.selectedOrgIds = [];
      this.noOrganizationsInTable = true;
    }

    if (event.detail.checked) {
      this.noOrganizationsInTable = false;
      selectedOrg.selected = true;
      this.selectedOrgIds.push({ id: selectedOrg.id });
    }
    this.selectionChanged = true;
  }

  close() {
    this.modalController.dismiss();
  }

  add() {
    const organizationIds: string[] = this.selectedOrgIds.map(org => org.id);
    this.modalController.dismiss({ changed: true, organizationIds: organizationIds });
  }

  toggleAddOrgs() {
    this.showAddOrgs = !this.showAddOrgs;
    this.currentAmount = 0;
    this.setOrgs(false);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
