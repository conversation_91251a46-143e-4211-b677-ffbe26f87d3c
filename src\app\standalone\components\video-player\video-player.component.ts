import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, As<PERSON><PERSON>ip<PERSON> } from '@angular/common';
import { AfterViewInit, Component, DoCheck, Input, OnDestroy } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { IAsset, IAssetCaptionLanguages, IComponent, IEngagementIn, IInstanceSectionComponent } from '@app/core/contracts/contract';
import { EngagementTypes } from '@app/core/enums/engagment-types.enum';
import { DataService } from '@app/core/services/data-service';
import { Events } from '@app/core/services/events-service';
import { InstanceService } from '@app/core/services/instance-service';
import { LayoutService } from '@app/core/services/layout-service';
import { ImageViewerValueComponent } from '@app/standalone/components/image-viewer-value/image-viewer-value.component';
import { environment } from '@env/environment';
import { IonicModule } from '@ionic/angular';
import { VgBufferingModule } from '@videogular/ngx-videogular/buffering';
import { VgControlsModule } from '@videogular/ngx-videogular/controls';
import { VgCoreModule } from '@videogular/ngx-videogular/core';
import { VgOverlayPlayModule } from '@videogular/ngx-videogular/overlay-play';
import { VgStreamingModule } from '@videogular/ngx-videogular/streaming';
import { Subject, forkJoin, take, takeUntil } from 'rxjs';
import { AuthoringHeaderComponent } from '../authoring-header/authoring-header.component';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
  selector: 'app-video-player',
  templateUrl: './video-player.component.html',
  styleUrls: ['./video-player.component.scss'],
  imports: [IonicModule, AuthoringHeaderComponent, NgStyle, NgClass, VgCoreModule, VgOverlayPlayModule, VgBufferingModule, VgControlsModule, VgStreamingModule, ImageViewerValueComponent, AsyncPipe, TranslatePipe],
})
export class VideoPlayerComponent implements AfterViewInit, OnDestroy, DoCheck {
  @Input() assetId: string | undefined;
  @Input() instanceId: string;
  @Input() instanceSectionComponent?: IInstanceSectionComponent;
  @Input() featureName: string | undefined;
  @Input() url: string | undefined;
  @Input() controls: boolean;
  @Input() mini: boolean;
  @Input() autoplay: boolean;
  @Input() poster: string;
  @Input() width: number;
  @Input() height: number;
  @Input() fluid: boolean;
  @Input() systemPropertyValue: string | null;
  @Input() component: IComponent;
  @Input() builderPreviewView = false;

  componentDestroyed$: Subject<boolean> = new Subject();
  src: null | string | SafeResourceUrl = null;
  currentStream: null | string = null;
  asset: IAsset;
  ready = false;
  api: any;
  dashBitrates: any;
  valueIn: string;
  progress: number;
  captionLang: IAssetCaptionLanguages[];
  trackCompletion = true;
  oldValue: any;

  constructor(
    private dataService: DataService,
    private sanitizer: DomSanitizer,
    private eventsService: Events,
    public layoutService: LayoutService,
    public instanceService: InstanceService
  ) {}

  get isCustomStyle() {
    return this.featureName ? this.featureName === 'Organization Manager' : false;
  }

  ngDoCheck(): void {
    if (this.instanceSectionComponent?.value !== this.oldValue) {
      this.oldValue = this.instanceSectionComponent?.value;

      this.src = null;
      this.valueIn = null;

      this.setValueIn();
      this.init();
    }
  }

  ngAfterViewInit(): void {
    this.setValueIn();
    this.init();
    this.eventsService.subscribe('viewLeft', () => {
      if (this.api) {
        this.api.pause();
      }
    });
  }

  init() {
    if (this.valueIn && this.valueIn.length > 0) {
      forkJoin({
        asset: this.dataService.getAssetDetailsById(this.valueIn).pipe(takeUntil(this.componentDestroyed$)),
        lang: this.dataService.getAssetCaptionLanguages(this.valueIn).pipe(takeUntil(this.componentDestroyed$)),
      })
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(({ asset, lang }) => {
          if (!asset.streamingLocator) {
            if (asset.mediaUploadType === 'Embed' && asset.embedCode && asset.embedCode !== '') {
              this.src = this.sanitizer.bypassSecurityTrustResourceUrl(asset.embedCode);
            } else if (asset.mediaUploadType === 'Url' && asset.urlUpload && asset.urlUpload !== '') {
              this.src = this.sanitizer.bypassSecurityTrustResourceUrl(asset.urlUpload);
            } else {
              const cacheBuster = asset.modifiedDate ? `?v=${asset.modifiedDate}` : `?v=${Date.now()}`;
              this.src = this.sanitizer.bypassSecurityTrustResourceUrl(`${environment.contentUrl}asset/${asset.id}/content${cacheBuster}`);
            }
            this.currentStream = null;
          } else {
            this.src = null;
            this.currentStream = asset.streamingLocator;
          }

          lang = lang.map(x => ({ ...x, vttUrl: `${environment.contentUrl}asset/${asset.id}/caption/${x.languageCode}` }) as IAssetCaptionLanguages);
          this.asset = asset;
          this.captionLang = lang;
          this.ready = true;
        });
    }
  }

  setValueIn() {
    if (this.instanceSectionComponent?.value && !this.systemPropertyValue) {
      this.valueIn = this.instanceSectionComponent?.value;
    } else if (this.systemPropertyValue) {
      this.valueIn = this.systemPropertyValue;
    } else if (this.assetId) {
      this.valueIn = this.assetId;
    }
  }

  onPlayerReady(api: any) {
    this.api = api;

    this.api
      .getDefaultMedia()
      .subscriptions.timeUpdate.pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        const prop = this.api.getDefaultMedia().time;
        this.progress = (prop.current / prop.total) * 100;
        if (this.component?.templateField?.percentageToComplete && this.component?.templateField?.percentageToComplete !== 0 && this.progress >= this.component?.templateField?.percentageToComplete) {
          this.eventsService.publish('videoDone', this.instanceId);
          this.completeInstanceComponent();
        }
      });

    if (this.instanceSectionComponent) {
      this.api
        .getDefaultMedia()
        .subscriptions.playing.pipe(takeUntil(this.componentDestroyed$), take(1))
        .subscribe(() => {
          this.addInstanceSectionComponentEngagement();
        });
    }
  }

  completeInstanceComponent() {
    if (this.trackCompletion) {
      this.trackCompletion = false;
      this.dataService
        .addInstanceSectionComponentCompletion(this.instanceSectionComponent?.id ?? '', this.instanceId ?? '', Math.round(this.progress))
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          this.eventsService.publish('instanceInProgress', this.instanceId);
          this.eventsService.publish('instanceSectionComponentCompleted', this.instanceSectionComponent?.id);
          this.eventsService.publish('checkCompletion');
          if (this.instanceSectionComponent) {
            this.instanceSectionComponent.completed = true;
          }
          this.addInstanceSectionComponentEngagement();
          this.eventsService.publish('videoDone', this.instanceId);
        });
    }
  }

  addInstanceSectionComponentEngagement() {
    this.dataService
      .addInstanceSectionComponentEngagement({
        instanceId: this.instanceId,
        instanceSectionComponentId: this.instanceSectionComponent?.id,
        engagementType: EngagementTypes.Play,
        percentageValue: this.progress !== undefined ? Math.round(this.progress) : 0,
        nominalValue: 1,
      } as IEngagementIn)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe();
  }

  setBitRat(ev: any) {
    const updatedBitrates = ev
      .map((e: any) => {
        switch (e.qualityIndex) {
          case 0:
            e.label = 'Auto';
            break;
          case 1:
            e.label = 'Low';
            break;
          case 3:
            e.label = 'Standard';
            break;
          case 5:
            e.label = 'High';
            break;
          case 7:
            e.label = 'HD';
            break;
          case 8:
            e.label = 'Ultra HD';
            break;
          default:
            e.label = '';
        }
        return e;
      })
      .filter((e: any) => e.label !== '');

    this.dashBitrates = updatedBitrates;
  }

  ngOnDestroy() {
    this.eventsService.unsubscribe('viewLeft');
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
