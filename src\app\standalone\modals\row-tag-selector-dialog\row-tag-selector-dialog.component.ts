import { Component, Inject, OnD<PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DataService } from '@app/core/services/data-service';
import { Observable, Subject, takeUntil } from 'rxjs';
import { IRowTagIn, ITag } from '@app/core/contracts/contract';
import { UntypedFormControl, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { map } from 'rxjs/operators';
import { IonicModule } from '@ionic/angular';
import { NgClass, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-row-tag-selector-dialog',
    templateUrl: './row-tag-selector-dialog.component.html',
    styleUrl: './row-tag-selector-dialog.component.scss',
    encapsulation: ViewEncapsulation.None,
    imports: [IonicModule, FormsModule, ReactiveFormsModule, NgClass, AsyncPipe, TranslatePipe]
})
export class RowTagSelectorDialogComponent implements OnInit, OnDestroy {
  tags$: Observable<ITag[]>;
  page = 1;
  selectedIndex: number | null;
  parentIdList: string[] = [];
  componentDestroyed$: Subject<boolean> = new Subject();
  searchForm: UntypedFormGroup;
  searchControl: UntypedFormControl;

  constructor(
    public dialogRef: MatDialogRef<RowTagSelectorDialogComponent>,
    private dataService: DataService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    dialogRef.disableClose = true;
    dialogRef.backdropClick().subscribe(() => {
      dialogRef.close();
    });
  }

  ngOnInit() {
    this.loadData();
    this.createFormControls();
    this.createForm();
  }

  createFormControls() {
    this.searchControl = new UntypedFormControl('');
  }

  createForm() {
    this.searchForm = new UntypedFormGroup({
      searchControl: this.searchControl,
    });
  }

  loadData() {
    this.tags$ = this.dataService.getRowTagChildren(this.data?.rowId, this.data?.tagId, '').pipe(
      takeUntil(this.componentDestroyed$),
      map(tags => {
        this.page = tags[0]?.treeLevel ?? 1;
        return tags;
      })
    );
  }

  onFinish() {
    const isDone = true;
    this.tags$.subscribe((tags: ITag[]) => {
      if (tags.length > 0) {
        this.page = tags[0].treeLevel;
      }
    });

    this.dialogRef.close(isDone);
  }

  searchResults() {
    this.tags$ = this.dataService.getRowTagChildren(this.data?.rowId, this.data?.tagId, this.searchControl.value).pipe(takeUntil(this.componentDestroyed$));
  }

  setPropertyTag(tagId: string) {
    this.data.tagId = tagId;
  }

  setRow(_index: number, tag: ITag) {
    this.selectedIndex = _index;
    this.setPropertyTag(tag.id);
  }

  nextTag(tag: ITag) {
    this.searchControl.setValue('');
    this.searchForm.reset();
    this.data.tagId = tag.id;
    this.tagHasChildren().subscribe(res => {
      if (res && this.page <= tag.treeLevel) {
        this.page++;
        this.loadData();
      }
    });

    if (tag.parentId != null) {
      this.parentIdList.push(tag.parentId);
    }
    this.data.tagId = '';
    this.selectedIndex = null;
  }

  tagHasChildren(): Observable<boolean> {
    return this.dataService.tagHasChildren(this.data.tagId);
  }

  tagSelection(event: any, tag: ITag) {
    const rowTag: IRowTagIn = {
      tagId: tag.id,
      rowId: this.data.rowId,
    };
    const hasTag = !tag.hasRowTags;
    if (hasTag) {
      this.dataService
        .addRowTag(rowTag)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          tag.hasRowTags = hasTag;
        });
    } else {
      this.dataService
        .removeRowTag(this.data.rowId, tag.id)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          tag.hasRowTags = hasTag;
        });
    }
  }

  onBack() {
    if (this.parentIdList.length > 0) {
      const firstIn = this.parentIdList.pop();
      this.data.tagId = firstIn;
      this.loadData();
      this.page--;
    } else {
      this.dialogRef.close();
    }
    this.searchForm.reset();
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
