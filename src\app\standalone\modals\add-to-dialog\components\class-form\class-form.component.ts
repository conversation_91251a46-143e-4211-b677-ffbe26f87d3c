import { Async<PERSON><PERSON><PERSON>, DatePipe, LowerCasePipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ReactiveFormsModule, UntypedFormControl, UntypedFormGroup, Validators } from '@angular/forms';
import { IInstance, IInstanceIn, ITag } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { FileUploadControlComponent } from '@app/standalone/components/file-upload-control/file-upload-control.component';
import { SelectOptionControlComponent } from '@app/standalone/components/select-option-control/select-option-control.component';
import { TagSelectOptionControlComponent } from '@app/standalone/components/tag-select-option-control/tag-select-option-control.component';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { IonicModule } from '@ionic/angular';
import { map, Observable, Subject, takeUntil } from 'rxjs';
import { TextAreaInputControlComponent } from '../../../../components/text-area-input-control/text-area-input-control.component';
import { format } from 'date-fns';
import { IonPopover } from '@ionic/angular/common';

const predefinedGradeOrder = ['Kindergarten', 'Grade 1', 'Grade 2', 'Grade 3', 'Grade 4', 'Grade 5', 'Grade 6', 'Grade 7', 'Grade 8', 'Grade 9', 'Grade 10', 'Grade 11', 'Grade 12', 'Postsecondary'];
@Component({
    selector: 'app-class-form',
    imports: [
        IonicModule,
        AsyncPipe,
        ReactiveFormsModule,
        TextInputControlComponent,
        TextAreaInputControlComponent,
        SelectOptionControlComponent,
        FileUploadControlComponent,
        TagSelectOptionControlComponent,
        LowerCasePipe,
        DatePipe,
        TranslatePipe,
    ],
    providers: [BuilderService],
    templateUrl: './class-form.component.html',
    styleUrl: './class-form.component.scss'
})
export class ClassFormComponent {
  @Input() instance: IInstance;
  @Input() height: number = 60;
  @Input() orgId: string;
  @Input() featureTypeName: string;
  @Input() header = 'Class';
  @Input() loading = false;
  @Output() createClicked = new EventEmitter<IInstanceIn>();
  @Output() closeClicked = new EventEmitter();

  backgroundColor = '#292929';
  classForm: UntypedFormGroup;
  grades$: Observable<KeyValue[]>;
  users$: Observable<KeyValue[]>;
  educatorsExist = false;
  featureId: string;
  instanceTags: ITag[];

  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private dataService: DataService) {}

  ngOnInit() {
    if (!this.instance) {
      this.createForm();
    } else {
      this.dataService
        .getInstanceTagsByParentId(this.instance.id, '8FDCB339-5853-4FA7-B88E-9C53156CE73E')
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((tags: ITag[]) => {
          this.instanceTags = tags;
          this.createForm();
        });
    }

    this.dataService
      .getFeatureByTypeName(this.featureTypeName)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        this.featureId = data.id;
      });

    if (this.featureTypeName === 'Modifiable Learning Container Pages') {
      this.loading = true;
    } else {
      this.loading = false;
    }
    //Grade.
    this.grades$ = this.dataService.getTagChildren('8FDCB339-5853-4FA7-B88E-9C53156CE73E').pipe(
      map(tags => {
        return tags
          .filter(x => predefinedGradeOrder.indexOf(x.name) !== -1)
          .sort((a, b) => {
            return predefinedGradeOrder.indexOf(a.name) - predefinedGradeOrder.indexOf(b.name);
          })
          .map(tag => ({ id: tag.id, parentId: tag.parentId, value: tag.name, level: tag.treeLevel, hasChildren: tag.inverseParent ?? false }) as KeyValue);
      })
    );

    // Users
    this.users$ = this.dataService.getOrganizationEducators(this.orgId).pipe(
      map(users => {
        if (users && users?.length > 0) {
          this.educatorsExist = true;
          this.classForm.controls['educatorId'].setValidators(this.featureTypeName === 'Modifiable Learning Container Pages' ? Validators.required : null);
        } else {
          this.educatorsExist = false;
        }
        this.loading = false;
        return users?.map(x => ({ id: x.id, value: x.name }) as KeyValue);
      })
    );
  }

  getFormControl(controlName: string) {
    return this.classForm.controls[controlName];
  }

  createForm() {
    this.classForm = new UntypedFormGroup({
      name: new UntypedFormControl(this.instance?.title ?? '', Validators.required),
      description: new UntypedFormControl(this.instance?.description ?? ''),
      gradeId: new UntypedFormControl(
        this.instanceTags != null && this.instanceTags.length > 0 ? this.instanceTags[0].id : '',
        this.featureTypeName === 'Modifiable Learning Container Pages' ? Validators.required : null
      ),
      educatorId: new UntypedFormControl(this.instance?.educatorId ?? ''),
      thumbnailId: new UntypedFormControl(this.instance?.iconAssetId ?? ''),
      dueDate: new UntypedFormControl(this.instance?.dueDate ? format(new Date(this.instance?.dueDate ?? ''), 'yyyy-MM-dd') : null),
    });
  }

  saveClicked() {
    let newInstance = {
      title: this.classForm.controls['name'].value,
      description: this.classForm.controls['description'].value,
      iconAssetId: this.classForm.controls['thumbnailId'].value && this.classForm.controls['thumbnailId'].value !== '' ? this.classForm.controls['thumbnailId'].value : null,
      gradeId: this.classForm.controls['gradeId'].value && this.classForm.controls['gradeId'].value !== '' ? this.classForm.controls['gradeId'].value : null,
      educatorId: this.classForm.controls['educatorId'].value && this.classForm.controls['educatorId'].value !== '' ? this.classForm.controls['educatorId'].value : null,
      featureId: this.featureId,
      status: 'public',
      dueDate: this.classForm.controls.dueDate.value ? new Date(this.classForm.controls.dueDate.value).getTime() : undefined,
      organizationId: this.orgId,
    } as IInstanceIn;

    if (this.instance) {
      newInstance = {
        ...this.instance,
        title: this.classForm.controls['name'].value,
        description: this.classForm.controls['description'].value,
        iconAssetId: this.classForm.controls['thumbnailId'].value && this.classForm.controls['thumbnailId'].value !== '' ? this.classForm.controls['thumbnailId'].value : null,
        gradeId: this.classForm.controls['gradeId'].value && this.classForm.controls['gradeId'].value !== '' ? this.classForm.controls['gradeId'].value : null,
        educatorId: this.classForm.controls['educatorId'].value && this.classForm.controls['educatorId'].value !== '' ? this.classForm.controls['educatorId'].value : null,
        featureId: this.featureId,
        status: 'public',
        dueDate: this.classForm.controls.dueDate.value ? new Date(this.classForm.controls.dueDate.value).getTime() : undefined,
        organizationId: this.orgId,
      };
    }

    this.createClicked.next(newInstance);
  }

  onDateSelected(popover: IonPopover) {
    // Close the popover
    popover.dismiss();
  }

  cancel() {
    this.closeClicked.next('');
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
