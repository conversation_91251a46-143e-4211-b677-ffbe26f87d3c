<div id="parent" class="parent-container" [ngClass]="{ moveOverBanner: moveOverBanner === true && canMoveOverBanner === true }">
  @if (title) {
    <ion-row class="inner-container" [ngClass]="{ 'padding-top': viewType !== viewTypes.Builder }">
      @if (iconUrl && centerContent !== true) {
        <ion-col class="img-col" size="*">
          <img class="icon" [ngClass]="{ 'icon-org': organizationManager }" [src]="iconUrl" alt="icon" />
        </ion-col>
      }
      <ion-col class="header-col" [ngClass]="{ 'center-header': centerContent === true }" [size]="centerContent === true ? 12 : 9">
        @if (title) {
          <div class="title-container">
            <span class="title" [ngStyle]="{ 'font-size: 2.813em': instance?.feature?.isFullWidth === true || (viewType === viewTypes.Player && centerContent === true) }">
              @if (headingStyle === 'h1') {
                <h1>{{ title | parsePipe: linkId | async | translate | async }}</h1>
              }
              @if (headingStyle === 'h2') {
                <h2>{{ title | parsePipe: linkId | async | translate | async }}</h2>
              }
              @if (headingStyle === 'h3') {
                <h3>{{ title | parsePipe: linkId | async | translate | async }}</h3>
              }
              @if (headingStyle === 'h4') {
                <h4>{{ title | parsePipe: linkId | async | translate | async }}</h4>
              }
              @if (headingStyle === 'h5') {
                <h5>{{ title | parsePipe: linkId | async | translate | async }}</h5>
              }
              @if (headingStyle === 'h6') {
                <h6>{{ title | parsePipe: linkId | async | translate | async }}</h6>
              }
              @if (headingStyle === undefined || headingStyle === '') {
                {{ title | parsePipe: linkId | async | translate | async }}
              }
              @if (actionBw && actionBw === 1) {
                <ion-icon class="lock-icon" name="lock-closed-outline"></ion-icon>
              }
            </span>
          </div>
        }
        @if (userInstanceTracking?.isComplete) {
          <div class="sub-heading-container">
            <span [ngClass]="{ center: centerContent === true }" class="sub-heading">{{ 'You completed this on' | translate | async }} {{ userInstanceTracking?.eventDate | date }}. </span>
          </div>
        }
        <div>
          <app-dynamic-text-value
            [ngClass]="{ center: centerContent === true }"
            class="description-tag"
            [instanceId]="instance.id"
            [defaultValue]="instance?.feature?.instanceDescriptors ?? ''"></app-dynamic-text-value>
        </div>
        <div class="description-container">
          <span [ngClass]="{ center: centerContent === true }" class="description">{{ description ?? '' | parsePipe: instance?.id | async | translate | async }} </span>
        </div>
      </ion-col>
    </ion-row>
  } @else {
    <app-authoring-header [componentName]="'Listing Details'"></app-authoring-header>
  }
</div>
