<ion-grid>
  <ion-row class="view-options-row">
    <ion-col size="9"> </ion-col>
  </ion-row>
  <div class="table-container">
    <table #table class="table-grid" mat-table [dataSource]="dataSource" matSort #sort="matSort">
      <ng-container matColumnDef="checkBox">
        <th mat-header-cell *matHeaderCellDef>
          <mat-checkbox (change)="$event ? masterHeaderToggle() : null" [checked]="selection.hasValue() && selectAll()" [indeterminate]="selection.hasValue() && !selectAll()"> </mat-checkbox>
        </th>
        <td mat-cell *matCellDef="let element">
          <mat-checkbox (click)="$event.stopPropagation()" (change)="$event ? selection.toggle(element) : null" [checked]="selection.isSelected(element)"> </mat-checkbox>
        </td>
      </ng-container>
      <ng-container matColumnDef="name">
        <th mat-sort-header mat-header-cell *matHeaderCellDef>{{ 'NAME' | translate | async }}</th>
        <td mat-cell *matCellDef="let element">{{ element.name }}</td>
      </ng-container>
      <ng-container matColumnDef="source">
        <th mat-sort-header mat-header-cell *matHeaderCellDef>{{ 'SOURCE' | translate | async }}</th>
        <td mat-cell *matCellDef="let element">{{ element.source }}</td>
      </ng-container>
      <ng-container matColumnDef="category">
        <th mat-sort-header mat-header-cell *matHeaderCellDef>{{ 'CATEGORY' | translate | async }}</th>
        <td mat-cell *matCellDef="let element">{{ element.category }}</td>
      </ng-container>
      <ng-container matColumnDef="runtime">
        <th mat-sort-header mat-header-cell *matHeaderCellDef>{{ 'RUNTIME' | translate | async }}</th>
        <td mat-cell *matCellDef="let element">{{ element.runtime }}</td>
      </ng-container>
      <ng-container matColumnDef="status">
        <th mat-sort-header mat-header-cell *matHeaderCellDef>{{ 'FEEDBACK STATUS' | translate | async }}</th>
        <td mat-cell *matCellDef="let element">{{ element.status }}</td>
      </ng-container>
      <ng-container matColumnDef="date">
        <th mat-sort-header mat-header-cell *matHeaderCellDef>{{ 'PUBLISH DATE' | translate | async }}</th>
        <td mat-cell *matCellDef="let element">{{ element.date }}</td>
      </ng-container>
      <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns" style="cursor: pointer" (click)="openInstanceBuilder(row.id)"></tr>
    </table>
    <mat-paginator [length]="resultsLength" [pageSizeOptions]="[10, 25, 50]" showFirstLastButtons></mat-paginator>
  </div>
</ion-grid>
