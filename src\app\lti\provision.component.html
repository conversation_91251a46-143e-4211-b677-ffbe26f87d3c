<div class="join-code">
  <ion-grid>
    <ion-row class="ion-align-items-center">
      @if (loading) {
        <ion-col size="12">
          <div class="wait-message">
            <div class="loader">
              <img class="img" src="assets/images/EdgeFactor-EF_rings-2018-white_small_png.png" alt="edgefactor logo" />
            </div>
            <h2>{{ 'Welcome to EdgeFactor!' | translate | async }}</h2>
            <p>{{ 'We are currently creating your personalized learning experience. This may take a minute or two, so we appreciate your patience.' | translate | async }}</p>
            <p>
              {{ 'Your page will automatically refresh once registration is complete. If you encounter any issues or the process takes longer than expected, please' | translate | async
              }}<a href="https://app.edgefactor.com/help">contact support</a>
            </p>
            <p>{{ 'Thank you for joining EdgeFactor!' | translate | async }}</p>
            <p>
              <strong>{{ 'Please Hold Tight! Your Automatic Registration is in Progress...' | translate | async }}</strong>
            </p>
            <ion-spinner color="primary" name="dots"></ion-spinner>
          </div>
        </ion-col>
      }
      @if (!loading) {
        <ion-col size="12">
          <div class="wait-message">
            <div class="loader">
              <img class="img" src="assets/images/EdgeFactor-EF_rings-2018-white_small_png.png" alt="edgefactor logo" />
            </div>
            <h2>{{ 'Registration Successful!' | translate | async }}</h2>
            <p>{{ 'Before you go, here are a few things to look forward to:' | translate | async }}</p>
            <strong>{{ 'Engaging Learning Content:' | translate | async }}</strong
            ><br />
            <p>{{ 'Dive into a vast library of interactive and multimedia resources tailored to bring education to life.' | translate | async }}</p>
            <strong>{{ 'Real-World Application:' | translate | async }}</strong
            ><br />
            <p>{{ 'Discover how classroom theory connects with real-world careers and skills.' | translate | async }}</p>
            <strong>{{ 'Personalized Pathways:' | translate | async }}</strong> <br />
            <p>{{ 'Explore courses and tools designed to match your interests and advance your journey in learning and career development.' | translate | async }}</p>
            <button mat-raised-button color="primary" (click)="completeRegistration()">{{ 'Close' | translate | async }}</button>
          </div>
        </ion-col>
      }
    </ion-row>
  </ion-grid>
</div>
