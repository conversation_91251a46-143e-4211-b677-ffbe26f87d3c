@if (templateField) {
  <div
    [ngClass]="{
      'border-completed': instanceSectionComponent?.completed && instanceService.isScorm === false,
      'border-in-progress': inProgress && !instanceSectionComponent?.completed && instanceService.isScorm === false,
    }">
    <ng-container>
      @if ((instanceSectionComponent?.component?.templateField?.isBlockRequired || instanceSectionComponent?.completed || inProgress) && instanceService.isScorm === false) {
        <div class="completed-header-container">
          <ion-row>
            @if (instanceSectionComponent?.completed) {
              <ion-col>
                <span class="inner-completed">{{ 'COMPLETED' | translate | async }}</span>
              </ion-col>
            }
            @if (inProgress && !instanceSectionComponent?.completed) {
              <ion-col>
                <span class="inner-in-progress">{{ 'IN PROGRESS' | translate | async }}</span>
              </ion-col>
            }
            @if (instanceSectionComponent?.component?.templateField?.isBlockRequired) {
              <ion-col>
                <span class="inner-required">{{ 'REQUIRED' | translate | async }}</span>
              </ion-col>
            }
          </ion-row>
        </div>
      }
    </ng-container>
    <iframe
      id="gameFrame"
      [style.aspect-ratio]="templateField?.aspectRatio ?? 'auto'"
      [style.background-color]="'transparent'"
      allowtransparency="true"
      class="html5-object"
      type="application/signed-exchange;v=b3"
      frameBorder="0"
      allow="autoplay"
      [src]="src"
      onerror="logAssetError()"
      (error)="logAssetError()"></iframe>
  </div>
}
