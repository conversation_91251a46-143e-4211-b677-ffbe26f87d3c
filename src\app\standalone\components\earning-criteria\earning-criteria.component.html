<div class="parent-container">
  @if (!isCriteriaManagerView) {
    <ion-row>
      <ion-col size="*">
        <p class="title">{{ 'Earning Criteria' | translate | async }}</p>
      </ion-col>
    </ion-row>
  }

  <p class="subtitle">{{ 'Recipients must complete the earning criteria to earn this badge.' | translate | async }}</p>
  @for (criteria of earningCriteria; track criteria) {
    <ion-row>
      <ion-col class="icon" size="auto">
        <ion-icon size="large" name="clipboard-outline"></ion-icon>
      </ion-col>
      @if (criteria?.earningCriteriaType?.name === 'CompletePage') {
        <ion-col class="info" [innerHTML]="criteria?.earningCriteriaType?.earningText ?? '' | parsePipe: criteria?.earningCriteriaContent?.[0]?.refId | async | translate | async"></ion-col>
      }
      @if (criteria?.earningCriteriaType?.name !== 'CompletePage') {
        <ion-col class="info" [innerHTML]="criteria.earningCriteriaType.earningText ?? '' | parsePipe: instanceId | async | translate | async"></ion-col>
      }
    </ion-row>
  }
  @for (criteria of earningCriteria; track criteria) {
    <ion-row>
      @for (row of criteria.rowCriteria; track row) {
        <ion-row class="row">
          <ion-row [innerHTML]="row.earningText ?? '' | parsePipe: row.rowId | async | translate | async"></ion-row>
        </ion-row>
      }
    </ion-row>
  }
</div>
