<div class="parent-container">
  <ion-grid>
    @if (type !== 'Product Manager') {
      <ion-row class="view-options-row">
        <ion-col style="justify-content: flex-start" size="4">{{ 'Role:' | translate | async }} {{ userRoleName }}</ion-col>
        <ion-col size="8" class="end-col-buttons">
          <ion-button (click)="saveProductFeatureRoles()" color="warning">Save</ion-button>
        </ion-col>
      </ion-row>
    }
    @if (type === 'Product Manager') {
      <ion-row class="view-options-row">
        <ion-col style="justify-content: flex-start" size="9"> {{ 'OK, now let\'s add features to this product' | translate | async }} </ion-col>
        <ion-col size="3" class="end-col-buttons">
          <ion-button (click)="saveProductFeatureRoles()" color="warning">Save</ion-button>
          <ion-button (click)="addProductFeature()"> <mat-icon svgIcon="add"></mat-icon> {{ 'Add' | translate | async }} </ion-button>
        </ion-col>
      </ion-row>
    }
    <mat-accordion multi>
      @for (feature of productFeatures | orderBy: 'title'; track feature) {
        <mat-expansion-panel (opened)="getProductFeatureRolesById(feature.id)" #mep="matExpansionPanel">
          <mat-expansion-panel-header class="expansion-panel-header">
            <div class="inner-panel">
              <ion-row>
                <ion-col size="9">
                  <div class="heading">{{ (feature.title ?? '') | translate | async }}</div>
                  @if (feature.description) {
                    <div class="sub-heading">
                      <span>{{ (feature.description ?? '') | translate | async }}</span>
                    </div>
                  }
                </ion-col>
                @if (this.type === 'Product Manager') {
                  <ion-col size="3" class="dlt-button-col">
                    <ion-button (click)="removeFeature(feature); $event.stopPropagation()" color="primary">{{ 'Delete' | translate | async }}</ion-button>
                  </ion-col>
                }
              </ion-row>
            </div>
          </mat-expansion-panel-header>
          @if (mep.expanded === true) {
            <div class="content">
              <mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start" (selectedTabChange)="tabSelected($event, feature.id)">
                <mat-tab [label]="'FEATURE ACTIONS' | translate | async">
                  <ng-template matTabContent>
                    <ion-grid>
                      @for (role of feature.productFeatureRoles; track role) {
                        <ion-row class="action-selectors-container">
                          <ion-col size="3">
                            <span class="role-name">{{ (role.name ?? '') | translate | async }} {{ 'CAN:' | translate | async }}</span>
                          </ion-col>
                          @for (action of actions; track action) {
                            <ion-col class="action-checkbox" col-auto>
                              <mat-checkbox [checked]="isSelected(role, action.actionBw)" (change)="manageActionChange($event, action.actionBw, role, feature.id)"></mat-checkbox>
                              <span class="action-name">{{ (action.name ?? '') | translate | async }} </span>
                            </ion-col>
                          }
                        </ion-row>
                      }
                    </ion-grid>
                  </ng-template>
                </mat-tab>
                <mat-tab [label]="'FEATURE INSTANCES' | translate | async">
                  <ng-template matTabContent>
                    <div>
                      <form [formGroup]="searchForm">
                        <ion-grid style="padding: 10px">
                          <ion-row class="search-bar-row">
                            <ion-col>
                              <ion-searchbar
                                color="dark"
                                formControlName="searchControl"
                                (ionChange)="getProductFeatureInstances(feature.id, false)"
                                type="search"
                                [placeholder]="'Search' | translate | async"
                                showCancelButton="focus"
                                debounce="600">
                              </ion-searchbar>
                            </ion-col>
                          </ion-row>
                        </ion-grid>
                      </form>
                    </div>
                    @for (instance of productFeatureInstances; track instance) {
                      <ion-grid class="productfeatureinstances">
                        <ion-row class="instance-selectors-container">
                          <ion-col class="instance-checkbox">
                            <mat-checkbox [checked]="instance.isActive" (change)="updateProductFeatureInstance($event, feature.id, instance.id)"></mat-checkbox>
                          </ion-col>
                          <ion-col>
                            <div class="inner-panel" size="12">
                              <div class="instance-heading">{{ (instance.title ?? '') | translate | async }}</div>
                              @if (instance.description) {
                                <div class="instance-sub-heading">
                                  <span>{{ (instance.description ?? '') | translate | async }}</span>
                                </div>
                              }
                            </div>
                          </ion-col>
                        </ion-row>
                      </ion-grid>
                    }
                    <ng-container>
                      @if (moreInstanceResults) {
                        <div (click)="getProductFeatureInstances(feature.id, true)" class="load-more">
                          <ion-row>
                            <ion-col size="12">
                              <div>{{ 'Load More' | translate | async }}</div>
                              <div><ion-icon name="chevron-down-outline"></ion-icon></div>
                            </ion-col>
                          </ion-row>
                        </div>
                      }
                    </ng-container>
                  </ng-template>
                </mat-tab>
              </mat-tab-group>
            </div>
          }
        </mat-expansion-panel>
      }
    </mat-accordion>
    @if (moreResults) {
      <div (click)="getProductActionFeaturesById(id ?? '', true)" class="load-more">
        <ion-row>
          <ion-col size="12">
            <div>{{ 'Load More' | translate | async }}</div>
            <div><ion-icon name="chevron-down-outline"></ion-icon></div>
          </ion-col>
        </ion-row>
      </div>
    }
  </ion-grid>
</div>
