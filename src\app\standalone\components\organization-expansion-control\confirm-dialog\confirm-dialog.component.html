<div>
  <div>
    <h3>{{ 'Request download' | translate | async }}</h3>
    <span
      >{{ 'We\'ve sent you an email with a link to download your personal data collected by this organization. If you can\'t find the email, check your spam folder or click the button below to resend.' | translate | async }}</span
    >
  </div>
  <div mat-dialog-actions [align]="'end'">
    <ion-button color="warning" (click)="onClose(true)">{{ 'Resend' | translate | async }}</ion-button>
    <ion-button (click)="onClose()">{{ 'Cancel' | translate | async }}</ion-button>
  </div>
</div>
