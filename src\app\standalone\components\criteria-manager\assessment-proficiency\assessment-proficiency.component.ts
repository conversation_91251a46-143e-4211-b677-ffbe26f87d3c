import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IEarningCriteriaContentIn, IEarningCriteriaIn } from '@app/core/contracts/contract';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { AsyncPipe } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-assessment-proficiency',
    templateUrl: './assessment-proficiency.component.html',
    styleUrls: ['./assessment-proficiency.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, AsyncPipe, TranslatePipe]
})
export class AssessmentProficiencyComponent implements OnInit, OnDestroy {
  @Input() type: string;
  @Input() earningCriteria: IEarningCriteriaIn;
  @Output() criteriaUpdated: EventEmitter<IEarningCriteriaContentIn[]> = new EventEmitter();

  formValueChanges$: Subscription;
  assessmentProficiencyForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private formBuilder: FormBuilder) {}

  ngOnInit() {
    this.createForm();
  }

  createForm() {
    this.assessmentProficiencyForm = this.formBuilder.group({
      minValue: [this.earningCriteria?.minValue],
      maxValue: [this.earningCriteria.maxValue],
    });

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.assessmentProficiencyForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.setObjectValues();
    });
  }

  setObjectValues() {
    if (this.assessmentProficiencyForm.valid) {
      this.earningCriteria.minValue = this.assessmentProficiencyForm.controls.minValue.value;
      this.earningCriteria.maxValue = this.assessmentProficiencyForm.controls.maxValue.value;
      this.criteriaUpdated.emit(this.earningCriteria.earningCriteriaContent);
    }
  }

  checkPercentageInputLength(event: any, type: string) {
    const numberVal = Number(event.detail.value);
    if (numberVal >= 100) {
      if (type === 'min') {
        this.assessmentProficiencyForm.controls.minValue.setValue(100);
      } else {
        this.assessmentProficiencyForm.controls.maxValue.setValue(100);
      }
    } else if (numberVal < 0) {
      if (type === 'min') {
        this.assessmentProficiencyForm.controls.minValue.setValue(0);
      } else {
        this.assessmentProficiencyForm.controls.maxValue.setValue(0);
      }
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
