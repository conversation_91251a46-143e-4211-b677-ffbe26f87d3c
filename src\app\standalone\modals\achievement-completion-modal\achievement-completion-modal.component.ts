import { Async<PERSON>ipe, DatePipe, UpperCasePipe } from '@angular/common';
import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { MatIcon } from '@angular/material/icon';
import { NavigationStart, Router } from '@angular/router';
import { IAchievementCompletion, IFeatureType, IInstance, IMyInstanceResult, IRowLite } from '@app/core/contracts/contract';
import { GlobalToastService } from '@app/core/services/global-toast.service';
import { AuthService } from '@app/core/services/auth-service';
import { DataService } from '@app/core/services/data-service';
import { RowService } from '@app/core/services/row.service';
import { ParseContentPipe } from '@app/shared/pipes/parse-content';
import { IonicModule, ModalController } from '@ionic/angular';
import { TranslatePipe } from '@app/shared/pipes/translate';
import html2canvas from 'html2canvas';
import { Subject, debounceTime, takeUntil } from 'rxjs';

@Component({
    selector: 'app-achievement-completion-modal',
    templateUrl: './achievement-completion-modal.component.html',
    styleUrls: ['./achievement-completion-modal.component.scss'],
    imports: [IonicModule, MatIcon, AsyncPipe, UpperCasePipe, DatePipe, ParseContentPipe, TranslatePipe]
})
export class AchievementCompletionModalComponent implements OnInit, OnDestroy {
  @Input() achievementCompletion: IAchievementCompletion;
  @Input() instanceId: string;
  @Input() assetImageIconUrl: string;
  @Input() badgeImageIcon: string;
  @Input() badgeImageOverlay: string;
  badgePngGenerated = true;
  myPortfolioInstance: IInstance;
  myPortfolioInstanceRow: IRowLite;
  myPortfolioFeatureType: IFeatureType;
  hidePortfolioAdd = false;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private toast: GlobalToastService,
    private rowService: RowService,
    private dataService: DataService,
    private modalController: ModalController,
    public authService: AuthService,
    private router: Router
  ) {}

  ngOnInit() {
    this.getProfileFeatureType();

    this.router.events.subscribe(event => {
      if (event instanceof NavigationStart) {
        this.close();
      }
    });
  }

  downloadSnippet() {
    this.badgePngGenerated = false;
    const element = document.getElementById('screen-canvas');
    if (element) {
      html2canvas(element, { allowTaint: true, useCORS: true, backgroundColor: null }).then(canvas => {
        const link = document.createElement('a');
        link.href = canvas.toDataURL('image/png');
        link.download = this.achievementCompletion.badgeName + '.png';
        link.click();
        this.badgePngGenerated = true;
      });
    }
  }

  getProfileFeatureType() {
    this.dataService
      .getMyFeatureTypes(this.instanceId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((featureTypes: IFeatureType[]) => {
        const featureType = featureTypes.find(x => x.name === 'Portfolio');
        if (featureType) {
          this.myPortfolioFeatureType = featureType;
          this.getMyPortfolioInstance();
        }
      });
  }

  getMyPortfolioInstance() {
    this.dataService
      .getMyInstances('', this.instanceId, this.myPortfolioFeatureType.id)
      .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
      .subscribe((myInstanceResult: IMyInstanceResult) => {
        const instances = myInstanceResult.instances;
        const findPortfolio = instances.find(x => x.feature.featureType.name === 'Portfolio');
        if (findPortfolio) {
          this.myPortfolioInstance = findPortfolio;
        }
      });
  }

  addToMyProfileInstance() {
    this.getMyPortfolioRowManager(this.myPortfolioInstance.id);
  }

  getMyPortfolioRowManager(instanceId: string) {
    this.dataService
      .getInstanceRowManagerRows(instanceId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((rows: IRowLite[]) => {
        if (rows) {
          const findPortfolioRow = rows[0];
          if (findPortfolioRow) {
            this.myPortfolioInstanceRow = findPortfolioRow;
            this.rowService.addRowContent(this.myPortfolioInstanceRow.id, this.instanceId, this.myPortfolioInstance.id, 'Portfolio');
            this.hidePortfolioAdd = true;
          }
        } else {
          this.toast.presentToast('No row manager found for Portolio instance, please add one!');
        }
      });
  }

  close() {
    this.modalController.dismiss();
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
