import { Component, Input, OnInit } from '@angular/core';
import { AsyncPipe } from '@angular/common';
import { IComponent, INotification } from '@app/core/contracts/contract';
import { NotificationService } from '@app/core/services/notification-service';
import { environment } from '@env/environment';
import { IonicModule } from '@ionic/angular';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-banner',
    templateUrl: './banner.component.html',
    styleUrls: ['./banner.component.scss'],
    imports: [IonicModule, AsyncPipe, TranslatePipe]
})
export class BannerComponent implements OnInit {
  @Input() component: IComponent;
  notification?: INotification;
  imageUrl: string;

  constructor(public notificationService: NotificationService) {}

  ngOnInit() {
    this.notificationService.newNotification.subscribe(() => {
      this.notification = this.notificationService.notifications.find(
        x => x.communication.communicationBlocks?.some(y => y.id === this.component.templateField.communicationBlock?.id) && x.notificationTypeBW === 2
      );

      if (this.notification) {
        this.notificationService.showBanner = true;
      } else {
        this.notificationService.showBanner = false;
      }
      this.setImageIcon();
    });
    this.setImageIcon();
  }

  setImageIcon() {
    if (this.notification?.notificationIcon && this.notification?.notificationIcon !== '') {
      this.imageUrl = `${environment.contentUrl}asset/${this.notification?.notificationIcon}/content`;
    } else {
      this.imageUrl = 'assets/images/no-image.png';
    }
  }
}
