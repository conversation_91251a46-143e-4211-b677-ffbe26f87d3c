<mat-expansion-panel>
  <mat-expansion-panel-header class="expansion-panel-header">
    <ion-grid>
      <ion-row class="ion-align-items-center">
        <ion-col size="9">
          <div class="inner-panel">
            <div class="heading">{{ category?.name | translate | async }}</div>
            <div class="sub-heading">
              <span>{{ category?.description | translate | async }}</span>
            </div>
          </div>
        </ion-col>
        <ion-col size="1">
          <div class="inner-panel center-col">
            <ion-icon class="heading" name="mail-outline"></ion-icon>
            <div class="sub-heading">
              <span>{{ 'EMAIL' | translate | async }}</span>
            </div>
          </div>
        </ion-col>
        <ion-col size="1">
          <div class="inner-panel center-col">
            <ion-icon class="heading" name="phone-portrait-outline"></ion-icon>
            <div class="sub-heading">
              <span>{{ 'PUSH' | translate | async }}</span>
            </div>
          </div>
        </ion-col>
        <ion-col size="1">
          <div class="inner-panel center-col">
            <ion-icon class="heading" name="notifications-outline"></ion-icon>
            <div class="sub-heading">
              <span>{{ 'BELL' | translate | async }}</span>
            </div>
          </div>
        </ion-col>
      </ion-row>
    </ion-grid>
  </mat-expansion-panel-header>
  @if (communications$ | async; as communications) {
    <div>
      @for (communication of communications; track communication) {
        <ion-card>
          <ion-grid>
            <ion-row class="ion-align-items-center header-row">
              <ion-col size="9">
                <div class="inner-panel">
                  <div class="heading">{{ communication?.name | translate | async }}</div>
                  <div class="sub-heading">
                    <span>{{ communication?.description | translate | async }}</span>
                  </div>
                </div>
              </ion-col>
              <ion-col size="1">
                <div class="inner-panel center-col">
                  <ion-checkbox
                    [(ngModel)]="communication.userCommunicationPreferenceSetting.shouldReceiveEmail"
                    (ngModelChange)="settingChanged(communication.userCommunicationPreferenceSetting)"></ion-checkbox>
                </div>
              </ion-col>
              <ion-col size="1">
                <div class="inner-panel center-col">
                  <ion-checkbox
                    [(ngModel)]="communication.userCommunicationPreferenceSetting.shouldReceiveSms"
                    (ngModelChange)="settingChanged(communication.userCommunicationPreferenceSetting)"></ion-checkbox>
                </div>
              </ion-col>
              <ion-col size="1">
                <div class="inner-panel center-col">
                  <ion-checkbox
                    [(ngModel)]="communication.userCommunicationPreferenceSetting.shouldReceivePush"
                    (ngModelChange)="settingChanged(communication.userCommunicationPreferenceSetting)"></ion-checkbox>
                </div>
              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card>
      }
    </div>
  }
</mat-expansion-panel>
