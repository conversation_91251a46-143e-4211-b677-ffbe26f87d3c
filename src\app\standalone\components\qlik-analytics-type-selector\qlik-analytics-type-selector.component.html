<div class="qlik-type">
  @if (organizations.length > 1) {
    <mat-button-toggle-group [value]="preSelectedOrganizationId">
      @for (organization of organizations; track organization) {
        <mat-button-toggle [value]="organization.id" (change)="updateOrgSelection($event)">{{organization.name | translate | async}}</mat-button-toggle>
      }
    </mat-button-toggle-group>
  } @else if (networks.length > 1) {
    <mat-button-toggle-group [value]="preSelectedNetworkId">
      @for (network of networks; track network) {
        <mat-button-toggle [value]="network.id" (change)="updateNetworkSelection($event)">{{network.name | translate | async}}</mat-button-toggle>
      }
    </mat-button-toggle-group>
  }
</div>
