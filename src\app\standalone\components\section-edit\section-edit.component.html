@if (sectionForm) {
  <form [formGroup]="sectionForm" class="parent-form-container" [ngClass]="{ 'no-margin': panelBuilder }">
    <ion-grid>
      <ion-row>
        @if (!panelBuilder) {
          <ion-col class="back-col">
            <ion-button fill="clear" size="small" color="warning" (click)="back()"> <ion-icon slot="start" name="chevron-back-outline"></ion-icon>{{ 'Back' | translate | async }}</ion-button>
          </ion-col>
        }
      </ion-row>
      <ion-row>
        <ion-col class="top-header-col">
          <div class="header">{{ 'Edit' | translate | async }} {{ (section?.typeBw === 1 ? 'Static' : 'Dynamic') | translate | async }} {{ 'Section' | translate | async }}</div>
        </ion-col>
      </ion-row>
      <div class="middle-line"><hr /></div>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" formControlName="title" [label]="'Section title'"></app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-area-input-control [noPadding]="true" [backgroundColor]="'#292929'" formControlName="description" [label]="'Section description'"></app-text-area-input-control>
        </ion-col>
      </ion-row>
      @if (!panelBuilder) {
        <ion-row>
          <ion-col>
            <app-text-area-input-control [noPadding]="true" [backgroundColor]="'#292929'" formControlName="caption" [label]="'Section caption'"></app-text-area-input-control>
          </ion-col>
        </ion-row>
      }
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" formControlName="webMaxWidth" [label]="'Max Width - Laptop ( > 992px )'"> </app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [noPadding]="true" [backgroundColor]="'#292929'" formControlName="mobileMaxWidth" [label]="'Max Width - Mobile ( < 992px )'"> </app-text-input-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          @if (actions$ | async; as actions) {
            <app-select-option-control
              [toolTip]="'Select Section Access'"
              [placeHolder]="'Select Section Access'"
              [label]="'Section Access'"
              [formControlName]="'actions'"
              [multiple]="true"
              [backgroundColor]="'#292929'"
              [options]="actions"
              [noPadding]="true"></app-select-option-control>
          }
        </ion-col>
      </ion-row>
      <!-- <ion-row class="toggle-row">
      <ion-col class="toggle-col">
        <mat-slide-toggle color="primary" formControlName="hideBackground"></mat-slide-toggle>
        <ion-label>Hide background</ion-label>
      </ion-col>
    </ion-row> -->
      <ion-row>
        <ion-col>
          <div class="normal">
            <ion-item class="inner-container">
              <ion-label position="stacked" class="label-header">
                @if (customBackgroundColour === false) {
                  <span>{{ 'Pick Background Color' | translate | async }}</span>
                }
                @if (customBackgroundColour === true) {
                  <span>{{ 'CSS Background Color' | translate | async }}</span>
                }
              </ion-label>
              @if (customBackgroundColour === false) {
                <ion-input [(colorPicker)]="color" [style.background]="color" [cpOKButton]="true" [cpOutputFormat]="'hex'" (colorPickerChange)="captureColour($event)"></ion-input>
              }
              @if (customBackgroundColour === true) {
                <ion-textarea placeholder="{{ 'CSS colour style' | translate | async }}" [formControlName]="'backgroundColor'" [value]="color"></ion-textarea>
              }
              <ion-button (click)="openCustomBackgroundColour()">
                @if (customBackgroundColour === false) {
                  <span>{{ 'Change to Custom CSS Color' | translate | async }}</span>
                }
                @if (customBackgroundColour === true) {
                  <span>{{ 'Change to Pick Color' | translate | async }}</span>
                }
              </ion-button>
            </ion-item>
          </div>
        </ion-col>
      </ion-row>
      <ion-row class="toggle-row">
        <ion-col class="toggle-col">
          <mat-slide-toggle color="primary" formControlName="isCompletable"></mat-slide-toggle>
          <ion-label>{{ 'Is Completable' | translate | async }}</ion-label>
        </ion-col>
      </ion-row>
      <ion-row class="toggle-row">
        <ion-col class="toggle-col">
          <mat-slide-toggle color="primary" (click)="toggleSectionVisibility()" formControlName="showOnInstanceViewer"></mat-slide-toggle>
          <ion-label>{{ 'Section visibility' | translate | async }}</ion-label>
        </ion-col>
      </ion-row>
      <ion-row class="toggle-row">
        <ion-col class="toggle-col">
          <mat-slide-toggle color="primary" formControlName="showOnPlayerSidepanel"></mat-slide-toggle>
          <ion-label>{{ 'Show title in playlist panel' | translate | async }}</ion-label>
        </ion-col>
      </ion-row>
      <ion-row class="toggle-row">
        <ion-col class="toggle-col">
          <mat-slide-toggle color="primary" formControlName="showTitleOnPlayer"></mat-slide-toggle>
          <ion-label>{{ 'Show title above section' | translate | async }}</ion-label>
        </ion-col>
      </ion-row>
      <ion-row class="toggle-row">
        <ion-col class="toggle-col">
          <mat-slide-toggle color="primary" formControlName="showDescOnPlayer"></mat-slide-toggle>
          <ion-label>{{ 'Show description above section' | translate | async }}</ion-label>
        </ion-col>
      </ion-row>
      @if (!panelBuilder) {
        <ion-row class="toggle-row">
          <ion-col class="toggle-col">
            <mat-slide-toggle color="primary" formControlName="isEditable"></mat-slide-toggle>
            <ion-label>{{ 'Is Editable in builder' | translate | async }}</ion-label>
          </ion-col>
        </ion-row>
      }
      @if (!panelBuilder) {
        <ion-row class="toggle-row">
          <ion-col class="toggle-col">
            <mat-slide-toggle color="primary" formControlName="isContinuousFeedback"></mat-slide-toggle>
            <ion-label>{{ 'Continuous Feedback' | translate | async }}</ion-label>
          </ion-col>
        </ion-row>
      }
    </ion-grid>
  </form>
}
