import { After<PERSON><PERSON>w<PERSON>hecked, Component, Destroy<PERSON>ef, inject, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';
import { IInstanceDetailsValues, IUserInstanceTracking } from '@app/core/contracts/contract';
import { ViewType } from '@app/core/enums/view-type';
import { DataService } from '@app/core/services/data-service';
import { ParseService } from '@app/core/services/parse-service';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { first, takeUntil } from 'rxjs';
import { NgClass, NgStyle, AsyncPipe, DatePipe } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { ParseContentPipe } from '../../../shared/pipes/parse-content';
import { TranslatePipe } from '../../../shared/pipes/translate';
import { AuthoringHeaderComponent } from '../authoring-header/authoring-header.component';
import { HeaderImageBaseComponent } from '../header-image-base/header-image-base.component';
import { Events } from '@app/core/services/events-service';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { DynamicTextValueComponent } from '../dynamic-text-value/dynamic-text-value.component';
import { MetaTagService } from '@app/core/services/meta-tag.service';
@Component({
    selector: 'app-instance-details-value',
    templateUrl: './instance-details-value.component.html',
    styleUrls: ['./instance-details-value.component.scss'],
    imports: [NgClass, IonicModule, NgStyle, AuthoringHeaderComponent, AsyncPipe, DatePipe, ParseContentPipe, TranslatePipe, DynamicTextValueComponent]
})
export class InstanceDetailsValueComponent extends HeaderImageBaseComponent implements OnInit, OnChanges, OnDestroy, AfterViewChecked {
  @Input() id: string;
  @Input() viewType: number;
  @Input() actionBw: ActionTypes | undefined;
  userInstanceTracking: IUserInstanceTracking;

  title: string | undefined;
  description: string | undefined;
  headingStyle?: string;
  linkId: string;
  centerContent = false;
  moveOverBanner = false;
  viewTypes = ViewType;
  canMoveOverBanner = false;
  destroyRef = inject(DestroyRef);

  constructor(
    dataService: DataService,
    systemPropertiesService: SystemPropertiesService,
    parseService: ParseService,
    builderService: BuilderService,
    private eventService: Events,
    private metaTagService: MetaTagService,
    private parseContentPipe: ParseContentPipe
  ) {
    super(parseService, builderService, dataService, systemPropertiesService);
  }

  get organizationManager() {
    return this.instance?.feature?.featureType?.name === 'Organization Manager';
  }

  ngOnInit() {
    this.height = 300;
    this.setCenterContent();
    this.setMoveOverBanner();
    this.getUserTrackingData();
    this.setValues();
    this.systemPropertiesService.reload$.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.setValues();
    });

    this.eventService.subscribe('componentValueChanged', object => {
      if (object.componentId === this.instanceSectionComponent.component.id) {
        this.instanceSectionComponent.value = object.value;
        this.setValues();
      }
    });

    this.destroyRef.onDestroy(() => {
      this.eventService.unsubscribe('componentValueChanged');
    });

    this.setIconAndCoverUrl$.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      if (this.instance && this.instance.status != 'private') {
        this.parseContentPipe
          .transform(this.instance.feature.featureType.name.toLowerCase() === 'internal' ? (this.instance.feature.description ?? '') : (this.instance.description ?? ''), this.instance.id, null, true)
          .pipe(first())
          .subscribe((result: string) => {
            if (result) {
              this.metaTagService.updateMetaTags(this.instance.title, result, this.iconUrl);
            }
          });
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['instance']) {
      this.getUserTrackingData();
      this.setValues();
      this.setCenterContent();
      this.setMoveOverBanner();
    }
  }

  setCenterContent() {
    this.centerContent = this.instanceSectionComponent.component.templateField.showGradient ?? false;
  }

  setMoveOverBanner() {
    this.moveOverBanner = this.instanceSectionComponent.component.templateField.moveToBack ?? false;
  }

  getUserTrackingData() {
    this.dataService
      .getUserInstanceTracking(this.instance.id)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((data: IUserInstanceTracking) => {
        this.userInstanceTracking = data;
      });
  }

  setValues() {
    // Do not like the org-profile check it is a hack......
    if (this.instanceSectionComponent.component.templateField.isInherit === false) {
      const instanceDetailsValues: IInstanceDetailsValues = this.instanceSectionComponent.value ? JSON.parse(this.instanceSectionComponent.value) : null;
      this.iconAssetId = instanceDetailsValues?.iconAssetId;
      this.title = instanceDetailsValues?.title;
      this.description = instanceDetailsValues?.description;
      this.headingStyle = this.instanceSectionComponent.component.templateField.headingStyle ?? undefined;
      this.linkId = this.instance.id;
    } else if (this.instance.feature.featureType.name === 'Organization Manager' || this.instance.feature.featureSlug === 'org-profile') {
      this.iconAssetId = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('LogoAssetId'))?.value ?? '';
      this.title = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('Name'))?.value;
      this.description = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('OrganizationBio'))?.value;
      this.linkId = this.id;
    } else if (this.instance.feature.featureType.name === 'User Manager') {
      this.iconAssetId = this.systemPropertiesService.userProperties.find(x => x.key.includes('picture'))?.value ?? '';
      this.title = this.systemPropertiesService.userProperties.find(x => x.key.includes('name'))?.value;
      this.description = this.systemPropertiesService.userProperties.find(x => x.key.includes('family_name'))?.value;
      this.linkId = this.id;
    } else if (this.instance.feature.featureType.name === 'Product Manager') {
      this.iconAssetId = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('IconAssetId'))?.value ?? '';
      this.title = this.systemPropertiesService.productProperties.find(x => x.key.includes('Name'))?.value;
      this.description = this.systemPropertiesService.productProperties.find(x => x.key.includes('Description'))?.value;
      this.linkId = this.id;
    } else if (this.viewType === this.viewTypes.Builder) {
      this.iconAssetId = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('IconAssetId'))?.value ?? '';
      this.title = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('Title'))?.value;
      this.description = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('Description'))?.value;
      this.headingStyle = this.instanceSectionComponent.component.templateField.headingStyle ?? undefined;
      this.linkId = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('Id'))!.value;
    } else {
      this.iconAssetId = this.instance.iconAssetId ?? '';
      this.title = this.instance.title;
      this.description = this.instance.description;
      this.headingStyle = this.instanceSectionComponent.component.templateField.headingStyle ?? undefined;
      this.linkId = this.instance.id;
    }

    this.setIconAndCoverUrl();
  }

  ngAfterViewChecked(): void {
    const element = document.getElementById('parent') as HTMLDivElement;
    const banner = document.querySelector('app-page-banner') as HTMLElement;

    if (banner !== null && banner.offsetHeight > 1 && banner.getBoundingClientRect().bottom <= element.getBoundingClientRect().top) {
      this.canMoveOverBanner = true;
    }
  }
}
