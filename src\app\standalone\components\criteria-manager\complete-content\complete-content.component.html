<div class="parent-container">
  <ion-row>
    <ion-col size="auto" class="heading-col">{{ 'Complete the following:' | translate | async }}</ion-col>
    <ion-col class="inner-col">
      @if (earningCriteria.earningCriteriaContent && earningCriteria.earningCriteriaContent.length > 0) {
        <app-chip-list style="width: 100%" (selectedToRemoveOut)="checkToRemoveById($event.selected)" [type]="type" [listItems]="getChipListItems()"></app-chip-list>
      }
      <div class="search-icon-container"><ion-icon (click)="addSearchModalOpen($event)" name="search-outline"></ion-icon></div>
    </ion-col>
  </ion-row>
</div>
