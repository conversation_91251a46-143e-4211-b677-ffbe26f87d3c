import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { IInstance, IMyInstanceResult } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { IonicModule } from '@ionic/angular';
import { debounceTime, Subject, takeUntil } from 'rxjs';
import { CreateInstanceData } from '../../add-to-dialog.component';
import { InstanceCardComponent } from '../instance-card/instance-card.component';

@Component({
    selector: 'app-class-selector',
    imports: [IonicModule, FormsModule, InstanceCardComponent, AsyncPipe, TranslatePipe],
    templateUrl: './class-selector.component.html',
    styleUrl: './class-selector.component.scss'
})
export class ClassSelectorComponent implements OnInit {
  @Input() instanceId: string;
  @Input() instances: IInstance[];
  @Input() height: number = 60;
  @Output() instanceSelected = new EventEmitter<IInstance>();
  @Output() createClicked = new EventEmitter();
  @Output() closeClicked = new EventEmitter();

  lastModified?: IInstance[];
  query: string;
  featureTypeSelectedId: string | undefined;
  featureId = '';

  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.dataService
      .getFeatureByTypeName('Modifiable Learning Container Pages')
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        this.featureId = data.id;
      });

    this.searchMyInstances();
  }

  searchMyInstances(featureTypeId?: string) {
    this.featureTypeSelectedId = featureTypeId;
    this.dataService
      .getMyInstances(this.query, this.instanceId, featureTypeId)
      .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
      .subscribe((data: IMyInstanceResult) => {
        this.instances = data.instances;
        this.lastModified = data.lastModified;
      });
  }

  newClass(event: any) {
    this.createClicked.next({ featureId: this.featureId, event: event } as CreateInstanceData);
  }

  close() {
    this.closeClicked.next('');
  }

  selectInstance(instance: IInstance) {
    this.instanceSelected.next(instance);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
