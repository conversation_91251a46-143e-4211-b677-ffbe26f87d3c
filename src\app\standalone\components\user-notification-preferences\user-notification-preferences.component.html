<div class="preferences-container">
  @if (formGroup) {
    <ng-container [formGroup]="formGroup">
      @if (emails) {
        <app-select-option-control
          [options]="emails"
          [label]="'Preferred email'"
          [placeHolder]="'--select--'"
          [backgroundColor]="'#181818'"
          formControlName="email"
          [toolTip]="'Choose your preferred email'"></app-select-option-control>
      }
      <div class="row-center container">
        <ion-checkbox formControlName="combine" color="primary"></ion-checkbox><ion-label>{{ 'Combine email notifications and send me a daily digest' | translate | async }}</ion-label>
      </div>
    </ng-container>
  }
  @if (categories) {
    @for (category of categories; track category) {
      <app-user-notification-preferences-expander
        [category]="category"
        [userCommunicationPreference]="userCommunicationPreference"
        (userCommunicationPreferenceChanged)="updateUserCommunicationPreference()"></app-user-notification-preferences-expander>
    }
  }
</div>
