@if (settingsForm) {
  <form [formGroup]="settingsForm">
    <ion-grid class="grid-container">
      <ion-row>
        <ion-col>
          <ion-item>
            <ion-label position="stacked"> {{ 'Name' | translate | async }} </ion-label>
            <ion-input class="input-background" [spellcheck]="true" [maxlength]="125" placeholder="{{ 'Add a row name here...' | translate | async }}" formControlName="title"></ion-input>
            <span class="text-counter">{{ title.value?.length }} / 125</span>
          </ion-item>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <ion-item>
            <ion-label position="stacked"> {{ 'Description' | translate | async }} </ion-label>
            <ion-textarea
              class="input-background"
              [spellcheck]="true"
              [maxlength]="250"
              [rows]="3"
              placeholder="{{ 'Add your description here...' | translate | async }}"
              formControlName="description"></ion-textarea>
            <span class="text-counter">{{ description.value?.length }} / 250</span>
          </ion-item>
        </ion-col>
      </ion-row>
      @if (
        (row.rowType.typeBw === rowTypes.Manual ||
          row.rowType.typeBw === rowTypes.Deliverable ||
          row.rowType.typeBw === rowTypes.ModifiablePackage ||
          row.rowType.typeBw === rowTypes.AccreditedPackage) &&
        hasManageAccess()
      ) {
        <ion-row>
          <ion-col>
            <ion-item>
              <ion-label position="stacked"> {{ 'Navigate to:' | translate | async }} </ion-label>
              <ion-select class="input-background" interface="popover" formControlName="instanceDisplayField">
                @for (item of instanceDisplayKeyValue; track item) {
                  <ion-select-option [value]="item.id">
                    {{ item.value | translate | async }}
                  </ion-select-option>
                }
              </ion-select>
            </ion-item>
          </ion-col>
        </ion-row>
      }
      @if (row.rowType.typeBw === 8 || row.rowType.typeBw === 16) {
        <ion-row class="flex-container">
          @if (row.rowType.typeBw === 8) {
            <ion-col>
              <ion-item class="due-date-item">
                <ion-label position="stacked"> {{ 'Due Date' | translate | async }} </ion-label>
                <ion-input placeholder="Select a date" value="{{ dueDate.value | date: 'YYYY/MM/dd' }}" id="date" [readonly]="true">
                  <ion-icon class="calendar-icon" name="calendar-outline" item-right></ion-icon>
                </ion-input>
                <ion-popover trigger="date" side="top" id="popover-bottom" alignment="end">
                  <ng-template>
                    <ion-content>
                      <ion-datetime formControlName="dueDate" presentation="date" displayFormat="yyyy/mm/dd" [preferWheel]="true" class="ion-datetime-dark"></ion-datetime>
                    </ion-content>
                  </ng-template>
                </ion-popover>
              </ion-item>
            </ion-col>
          }
          <ion-col>
            <ion-item class="no-hover-item">
              <ion-label position="stacked"> {{ 'Visibility' | translate | async }} </ion-label>
              <ion-select class="input-background" interface="popover" formControlName="instanceDisplayField">
                @for (item of statusses; track item) {
                  <ion-select-option [value]="item.id">
                    {{ item.value | translate | async }}
                  </ion-select-option>
                }
              </ion-select>
            </ion-item>
          </ion-col>
        </ion-row>
      }
      @if (hasManageAccess() && row.rowType.typeBw !== 8 && row.rowType.typeBw !== 16) {
        <ion-row>
          <ion-col>
            <ion-item>
              <ion-label position="stacked"> {{ 'Row Completion Criteria' | translate | async }} </ion-label>
              <ion-select interface="popover" formControlName="earningCriteria">
                @for (item of earningCriteriaTypes; track item) {
                  <ion-select-option [value]="item.id">
                    {{ item.name | translate | async }}
                  </ion-select-option>
                }
              </ion-select>
            </ion-item>
          </ion-col>
          <ion-col>
            <ion-item>
              <ion-label position="stacked"> {{ 'Completion Requirements' | translate | async }} </ion-label>
              <ion-input formControlName="minValue"></ion-input>
            </ion-item>
          </ion-col>
          <ion-item class="completion-criteria-button">
            <ion-button (click)="cancel()" fill="clear">
              <ion-icon name="close-circle"></ion-icon>
            </ion-button>
          </ion-item>
        </ion-row>
      }
      @if (hasManageAccess() && row.rowType.typeBw !== 8 && row.rowType.typeBw !== 16) {
        <ion-row>
          <ion-col>
            <ion-item>
              <ion-label class="chip-label" position="stacked"> {{ 'Row Tags' | translate | async }} </ion-label>
              <div class="chip-list-container">
                <mat-form-field>
                  <mat-chip-grid #chipList aria-label="Tag selection">
                    @for (tag of existingTags$ | async; track tag) {
                      <mat-chip-option (removed)="removeTagDirect(tag)">
                        <div class="name">{{ tag.name | translate | async }}</div>
                        <button matChipRemove [attr.aria-label]="'remove ' + tag.name">
                          <mat-icon>cancel</mat-icon>
                        </button>
                      </mat-chip-option>
                    }
                    <input [matChipInputFor]="chipList" [matChipInputSeparatorKeyCodes]="separatorKeysCodes" [matChipInputAddOnBlur]="addOnBlur" (matChipInputTokenEnd)="add($event)" />
                    <mat-label (click)="openTagModal()">{{ 'Add tag' | translate | async }}</mat-label>
                  </mat-chip-grid>
                </mat-form-field>
              </div>
            </ion-item>
          </ion-col>
        </ion-row>
      }
      @if (hasManageAccess() && row.rowType.typeBw !== 8 && row.rowType.typeBw !== 16) {
        <ion-row>
          <ion-col>
            <ion-item>
              <ion-label class="chip-label" position="stacked"> {{ 'Row Products' | translate | async }} </ion-label>
              <div class="chip-list-container">
                <mat-form-field>
                  <mat-chip-grid #chipList aria-label="Product selection">
                    @for (product of existingProducts$ | async; track product) {
                      <mat-chip-option (removed)="removeProductDirect(product)">
                        <div class="name">{{ product.name | translate | async }}</div>
                        <button matChipRemove [attr.aria-label]="'remove ' + product.name">
                          <mat-icon>cancel</mat-icon>
                        </button>
                      </mat-chip-option>
                    }
                    <input [matChipInputFor]="chipList" [matChipInputSeparatorKeyCodes]="separatorKeysCodes" [matChipInputAddOnBlur]="addOnBlur" (matChipInputTokenEnd)="add($event)" />
                    <mat-label (click)="openProductModal()">{{ 'Add product' | translate | async }}</mat-label>
                  </mat-chip-grid>
                </mat-form-field>
              </div>
            </ion-item>
          </ion-col>
        </ion-row>
      }
      @if (hasManageAccess()) {
        <ion-row>
          <ion-col class="guest-checkbox-col">
            <ion-checkbox formControlName="allowGuestAccess" color="primary"></ion-checkbox>
            <ion-label>{{ 'Allow guest access' | translate | async }}</ion-label>
          </ion-col>
        </ion-row>
      }
    </ion-grid>
  </form>
}
