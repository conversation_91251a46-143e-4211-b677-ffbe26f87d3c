import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { IPrivacyType, IOrgPrivacyTypeIn } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { LayoutService } from '@app/core/services/layout-service';
import { AlertController, IonicModule } from '@ionic/angular';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AsyncPipe, NgClass } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-privacy-manager-setup',
    templateUrl: './privacy-manager-setup.component.html',
    styleUrls: ['./privacy-manager-setup.component.scss'],
    imports: [IonicModule, NgClass, AsyncPipe, TranslatePipe]
})
export class PrivacyManagerSetupComponent implements OnInit, OnDestroy {
  @Input() organizationId: string;
  @Input() privacyTypeId: string | undefined;
  privacyTypes: IPrivacyType[] = [];
  dataLoaded = false;
  handlerMessage = '';
  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(
    private dataService: DataService,
    private alertController: AlertController,
    public layoutService: LayoutService
  ) {}

  ngOnInit() {
    this.getPrivacyTypes();
  }

  getPrivacyTypes() {
    this.dataService
      .getPrivacyTypes()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(privacyTypes => {
        if (privacyTypes) {
          this.dataLoaded = true;
          this.privacyTypes = privacyTypes;
        }
      });
  }

  async selectedType(typeId: string) {
    if (typeId) {
      const alert = await this.alertController.create({
        header: 'Are you sure?',
        message: 'This step might result in the loss of data.',
        buttons: [
          {
            text: 'Cancel',
            role: 'cancel',
            handler: () => {
              this.dataLoaded = false;
              this.getPrivacyTypes();
            },
          },
          {
            text: 'OK',
            role: 'ok',
            handler: async () => {
              const selectedTypeIn = { privacyTypeId: typeId, organizationId: this.organizationId } as IOrgPrivacyTypeIn;
              await this.dataService
                .updateOrgPrivacyType(selectedTypeIn)
                .pipe(takeUntil(this.componentDestroyed$))
                .subscribe(() => {
                  this.privacyTypeId = typeId;
                });
            },
          },
        ],
      });

      alert.present();
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
