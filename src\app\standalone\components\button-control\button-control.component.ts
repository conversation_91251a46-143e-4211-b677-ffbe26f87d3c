import { Ng<PERSON><PERSON>, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { Component, forwardRef, Input, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, NG_VALIDATORS, NG_VALUE_ACCESSOR, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatSlideToggle } from '@angular/material/slide-toggle';
import { IButton, IComponent, ITag } from '@app/core/contracts/contract';
import { IonicModule } from '@ionic/angular';
import { Subject, takeUntil, tap } from 'rxjs';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { ComponentUpdateSignalService } from '@app/core/services/component-update-signals.service';
import { AlignmentControlComponent } from '../alignment-control/alignment-control.component';
import { DataService } from '@app/core/services/data-service';
import { TagSelectOptionControlComponent } from '../tag-select-option-control/tag-select-option-control.component';

@Component({
    selector: 'app-button-control',
    templateUrl: './button-control.component.html',
    styleUrls: ['./button-control.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => ButtonControlComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => ButtonControlComponent),
        },
    ],
    imports: [NgClass, IonicModule, FormsModule, ReactiveFormsModule, TextInputControlComponent, MatSlideToggle, AlignmentControlComponent, TagSelectOptionControlComponent, AsyncPipe, TranslatePipe]
})
export class ButtonControlComponent extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() override label: string;
  @Input() description: string;
  @Input() component: IComponent;
  @Input() sidePanelPadding = false;

  textForm: FormGroup;
  button: IButton = { buttonName: '', url: '', sameUrlNavigation: false, tagId: undefined };
  componentDestroyed$: Subject<boolean> = new Subject();
  stylingDirection: string;
  buttonTags: KeyValue[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private signalService: ComponentUpdateSignalService,
    private dataService: DataService
  ) {
    super();
  }

  ngOnInit() {
    this.getButtonTags();
    this.initButton();
    this.createForm();
    this.fieldValueChanged
      .pipe(
        takeUntil(this.componentDestroyed$),
        tap(() => {
          this.initButton();
          this.updateForm();
        })
      )
      .subscribe();
  }

  private initButton() {
    if (this.textValue) {
      try {
        this.button = JSON.parse(this.textValue) as IButton;
      } catch (error) {
        this.button = {
          buttonName: this.textValue,
          url: '',
          sameUrlNavigation: false,
        };
      }
    }
  }

  private createForm() {
    this.textForm = this.formBuilder.group({
      buttonName: [this.button.buttonName ?? null, Validators.required],
      url: [this.button.url ?? null],
      sameUrlNavigation: [this.button.sameUrlNavigation ?? false],
      stylingDirection: [this.component.templateField?.stylingDirection ?? 'Left', Validators.required],
      tagId: [this.button.tagId ?? null],
    });

    this.subscribeToFormChanges();
  }

  private updateForm() {
    this.textForm.patchValue(
      {
        buttonName: this.button.buttonName,
        url: this.button.url,
        sameUrlNavigation: this.button.sameUrlNavigation,
        tagId: this.button.tagId,
      },
      { emitEvent: false }
    );
  }

  private subscribeToFormChanges() {
    this.textForm.valueChanges
      .pipe(
        takeUntil(this.componentDestroyed$),
        tap(() => this.updateComponentValues())
      )
      .subscribe();
  }

  private getButtonTags() {
    this.dataService.getTagChildren(null, -1, true).subscribe((tags: ITag[]) => {
      this.buttonTags = this.flattenTagHierarchy(tags);
    });
  }

  private flattenTagHierarchy(tags: ITag[]): KeyValue[] {
    let result: KeyValue[] = [];
    
    for (const tag of tags) {
      const displayName = tag.name;
      result.push({ 
        id: tag.id,
        value: displayName,
        parentId: tag.parentId,
        level: tag.treeLevel,
        hasChildren: (tag.inverseParent?.length || 0) > 0
      });
      
      if (tag.inverseParent?.length) {
        result = result.concat(this.flattenTagHierarchy(tag.inverseParent));
      }
    }

    return result;
  }

  private updateComponentValues() {
    if (this.textForm.valid && this.component.templateField) {
      const { stylingDirection, buttonName, url, sameUrlNavigation, tagId } = this.textForm.value;
      Object.assign(this.component.templateField, { stylingDirection });
      //Call this last after all values have been set to triggers the Save
      this.forceWriteValue(JSON.stringify({ buttonName, url, sameUrlNavigation, tagId }));

      //Update the Change detection on the Signal
      const changedValue = JSON.stringify({ buttonName, url, sameUrlNavigation, stylingDirection, tagId });
      if (this.component.id) {
        this.signalService.triggerSignal({ componentId: this.component.id, updateValue: changedValue });
      }
    }
  }

  formChanges() {
    this.textForm.valueChanges.subscribe(() => {
      this.updateComponent(this.textForm.value as IButton);
    });
  }

  isSelected(styling: string) {
    return this.stylingDirection ? this.stylingDirection === styling : false;
  }

  checkboxChanged(styling: string) {
    this.stylingDirection = styling;
    this.textForm.patchValue({ stylingDirection: this.stylingDirection });
  }

  updateComponent(textAndButtonIn: IButton) {
    this.setValue(JSON.stringify(textAndButtonIn));
  }

  override setValue(value: string) {
    this.writeValue(value);
    if (this.component.id) {
      this.signalService.triggerSignal({ componentId: this.component.id, updateValue: value });
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
