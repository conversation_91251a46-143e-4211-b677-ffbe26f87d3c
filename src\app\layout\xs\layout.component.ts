import { Component } from '@angular/core';
import { PendingRequestsInterceptor } from '@app/core/interceptors/pending-requests-interceptor';
import { AuthService } from '@app/core/services/auth-service';
import { BannerService } from '@app/core/services/banner.service';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { JoinCodeService } from '@app/core/services/join-code.service';
import { LayoutService } from '@app/core/services/layout-service';
import { NotificationService } from '@app/core/services/notification-service';
import { StorageService } from '@app/core/services/storage-service';
import { <PERSON>u<PERSON><PERSON>roller, ModalController, NavController, PopoverController } from '@ionic/angular';
import { LayoutComponent } from '../layout.component';
import { UnsavedChangesGuard } from '@app/core/services/unsaved-changes.guard';
import { AlertService } from '@app/core/services/alert-service';
import { GoogleTranslateService } from '@app/core/services/google-translate.service';

@Component({
  selector: 'app-layout-xs',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss'],
  standalone: false,
})
export class LayoutXSComponent extends LayoutComponent {
  constructor(
    layoutService: LayoutService,
    authService: AuthService,
    dataService: DataService,
    storageService: StorageService,
    pendingRequestsInterceptor: PendingRequestsInterceptor,
    popoverController: PopoverController,
    navController: NavController,
    modalController: ModalController,
    notificationService: NotificationService,
    menuController: MenuController,
    joinCodeService: JoinCodeService,
    instanceService: InstanceService,
    unsavedChangesGuard: UnsavedChangesGuard,
    bannerService: BannerService,
    alertService: AlertService,
    googleTranslateService: GoogleTranslateService
  ) {
    super(
      layoutService,
      authService,
      notificationService,
      googleTranslateService,
      dataService,
      storageService,
      pendingRequestsInterceptor,
      popoverController,
      navController,
      modalController,
      menuController,
      joinCodeService,
      instanceService,
      unsavedChangesGuard,
      bannerService,
      alertService
    );
  }
}
