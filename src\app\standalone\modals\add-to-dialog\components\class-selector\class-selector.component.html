<ion-header class="ion-no-border">
  <ion-toolbar mode="ios">
    <ion-buttons slot="end">
      <ion-button (click)="close()">
        <ion-icon slot="icon-only" name="close-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
  <ion-toolbar class="title-toolbar" mode="ios">
    <ion-title>{{ 'Assign to a Class' | translate | async }}</ion-title>
  </ion-toolbar>
  <ion-toolbar>
    <ion-searchbar [(ngModel)]="query" (ionInput)="searchMyInstances()" type="search" placeholder="{{ 'Search' | translate | async }}" showCancelButton="focus" enterkeyhint="enter" [debounce]="1000"></ion-searchbar>
  </ion-toolbar>
</ion-header>
<ion-content class="ion-padding" [style.height.vh]="height" [style.min-height.vh]="height" [style.max-height.vh]="height">
  <ion-grid>
    @if (lastModified && lastModified.length > 0) {
      <ion-row>
        <ion-header>{{ 'Recently modified classes' | translate | async }}</ion-header>
        <ion-grid>
          @for (instance of lastModified; track $index) {
            <app-instance-card [instance]="instance" (instanceClicked)="selectInstance($event)"></app-instance-card>
          }
          <hr />
        </ion-grid>
      </ion-row>
    }
    @if (instances && instances.length > 0) {
      <ion-row>
        <ion-header>{{ 'All of your classes' | translate | async }}</ion-header>
        <ion-grid>
          @for (instance of instances; track $index) {
            <app-instance-card [instance]="instance" (instanceClicked)="selectInstance($event)"></app-instance-card>
          }
          <hr />
        </ion-grid>
      </ion-row>
    }
  </ion-grid>
</ion-content>
<ion-footer>
  <ion-toolbar mode="ios">
    <ion-buttons slot="start">
      <ion-button fill="solid" (click)="newClass($event)"> <ion-icon slot="start" color="light" name="add-outline"></ion-icon>{{ 'Create a new Class' | translate | async }}</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-footer>
