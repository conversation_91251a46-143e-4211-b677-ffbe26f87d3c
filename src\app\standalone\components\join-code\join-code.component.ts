import { Component, Input, OnInit } from '@angular/core';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { MatIcon } from '@angular/material/icon';
import { GlobalToastService } from '@app/core/services/global-toast.service';
import { environment } from '@env/environment';
import { IonicModule } from '@ionic/angular';
import { QRCodeComponent } from 'angularx-qrcode';
import html2canvas from 'html2canvas';

@Component({
  selector: 'app-join-code',
  templateUrl: './join-code.component.html',
  styleUrls: ['./join-code.component.scss'],
  imports: [IonicModule, QRCodeComponent, MatIcon, AsyncPipe, TranslatePipe],
})
export class JoinCodeComponent implements OnInit {
  @Input() joinCode: string | undefined;
  @Input() roleName: string | undefined;
  @Input() featureTypeName: string | undefined;

  joinCodeQRCode: string | undefined;
  showQRCode = false;

  constructor(private toast: GlobalToastService) {}

  ngOnInit() {
    this.joinCodeQRCode = environment.apiUrl + 'user/joincode/' + this.joinCode;
  }

  copy() {
    if (!navigator.clipboard) {
      const selBox = document.createElement('textarea');
      selBox.style.position = 'fixed';
      selBox.style.left = '0';
      selBox.style.top = '0';
      selBox.style.opacity = '0';
      selBox.value = this.joinCode ?? '';
      document.body.appendChild(selBox);
      selBox.focus();
      selBox.select();
      document.execCommand('copy');
      document.body.removeChild(selBox);
      this.toast.presentToast('Copied successfully');
    } else {
      navigator.clipboard.writeText(this.joinCode ?? '').then(() => {
        this.toast.presentToast('Copied successfully');
      });
    }
  }

  toggleQRCode() {
    if (this.featureTypeName !== 'Modifiable Learning Container Pages') {
      this.showQRCode = !this.showQRCode;
    }
  }

  downloadSnippet() {
    const element = document.getElementById('screen-canvas');

    if (element) {
      html2canvas(element, { allowTaint: true, useCORS: true, backgroundColor: null }).then(canvas => {
        const link = document.createElement('a');
        link.href = canvas.toDataURL('image/png');
        link.download = this.roleName + '_' + this.joinCode + '.png';
        link.click();
      });
    }
  }
}
