<ion-header class="ion-no-border">
  <ion-toolbar mode="ios">
    <ion-card-subtitle class="selected-header">{{ "You've selected this class" | translate | async }}</ion-card-subtitle>
  </ion-toolbar>
  <ion-toolbar id="popover-ref">
    @if (instances) {
      <app-instance-card
        [instance]="selectedInstance"
        [isModalOpen]="isModalOpen"
        (instanceClicked)="selectInstanceClicked($event)"
        [isDropdown]="true"
        [isModalOpen]="isInstanceModalOpen"></app-instance-card>
    }
    <div class="short-grey-line">
      <hr />
    </div>
  </ion-toolbar>
  <ion-toolbar mode="ios">
    @if (assignments && assignments.length > 0) {
      <ion-header class="center-header">{{ 'Now choose the assignment you' | translate | async }}<br />{{ 'want to add this to' | translate | async }}</ion-header>
    } @else {
      <ion-header class="center-header">{{ 'No assignments found...' | translate | async }}</ion-header>
    }
  </ion-toolbar>
</ion-header>
<ion-content
  class="ion-padding"
  [style.height.vh]="assignments && assignments.length > 0 ? height : 25"
  [style.min-height.vh]="assignments && assignments.length > 0 ? height : 25"
  [style.max-height.vh]="assignments && assignments.length > 0 ? height : 25">
  <ion-grid>
    @if (assignments && assignments.length > 0) {
      <ion-row>
        <ion-grid>
          <hr />
          @for (instance of assignments; track $index) {
            <app-instance-card [instance]="instance" [selected]="instance.id === selectedAssignment?.id" (instanceClicked)="selectInstance($event)"></app-instance-card>
          }
        </ion-grid>
      </ion-row>
    } @else {
      <ion-row>
        <ion-col>
          <ion-img src="\assets\images\Create Content Icon.png"></ion-img>
          <ion-header class="center-header">{{ 'Create a new assignment' | translate | async }}</ion-header>
          <ion-card-subtitle class="selected-header content-subtext">{{ "Assignments are used to group content that you've assigned to a class" | translate | async }}</ion-card-subtitle>
        </ion-col>
      </ion-row>
    }
  </ion-grid>
</ion-content>
<ion-footer>
  <ion-toolbar mode="ios">
    @if (!selectedAssignment) {
      <ion-buttons [ngClass]="{ normal: assignments && assignments.length > 0 }" slot="start">
        @if (assignments && assignments.length > 0) {
          <ion-button class="add-intance-button" fill="solid" (click)="addInstance(null)">
            <ion-icon slot="start" class="add-button" color="light" name="add-outline"></ion-icon>{{ 'Create a new Assignment' | translate | async }}
          </ion-button>
        } @else {
          <ion-button class="button-highlight" fill="solid" (click)="addInstance(null)">
            <ion-icon slot="start" class="add-button" color="dark" name="add-outline"></ion-icon>{{ 'Create a new Assignment' | translate | async }}
          </ion-button>
        }
      </ion-buttons>
    } @else {
      <ion-buttons slot="end">
        <ion-button class="assign-button" fill="solid" color="primary" [disabled]="loading === true" (click)="assign(selectedAssignment)">{{ 'Assign' | translate | async }}</ion-button>
      </ion-buttons>
    }
  </ion-toolbar>
</ion-footer>
