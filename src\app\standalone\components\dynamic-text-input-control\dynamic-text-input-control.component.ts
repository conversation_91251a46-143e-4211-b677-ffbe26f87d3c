import { Component, forwardRef, Input, OnInit } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { NgClass, AsyncPipe } from '@angular/common';
import { ContentQuillEditorComponent } from '../content-quill-editor/content-quill-editor.component';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { ComponentUpdateSignalService } from '@app/core/services/component-update-signals.service';
import { IComponent } from '@app/core/contracts/contract';
import { TranslatePipe } from '@app/shared/pipes/translate';
@Component({
    selector: 'app-dynamic-text-input-control',
    templateUrl: './dynamic-text-input-control.component.html',
    styleUrls: ['./dynamic-text-input-control.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => DynamicTextInputControlComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => DynamicTextInputControlComponent),
        },
    ],
    imports: [IonicModule, NgClass, ContentQuillEditorComponent, AsyncPipe, TranslatePipe]
})
export class DynamicTextInputControlComponent extends BaseControlComponent implements OnInit {
  @Input() component: IComponent;
  @Input() toolTip: string;
  @Input() placeHolder: string;
  @Input() defaultValue: string | undefined;
  @Input() override label: string;
  @Input() backgroundColor = '#181818';
  @Input() itemBackgroundColor = '';
  @Input() hideQuillPersonalize = false;
  @Input() noPadding = false;
  @Input() noBorder = false;
  @Input() sidePanelPadding = false;
  @Input() showSelected = false;

  constructor(private signalService: ComponentUpdateSignalService) {
    super();
  }

  ngOnInit() {
    this.setDefaultValue();
  }

  setDefaultValue() {
    setTimeout(() => {
      if (this.defaultValue && !this.textValue) {
        this.textValue = this.defaultValue;
      }
    }, 1);
  }

  override setValue(input: string): void {
    this.writeValue(input);
    if (this.component && this.component?.id) {
      this.signalService.triggerSignal({ componentId: this.component.id, updateValue: input });
    }
  }
}
