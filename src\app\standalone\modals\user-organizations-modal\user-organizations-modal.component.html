<div class="organization-parent-container">
  @if (organizations.length > 1) {
    <h1 class="component-header">{{ name }}'s {{ 'Organizations and Product Roles' | translate | async }}</h1>
  }
  @if (organizations.length <= 1) {
    <h1 class="component-header">{{ name }}'s {{ 'Organization and Product Roles' | translate | async }}</h1>
  }
  <div class="accordion-container">
    <ion-content class="container">
      <mat-accordion class="accordions" multi="false">
        @for (organization of organizations; track organization; let isFirst = $first) {
          <div>
            <mat-expansion-panel (opened)="openGroup(true, organization.id)" (closed)="openGroup(false, organization.id)" [expanded]="isFirst">
              <mat-expansion-panel-header class="expansion-panel-header">
                <div class="inner-panel">
                  <div class="heading">{{ organization.name }}</div>
                </div>
                @if (featureType !== 'Organization Manager') {
                  <div class="inner-panel-role">
                    <div class="button-container">
                      <ion-button (click)="routeToOrg(organization.id)">{{ 'View' | translate | async }}</ion-button>
                    </div>
                  </div>
                }
              </mat-expansion-panel-header>
              @if (organization?.panelState === true) {
                <div class="content">
                  <mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start" (selectedTabChange)="tabSelected($event, organization.id)">
                    <mat-tab label="{{ 'ORGANIZATION ROLE' | translate | async }}">
                      <ng-template matTabContent>
                        <ion-grid>
                          <ion-row class="action-selectors-container">
                            <mat-radio-group class="action-checkbox">
                              @for (role of getOrganizationRoles(organization.id); track role) {
                                <mat-radio-button
                                  [color]="'primary'"
                                  [checked]="role.isSelected"
                                  [value]="role.isSelected"
                                  name="roleList"
                                  (change)="manageOrgRoleChange(organization.id, role.id, userId)">
                                  {{ role.name }}
                                </mat-radio-button>
                              }
                            </mat-radio-group>
                          </ion-row>
                        </ion-grid>
                      </ng-template>
                    </mat-tab>
                    <mat-tab label="{{ 'ORGANIZATION PRODUCTS' | translate | async }}">
                      <ng-template matTabContent>
                        <mat-accordion multi="false">
                          @for (product of organization.organizationProducts; track product) {
                            <mat-expansion-panel (opened)="openProductGroup(true, product)" (closed)="openProductGroup(false, product)">
                              <mat-expansion-panel-header class="expansion-panel-header">
                                <ion-col class="instance-checkbox" size="1">
                                  <mat-checkbox disabled [checked]="product.userHasProduct"></mat-checkbox>
                                </ion-col>
                                <ion-col size="12">
                                  <div class="inner-panel">
                                    <div class="instance-heading">{{ product.productName }}</div>
                                  </div>
                                </ion-col>
                              </mat-expansion-panel-header>
                              @if (product.panelState === true) {
                                <ion-grid>
                                  <ion-row class="action-selectors-container">
                                    <mat-radio-group class="action-checkbox">
                                      @for (role of getProductRoles(organization.id, product); track role) {
                                        <mat-radio-button
                                          [color]="'primary'"
                                          [checked]="role.isSelected"
                                          [value]="role.isSelected"
                                          name="roleList"
                                          (change)="manageProductOrgRoleChange(userId, role, product)">
                                          {{ role.name }}
                                        </mat-radio-button>
                                      }
                                    </mat-radio-group>
                                  </ion-row>
                                </ion-grid>
                              }
                            </mat-expansion-panel>
                          }
                          @if (featureType === 'User Repository') {
                            <div class="add-product">
                              <ion-row style="width: 100%">
                                <ion-col class="add">
                                  <ion-button class="addBtn" (click)="addProduct(organization.id)">{{ 'Add Product' | translate | async }}</ion-button>
                                </ion-col>
                              </ion-row>
                            </div>
                          }
                        </mat-accordion>
                      </ng-template>
                    </mat-tab>
                  </mat-tab-group>
                </div>
              }
            </mat-expansion-panel>
          </div>
        }
      </mat-accordion>
    </ion-content>
  </div>

  <div class="bottom-container">
    <ion-row style="width: 100%">
      <ion-col class="complete">
        <ion-button class="completeBtn" [disabled]="loading" (click)="complete()">{{ 'Complete' | translate | async }}</ion-button>
      </ion-col>
    </ion-row>
  </div>
</div>
