@if (content.joinCode) {
  <app-join-code [ngStyle]="{ left: layoutService.currentScreenSize === 'lg' ? '14px' : '0' }" [joinCode]="content.joinCode"></app-join-code>
}
<div appRowContentHover (rowContentHoverValuesChanged)="updateRowContentHoverValues($event)">
  <div
    role="button"
    aria-disabled="true"
    (click)="route()"
    (keydown)="route()"
    [style]="'--background-image:url(' + iconUrl + '); margin-top:2px; margin-bottom:6px'"
    [ngClass]="{
      'content portrait': thumbnailType === 'Portrait',
      'content landscape': thumbnailType !== 'Portrait',
      'org-background': content.entityType === entityTypes.Organizations,
      'background-layout': content.entityType !== entityTypes.Organizations,
      'landscape-gradient': thumbnailType === 'Landscape',
      'bigC-gradient': thumbnailType === 'Big content',
      'portrait-gradient': thumbnailType === 'Portrait',
    }">
    @if (!rowContentHoverValues?.showHover || isMobile) {
      <div class="gradient" [ngClass]="content.status && (content.status === 'InProgress' ? 'status-inprogress' : content.status === 'Completed' ? 'status-complete' : null)">
        @if (content?.achievementCompletion) {
          <div
            role="button"
            aria-disabled="true"
            (keydown)="openAchievementCompletionModalOnClick($event)"
            (click)="openAchievementCompletionModalOnClick($event)"
            class="completion-header-container"
            loading="lazy">
            @if (badgeImageIcon) {
              <span class="badge-icon" [ngStyle]="{ right: thumbnailType === 'Icon' ? '0px' : null }">
                <img ngSrc="{{ badgeImageIcon }}" alt fill="true" />
              </span>
            }
          </div>
        }
      </div>
    }
    <app-thumbnail-icons
      [row]="row"
      [content]="content"
      [instance]="instance"
      [readingMode]="readingMode"
      [isDraggable]="isDraggable"
      [isEducator]="isEducator"
      [canHover]="true"
      [onHover]="false"
      [isAssignmentRow]="isAssignmentRow"
      (contentRemoved)="emitContentRemoved()"
      (editCustomRowContent)="editContent()">
    </app-thumbnail-icons>
    <div class="text-container">
      @if (content.featureName) {
        <h5>{{ content.featureName | translate | async | uppercase }}</h5>
      }
      @if (content.instanceName) {
        <h1>{{ content.instanceName | translate | async }}</h1>
      }
      @if (content.entityType === entityTypes.Instances && content.instanceDescriptors) {
        <h6 [innerHTML]="content.instanceDescriptors ?? '' | parsePipe: content.id : null : false | async | translate | async"></h6>
      }
      @if (content.property && content.entityType === entityTypes.Organizations) {
        <h6>{{ content.property | translate | async }} &#8226; {{ content.property2 | translate | async }}</h6>
      }
      @if (content.entityType === entityTypes.DefaultInstance && content.featureDescriptors) {
        @if (content.displayObjectCount) {
          <div class="inline-descriptors">
            <h6>
              <span [innerHTML]="(content.featureDescriptors | parsePipe: content.id : null : false | async | translate | async) || ''"></span>
              <span>&nbsp; &#8226; {{ content.property2 + ' Items' | translate | async }}</span>
            </h6>
          </div>
        } @else {
          <h6 [innerHTML]="content.featureDescriptors ?? '' | parsePipe: content.id : null : false | async | translate | async"></h6>
        }
      } @else if (content.property2 && content.entityType !== entityTypes.Organizations) {
        <h6>{{ content.property2 + ' Items' | translate | async }}</h6>
      }
    </div>
  </div>
  @if ((content.status && !rowContentHoverValues?.showHover) || isMobileDevice) {
    <div class="status-foot-container" loading="lazy">
      @if (content.status === 'InProgress') {
        <span class="inner" style="background-color: orange">
          <span>{{ 'IN PROGRESS' | translate | async }}</span>
        </span>
      }
      @if (content.status === 'Completed') {
        <span class="inner" style="background-color: green">
          <span>{{ 'COMPLETED' | translate | async }}</span>
        </span>
      }
    </div>
  }

  <!-- Hover -->
  @if (rowContentHoverValues?.showHover && !isMobileDevice) {
    <div
      appRowContentHoverForeground
      [hoverValues]="rowContentHoverValues"
      [ngClass]="{ 'hover-item-org': content.entityType === 16, 'hover-item-default': content.entityType !== 16 }"
      [style]="'--background-image:url(' + iconUrl + ');'"
      class="hover-item">
      <div class="actions-heading">
        <ion-row>
          <app-thumbnail-icons
            [row]="row"
            [content]="content"
            [instance]="instance"
            [readingMode]="readingMode"
            [isDraggable]="isDraggable"
            [isEducator]="isEducator"
            [hasAdminAccess]="hasAdminAccess"
            [hideAddButton]="hideAddButton"
            [canHover]="true"
            [onHover]="true"
            [isAssignmentRow]="isAssignmentRow"
            (contentRemoved)="emitContentRemoved()"
            (editCustomRowContent)="editContent()">
          </app-thumbnail-icons>
        </ion-row>
      </div>
      <div role="button" aria-disabled="true" class="descriptions-heading bottom-row" (click)="route()" (keydown)="route()">
        <div class="gradient" [ngClass]="content.status && (content.status === 'InProgress' ? 'status-inprogress' : content.status === 'Completed' ? 'status-complete' : null)">
          @if (content?.achievementCompletion) {
            <div role="button" aria-disabled="true" class="badge-icon" (click)="openAchievementCompletionModalOnClick($event)" (keydown)="openAchievementCompletionModalOnClick($event)">
              <img ngSrc="{{ badgeImageIcon }}" fill="true" alt />
            </div>
          }
        </div>
        <!--Features-->
        @if (content.entityType === entityTypes.DefaultInstance) {
          <div class="descriptors-container">
            @if (content.featureName) {
              <h5>{{ content.featureName | translate | async | uppercase }}</h5>
            } @else if (content.instanceName) {
              <h1>{{ content.instanceName | translate | async }}</h1>
            }
            @if (content.featureDescriptors) {
              @if (content.displayObjectCount) {
                <div class="inline-descriptors">
                  <h6>
                    <span [innerHTML]="(content.featureDescriptors | parsePipe: content.id : null : false | async | translate | async) || ''"></span>
                    <span>&nbsp; &#8226; {{ content.property2 + ' Items' | translate | async }}</span>
                  </h6>
                </div>
              } @else {
                <h6 [innerHTML]="content.featureDescriptors ?? '' | parsePipe: content.id : null : false | async | translate | async"></h6>
              }
            }
            <div class="sub-description">{{ content.featureDescription | translate | async }}</div>
          </div>
        }
        <!--Instances-->
        @if (content.entityType === entityTypes.Instances) {
          <div class="descriptors-container">
            @if (content.instanceName) {
              <h1>{{ content.instanceName | translate | async }}</h1>
            }
            @if (content.instanceDescriptors) {
              <span class="inner-html" [innerHTML]="content.instanceDescriptors ?? '' | parsePipe: content.id : null : false | async | translate | async"></span>
            }
            <div class="sub-description">{{ content.instanceDescription | translate | async }}</div>
          </div>
        }
        @if ((content.actionBW !== actionTypes.View && content.entityType === entityTypes.Instances) || content.entityType === entityTypes.DefaultInstance) {
          <app-instance-section-hover-components [routeParams]="contentParams" [onlyHover]="true" [iconAssetId]="content.iconAssetId"></app-instance-section-hover-components>
        }
      </div>
      @if (content.status && rowContentHoverValues?.showHover) {
        <div class="status-foot-container" loading="lazy">
          @if (content.status === 'InProgress') {
            <span class="inner" style="background-color: orange"> {{ 'IN PROGRESS' | translate | async }} </span>
          }
          <div class="instance-tracker-container">
            @if (content.status === 'Completed') {
              <app-instance-tracking [instanceId]="content.id" [status]="content.status"></app-instance-tracking>
            }
          </div>
        </div>
      }
    </div>
  }
</div>
