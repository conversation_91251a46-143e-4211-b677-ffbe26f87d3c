import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IBadgeSearch } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { ModalController, IonicModule } from '@ionic/angular';
import { Observable, Subject, debounceTime, takeUntil } from 'rxjs';
import { AlertService } from '@app/core/services/alert-service';
import { SelectOptionControlComponent } from '@app/standalone/components/select-option-control/select-option-control.component';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { CommonModule, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-sync-badge-to-credential-engine-modal',
    templateUrl: './sync-badge-to-credential-engine-modal.component.html',
    styleUrls: ['./sync-badge-to-credential-engine-modal.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, CommonModule, SelectOptionControlComponent, AsyncPipe, TranslatePipe]
})
export class SyncBadgeToCredentialEngineModalComponent implements OnInit, OnDestroy {
  searchForm: UntypedFormGroup;
  searchValue: UntypedFormControl;

  showSyncedBadges = false;

  badges: IBadgeSearch[] = [];
  selectedBadges: IBadgeSearch[] = [];
  noBadgesInTable: boolean;

  // Select Status params
  controlBackground = '#181818';
  badgeStatusTypes: KeyValue[] = [
    { id: 'Active', value: 'Active' },
    { id: 'Deprecated', value: 'Deprecated' },
  ];

  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(
    private modalController: ModalController,
    private dataService: DataService,
    private alertService: AlertService
  ) {}

  ngOnInit() {
    this.createFormControls();
    this.createForm();
    this.setBadges();
  }

  createFormControls() {
    this.searchValue = new UntypedFormControl('');
  }

  createForm() {
    this.searchForm = new UntypedFormGroup({
      searchValue: this.searchValue,
    });
  }

  searchRepoValue() {
    this.setBadges();
  }

  toggleSyncButton() {
    this.searchForm.patchValue({ searchValue: '' });
    this.showSyncedBadges = !this.showSyncedBadges;
    this.setBadges();
  }

  updateStatus(status: string, instanceId: string) {
    this.dataService.upsertBadge(instanceId, status).pipe(takeUntil(this.componentDestroyed$)).subscribe();
  }

  setBadges() {
    this.badges = [];
    this.getBadges()
      .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
      .subscribe((badges: IBadgeSearch[]) => {
        if (badges) {
          this.noBadgesInTable = badges.length === 0;
          this.badges = badges;
        }
      });
  }

  getBadges(): Observable<IBadgeSearch[]> {
    if (this.showSyncedBadges) {
      return this.dataService.getSyncedBadges(this.searchValue.value);
    } else {
      return this.dataService.getUnsyncedOrNewBadges(this.searchValue.value);
    }
  }

  addAllBadgesToList(event: any, selectedBadges: IBadgeSearch[]) {
    if (event.detail.checked) {
      selectedBadges.map((badge: IBadgeSearch) => {
        if (!badge.selected) {
          badge.selected = true;
          this.selectedBadges.push(badge);
        }
      });
    } else {
      selectedBadges.map((badge: IBadgeSearch) => {
        badge.selected = false;
      });
      this.selectedBadges = [];
    }
  }

  addBadgeToList(event: any, selectedBadge: IBadgeSearch) {
    if (event.detail.checked) {
      selectedBadge.selected = true;
      this.selectedBadges.push(selectedBadge);
    } else {
      const index = this.selectedBadges.findIndex(x => x.id === selectedBadge.id);
      selectedBadge.selected = true;
      this.selectedBadges.splice(index, 1);
    }
  }

  close() {
    this.modalController.dismiss();
  }

  syncSelectedBadges() {
    if (this.selectedBadges.length == 0) return;
    this.dataService
      .bulkUpsertBadges(this.selectedBadges)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((errorMessages: string[]) => {
        if (errorMessages.length == 0) {
          this.alertService.presentAlert('Success', 'Badge(s) synced.');
          this.modalController.dismiss(this.selectedBadges);
        } else {
          const formattedErrors = errorMessages.join('\n\n');
          this.alertService.presentAlert('Badge Sync Errors', formattedErrors);
        }
      });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
