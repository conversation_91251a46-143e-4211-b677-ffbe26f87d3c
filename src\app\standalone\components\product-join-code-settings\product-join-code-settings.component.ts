import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { IProductJoinCodeSetting, IUserRole } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { map, Subject, takeUntil } from 'rxjs';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { IonicModule } from '@ionic/angular';
import { FormsModule } from '@angular/forms';

@Component({
    selector: 'app-product-join-code-settings',
    templateUrl: './product-join-code-settings.component.html',
    styleUrls: ['./product-join-code-settings.component.scss'],
    imports: [IonicModule, FormsModule, AsyncPipe, TranslatePipe]
})
export class ProductJoinCodeSettingsComponent implements OnInit, OnDestroy {
  @Input() productId: string;
  @Input() organizationId: string;
  @Input() productOrganizationId: string;
  @Input() userRoles: IUserRole[] = [];
  @Input() productJoinCodeSettings?: IProductJoinCodeSetting[] = [];
  @Output() productJoinCodesChange = new EventEmitter<any>();
  componentDestroyed$: Subject<boolean> = new Subject();
  backgroundColor = '#181818';
  users: KeyValue[];
  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.getProductJoinCodeSettings();
    this.dataService
      .getOrganizationTableUsersByOrgId(this.organizationId ?? '')
      .pipe(
        map(tags => {
          return tags.map(t => {
            return { id: t.id, value: t.name } as KeyValue;
          });
        })
      )
      .subscribe(data => {
        this.users = data;
      });
  }

  getProductJoinCodeSettings() {
    this.dataService
      .getProductJoinCodeSettings(this.productOrganizationId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        if (data && data.length > 0 && this.productJoinCodeSettings) {
          data.forEach(value => {
            const index: number = this.productJoinCodeSettings?.findIndex(x => x.typeName === value.typeName) ?? -1;
            if (index === -1) {
              this.productJoinCodeSettings?.push(value);
            } else if (this.productJoinCodeSettings) {
              this.productJoinCodeSettings[index] = value;
            }
          });
        }

        if (this.productJoinCodeSettings && !this.productJoinCodeSettings?.some(x => x.typeName === 'Product')) {
          this.productJoinCodeSettings.push({
            id: null,
            productOrganizationId: null,
            isApprovalRequired: false,
            isLearnerApprovalRequired: false,
            isGuestCode: false,
            isMonthlyRegenCode: false,
            isSelectUserNotificationCode: false,
            typeName: 'Product',
            isActive: false,
            users: [],
          } as IProductJoinCodeSetting);
        }

        if (this.productJoinCodeSettings && !this.productJoinCodeSettings?.some(x => x.typeName === 'Class')) {
          this.productJoinCodeSettings.push({
            id: null,
            productOrganizationId: null,
            isApprovalRequired: false,
            isLearnerApprovalRequired: false,
            isGuestCode: false,
            isMonthlyRegenCode: false,
            isSelectUserNotificationCode: false,
            typeName: 'Class',
            isActive: false,
            users: [],
          } as IProductJoinCodeSetting);
        }
      });
  }

  updateIsActive(event: any, joinCodeSettingsId: string) {
    if (this.productJoinCodeSettings) {
      const index = this.productJoinCodeSettings.findIndex(x => x.id === joinCodeSettingsId);

      if (index === -1) {
        return;
      }
      this.productJoinCodeSettings[index].isActive = event.checked;
    }
  }

  setProductJoinCodesChanged() {
    this.productJoinCodesChange.emit(null);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
