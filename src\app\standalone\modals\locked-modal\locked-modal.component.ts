import { Component } from '@angular/core';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { AuthService } from '@app/core/services/auth-service';
import { ModalController, IonicModule } from '@ionic/angular';
import { environment } from '@env/environment';
import { map, Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-locked-modal',
    templateUrl: './locked-modal.component.html',
    styleUrls: ['./locked-modal.component.scss'],
    imports: [IonicModule, AsyncPipe, TranslatePipe]
})
export class LockedModalComponent {
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private modalController: ModalController,
    public authService: AuthService
  ) {}

  confirm() {
    return this.modalController.dismiss(null, 'confirm');
  }

  openRegistration() {
    this.authService
      .setUserContext(true)
      .pipe(takeUntil(this.componentDestroyed$))
      .pipe(
        map(() => {
          let authorityUrl = '';
          if (this.authService.userContext?.country === 'Canada') {
            authorityUrl = environment.authority_ca;
          } else {
            authorityUrl = environment.authority_usa;
          }

          window.open(`${authorityUrl}/Account/Register?returnUrl=${window.location}`, '_self');
        })
      )
      .subscribe();
  }

  openAuth() {
    this.authService
      .setUserContext(true)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.authService.startAuthentication();
      });
  }
}
