import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { IOrganizationSsoAuthIn, IProductSetting, IUserRole } from '@app/core/contracts/contract';
import { AlertService } from '@app/core/services/alert-service';
import { DataService } from '@app/core/services/data-service';
import { forkJoin, Subject, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { MatAccordion, MatExpansionPanel, MatExpansionPanelHeader } from '@angular/material/expansion';
import { D2lPortalSettingsComponent } from '../d2l-portal-settings/d2l-portal-settings.component';
import { OrganizationScormSettingsComponent } from '../organization-scorm-settings/organization-scorm-settings.component';

@Component({
    selector: 'app-organization-settings',
    templateUrl: './organization-settings.component.html',
    styleUrls: ['./organization-settings.component.scss'],
    imports: [IonicModule, MatAccordion, MatExpansionPanel, MatExpansionPanelHeader, D2lPortalSettingsComponent, OrganizationScormSettingsComponent, AsyncPipe, TranslatePipe]
})
export class OrganizationSettingsComponent implements OnInit, OnDestroy {
  @Input() organizationId: string | null | undefined;
  userRoles: IUserRole[] = [];
  isParentPanelClosed = false;
  organizationSettings: IProductSetting[] = [];
  index = 0;

  orgSsoAuthIn: IOrganizationSsoAuthIn;
  hasChanges = false;
  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(
    private dataService: DataService,
    private alertService: AlertService
  ) {}

  ngOnInit() {
    this.getOrganizationSettings();
    this.getUserRoles();
  }

  getUserRoles() {
    this.dataService.getUserRoles().subscribe((userRoles: IUserRole[]) => {
      if (userRoles.length > 0) {
        this.userRoles = userRoles;
      }
    });
  }

  openGroup(panelState: boolean) {
    this.isParentPanelClosed = panelState;
  }

  getOrganizationSettings() {
    //CurrentStaticCategories
    const staticSettings: IProductSetting[] = [
      { id: '1', title: 'D2L | SSO', description: 'Allow users to sign in from their D2L portal', isActive: false },
      { id: '2', title: 'SCORM', description: '', isActive: false },
    ];

    this.organizationSettings = staticSettings;
  }

  toggleIsActive(selectedOrganizationSetting: IProductSetting, isActive: boolean) {
    if (isActive === true) {
      selectedOrganizationSetting.isActive = true;
    } else {
      selectedOrganizationSetting.isActive = false;
    }
  }

  setOrgSsoAuth(orgSsoAuthIn: IOrganizationSsoAuthIn) {
    this.hasChanges = true;
    this.orgSsoAuthIn = orgSsoAuthIn;
  }

  saveOrganizationSettings() {
    const requestList = [];

    if (this.orgSsoAuthIn != null) {
      requestList.push(this.dataService.updateOrgSsoAuth(this.orgSsoAuthIn));
    }

    forkJoin(requestList)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(result => {
        if (result) {
          this.showAlert('Organization settings updated!');
          this.hasChanges = false;
        }
      });
  }

  showAlert(alertText: string) {
    this.alertService.presentAlert('', alertText);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
