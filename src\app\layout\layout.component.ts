import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Compo<PERSON>, On<PERSON>estroy, OnInit, ViewChild } from '@angular/core';
import { IOrganizationLite } from '@app/core/contracts/contract';
import { PendingRequestsInterceptor } from '@app/core/interceptors/pending-requests-interceptor';
import { AlertService } from '@app/core/services/alert-service';
import { AuthService } from '@app/core/services/auth-service';
import { BannerService } from '@app/core/services/banner.service';
import { DataService } from '@app/core/services/data-service';
import { GoogleTranslateService } from '@app/core/services/google-translate.service';
import { InstanceService } from '@app/core/services/instance-service';
import { JoinCodeService } from '@app/core/services/join-code.service';
import { LayoutService } from '@app/core/services/layout-service';
import { NotificationService } from '@app/core/services/notification-service';
import { StorageService } from '@app/core/services/storage-service';
import { UnsavedChangesGuard } from '@app/core/services/unsaved-changes.guard';
import { SettingsPopoverComponent } from '@app/standalone/components/settings-popover/settings-popover';
import { LockedModalComponent } from '@app/standalone/modals/locked-modal/locked-modal.component';
import { environment } from '@env/environment';
import { IonSearchbar, MenuController, ModalController, NavController, PopoverController } from '@ionic/angular';
import { Observable, Subject } from 'rxjs';
import { delay, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-layout-public',
  styleUrls: ['./layout.component.scss'],
  templateUrl: './layout.component.html',
  standalone: false,
})
export class LayoutComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('globalSearchText') searchbar: IonSearchbar;
  componentDestroyed$: Subject<boolean> = new Subject();
  organizations: IOrganizationLite[] = [];
  appVersion: string = environment.version;
  isLoading$: Observable<boolean>;
  showSearch = false;
  returnUrl: string;
  loginLoader = false;
  featureClicked: string;
  currentNotificationFetchCheckpoint: number = 0;
  showLanguageSelector = false;
  currentLanguage = 'en'; // Default language

  constructor(
    public layoutService: LayoutService,
    public authService: AuthService,
    public notificationService: NotificationService,
    public translateService: GoogleTranslateService,
    private dataService: DataService,
    private storageService: StorageService,
    private pendingRequestsInterceptor: PendingRequestsInterceptor,
    private popoverController: PopoverController,
    private navController: NavController,
    private modalController: ModalController,
    private menuController: MenuController,
    private joinCodeService: JoinCodeService,
    private instanceService: InstanceService,
    private unsavedChangesGuard: UnsavedChangesGuard,
    private bannerService: BannerService,
    private alertService: AlertService
  ) {}

  ngOnInit() {
    if (this.authService.user) {
      this.dataService
        .getMyOrganizations()
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(org => {
          this.organizations = org;
        });
      this.notificationService.startConnection(this.authService.user.access_token);
      this.notificationService.newNotification.subscribe(() => {
        if (this.notificationService.notifications?.length > 0) {
          this.menuController.open('notifications');
        }
      });
    }
  }

  clearPopup() {
    this.menuController.close('notifications');
  }

  checkNotificationsInView() {
    const notificationsListElement = document.getElementById('notificationsList');
    const notificationsList = document.getElementById('notificationsList')?.querySelectorAll('ion-item') as NodeListOf<HTMLIonItemElement> | undefined;

    if (notificationsListElement && notificationsList) {
      notificationsList.forEach((notification: Element, index: number) => {
        const notificationItemRect = notification.getBoundingClientRect();

        if (notificationItemRect.top >= 0 && notificationItemRect.bottom <= window.innerHeight) {
          const currentNotification = this.notificationService.notifications[index];

          if (currentNotification && index + 1 == notificationsList.length && this.currentNotificationFetchCheckpoint !== index + 1) {
            // grab older notifications
            this.currentNotificationFetchCheckpoint = index + 1;

            this.dataService
              .getNotifications(notificationsList.length)
              .pipe()
              .subscribe(notificationData => {
                this.notificationService.notifications = this.notificationService.notifications.concat(notificationData);
              });
          }
        }
      });
    }
  }

  notificationListScrollEvent() {
    this.checkNotificationsInView();
  }

  getFirstValue(value: string | undefined) {
    if (value) {
      return value.toString().split(',')[0];
    } else {
      return undefined;
    }
  }

  ngAfterViewInit() {
    this.isLoading$ = this.pendingRequestsInterceptor.PendingRequestsStatus$.pipe(delay(0));
  }

  openAuth() {
    this.loginLoader = true;
    this.authService
      .setUserContext(false)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.authService.startAuthentication();
      });
  }

  openJoin() {
    this.joinCodeService.openJoin(false);
  }

  async presentSettingsPopover(ev: any) {
    const popover = await this.popoverController.create({
      component: SettingsPopoverComponent,
      cssClass: 'settings-popover',
      componentProps: {
        managedOrganization: this.organizations?.filter(x => x.actionBw > 0),
      },
      event: ev,
    });
    await popover.present();

    const { data } = await popover.onDidDismiss();
    switch (data?.value) {
      case 'info': {
        this.instanceService.openInstance('my-journey', null, 'myinfo', null);
        break;
      }

      case 'organization': {
        this.instanceService.openInstance('my-organization', data.id, null, null, null, true);
        break;
      }

      case 'logout': {
        this.authService
          .endAuthentication()
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(() => {
            this.navController.navigateRoot('');
          });

        break;
      }
      default: {
        break;
      }
    }
  }

  async openLockedModalOnClick() {
    this.showSearch = false;
    const modal = await this.modalController.create({
      component: LockedModalComponent,
      cssClass: 'completion-modal',
    });

    await modal.present();
  }

  setGlobalSearchValueLG(value: any) {
    if (value && value.length >= 3) {
      this.storageService.initialSearchVal = value;
      this.storageService.globalSearchValue$.next(value);
      this.searchbar.value = '';
      this.instanceService.openInstance(`global-search`);
    }
  }

  setGlobalSearchValueXS(value: any) {
    if (value && value.length >= 3) {
      this.storageService.initialSearchVal = value;
      this.storageService.globalSearchValue$.next(value);
      this.instanceService.openInstance(`global-search`);
      this.showSearch = false;
    }
  }

  toggleNotifications() {
    this.menuController.isOpen().then(result => {
      if (result) {
        this.menuController.close();
        this.notificationService.notifications.length = 0;
      } else {
        if (this.notificationService.notifications.length == 0) {
          this.notificationService
            .loadNotifications()
            .pipe(takeUntil(this.componentDestroyed$))
            .subscribe(() => {
              this.menuController.open('notifications');
            });
        } else {
          this.menuController.open('notifications');
        }
      }
    });
  }

  removeNotification(id: string) {
    this.notificationService.completeNotification(id);
  }

  openFeature(featureSlug: string) {
    this.showSearch = false;
    if (!this.unsavedChangesGuard.canDeactivateVar) {
      this.alertService.leaveBuilderAlert('Leave the builder?', "Don't worry, your changes will be autosaved and published.").then(() => {
        this.unsavedChangesGuard.canDeactivateVar = true;
        setTimeout(() => {
          this.featureClicked = featureSlug;
          this.instanceService.openInstance(featureSlug, null, null, null, null, true);
        }),
          5000;
      });
    } else {
      this.featureClicked = featureSlug;
      this.instanceService.openInstance(featureSlug, null, null, null, null, true);
    }
  }

  selectedTab(value: string) {
    this.featureClicked = value;
  }

  toggleSearchbar() {
    this.showSearch = !this.showSearch;
    if (this.showSearch) {
      setTimeout(() => {
        this.searchbar.setFocus();
      }, 150);
    }
  }

  removeBrowsingAs() {
    localStorage.removeItem('guest_context');
    this.authService.guestUserContext = null;
    window.location.reload();
  }

  toggleLanguageSelector(event: Event) {
    event.stopPropagation();
    this.showLanguageSelector = !this.showLanguageSelector;
  }

  selectLanguage(languageCode: string) {
    this.currentLanguage = languageCode;
    this.showLanguageSelector = false;

    console.log('Selected language in layout:', languageCode);
    // Store the selected language in localStorage for persistence
    localStorage.setItem('selectedLanguage', languageCode);
    // Update the signal with the new language
    this.storageService.selectedLanguage.set(languageCode);
    
    // Force refresh of content by triggering a change detection cycle
    setTimeout(() => {
      console.log('Language selection applied');
    }, 0);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
