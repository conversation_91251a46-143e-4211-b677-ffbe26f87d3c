@if (status === 'private') {
  <div class="parent-container">
    <ion-reorder-group (ionItemReorder)="setCriteriaRowSortOrderDirect($event)" disabled="false">
      @for (criteria of earningCriteria; track criteria) {
        @if (criteria.isDeleted !== true) {
          <ion-card class="parent-card-container">
            <ion-row>
              <ion-col class="inner-col" size="12">
                <div class="content-container">
                  @if (criteria.earningCriteriaType.name === 'CompleteObjects') {
                    <div class="content">
                      <app-completed-objects [earningCriteria]="criteria" [type]="criteria.earningCriteriaType.name" (criteriaUpdated)="setCriteriaUpdated($event, criteria)"></app-completed-objects>
                    </div>
                  }
                  @if (criteria.earningCriteriaType.name === 'CompletePage') {
                    <div class="content">
                      <app-complete-page [earningCriteria]="criteria" [type]="criteria.earningCriteriaType.name" (criteriaUpdated)="setCriteriaUpdated($event, criteria)"></app-complete-page>
                    </div>
                  }
                  @if (criteria.earningCriteriaType.name === 'CompleteContent') {
                    <div class="content">
                      <app-complete-content [earningCriteria]="criteria" [type]="criteria.earningCriteriaType.name" (criteriaUpdated)="setCriteriaUpdated($event, criteria)"></app-complete-content>
                    </div>
                  }
                  @if (criteria.earningCriteriaType.name === 'CompleteAtLeast') {
                    <div class="content">
                      <app-complete-atleast [earningCriteria]="criteria" [type]="criteria.earningCriteriaType.name" (criteriaUpdated)="setCriteriaUpdated($event, criteria)"></app-complete-atleast>
                    </div>
                  }
                  @if (criteria.earningCriteriaType.name === 'CreateAtLeast') {
                    <div class="content">
                      <app-create-atleast [earningCriteria]="criteria" [type]="criteria.earningCriteriaType.name" (criteriaUpdated)="setCriteriaUpdated($event, criteria)"></app-create-atleast>
                    </div>
                  }
                  @if (criteria.earningCriteriaType.name === 'CompleteCreatedBy') {
                    <div class="content">
                      <app-complete-created-by
                        [earningCriteria]="criteria"
                        [type]="criteria.earningCriteriaType.name"
                        (criteriaUpdated)="setCriteriaUpdated($event, criteria)"></app-complete-created-by>
                    </div>
                  }
                  @if (criteria.earningCriteriaType.name === 'ComponentSubType') {
                    <div class="content">
                      <app-complete-sub-type [earningCriteria]="criteria" [type]="criteria.earningCriteriaType.name" (criteriaUpdated)="setCriteriaUpdated($event, criteria)"></app-complete-sub-type>
                    </div>
                  }
                  @if (criteria.earningCriteriaType.name === 'CompleteDuringMonth') {
                    <div class="content">
                      <app-complete-during-month
                        [earningCriteria]="criteria"
                        [type]="criteria.earningCriteriaType.name"
                        (criteriaUpdated)="setCriteriaUpdated($event, criteria)"></app-complete-during-month>
                    </div>
                  }
                  @if (criteria.earningCriteriaType.name === 'CompletePublishedWithin') {
                    <div class="content">
                      <app-complete-published-within
                        [earningCriteria]="criteria"
                        [type]="criteria.earningCriteriaType.name"
                        (criteriaUpdated)="setCriteriaUpdated($event, criteria)"></app-complete-published-within>
                    </div>
                  }
                  @if (criteria.earningCriteriaType.name === 'EarnXpPoints') {
                    <div class="content">
                      <app-earn-xp-points [earningCriteria]="criteria" [type]="criteria.earningCriteriaType.name" (criteriaUpdated)="setCriteriaUpdated($event, criteria)"></app-earn-xp-points>
                    </div>
                  }
                  @if (criteria.earningCriteriaType.name === 'AssessmentProficiency') {
                    <div class="content">
                      <app-assessment-proficiency
                        [earningCriteria]="criteria"
                        [type]="criteria.earningCriteriaType.name"
                        (criteriaUpdated)="setCriteriaUpdated($event, criteria)"></app-assessment-proficiency>
                    </div>
                  }
                  @if (criteria.earningCriteriaType.name === 'CompleteStartedWithin') {
                    <div class="content"><app-complete-started-within></app-complete-started-within></div>
                  }
                </div>
                <div class="remove-button-container">
                  <ion-icon (click)="removeEarningCriteria(criteria)" name="remove-circle-outline"></ion-icon>
                </div>
                <div class="drag-button-container">
                  <ion-reorder>
                    <ion-icon name="apps-outline"></ion-icon>
                  </ion-reorder>
                </div>
              </ion-col>
            </ion-row>
          </ion-card>
        }
      }
    </ion-reorder-group>
    <ion-row>
      <ion-col size="3" class="add-button-col">
        <ng-container>
          <div class="add-button-container">
            <mat-icon (click)="addEarningCriteriaPopOver($event)">add_circle_outline</mat-icon>
            <div class="button-text">{{ 'Add earning criteria' | translate | async }}</div>
          </div>
        </ng-container>
      </ion-col>
    </ion-row>
  </div>
} @else {
  @if (instanceId) {
    <div class="parent-container-view">
      <app-earning-criteria [instanceId]="instanceId" [isCriteriaManagerView]="true"></app-earning-criteria>
    </div>
  }
}
