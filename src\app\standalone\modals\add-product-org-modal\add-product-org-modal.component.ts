import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { AsyncPipe } from '@angular/common';
import { UntypedFormControl, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IOrganizationSearch } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { ModalController, IonicModule } from '@ionic/angular';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { debounceTime, Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-add-product-org-modal',
    templateUrl: './add-product-org-modal.component.html',
    styleUrls: ['./add-product-org-modal.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, AsyncPipe, TranslatePipe]
})
export class AddProductOrganizationModalComponent implements OnInit, OnDestroy {
  @Input() id: string;
  searchForm: UntypedFormGroup;
  orgSearch: UntypedFormControl;
  organizations: IOrganizationSearch[] = [];
  selectedOrgs: IOrganizationSearch[] = [];
  currentAmount = 0;
  getAmount = 25;

  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(
    private modalController: ModalController,
    private dataService: DataService
  ) {}

  ngOnInit() {
    this.createFormControls();
    this.createForm();
  }

  createFormControls() {
    this.orgSearch = new UntypedFormControl('');
  }

  createForm() {
    this.searchForm = new UntypedFormGroup({
      orgSearch: this.orgSearch,
    });
  }

  searchOrganizationById() {
    if (this.orgSearch.value !== '') {
      this.dataService
        .searchOrganizationByProductId(this.id, this.orgSearch.value, this.currentAmount, this.getAmount)
        .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
        .subscribe((organizations: IOrganizationSearch[]) => {
          this.organizations = organizations;
        });
    }
  }

  addOrgToList(event: any, selectedOrg: IOrganizationSearch) {
    if (event.detail.checked) {
      selectedOrg.selected = true;
      this.selectedOrgs.push(selectedOrg);
    } else {
      const index = this.selectedOrgs.findIndex(x => x.id === selectedOrg.id);
      selectedOrg.selected = true;
      this.selectedOrgs.splice(index, 1);
    }
  }

  close() {
    this.modalController.dismiss();
  }

  add() {
    this.modalController.dismiss(this.selectedOrgs);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
