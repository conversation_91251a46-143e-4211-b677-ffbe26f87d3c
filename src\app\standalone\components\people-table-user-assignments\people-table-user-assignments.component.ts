import { Component, Input, On<PERSON><PERSON>roy, OnIni<PERSON> } from '@angular/core';
import { <PERSON><PERSON><PERSON>, MatSortHeader } from '@angular/material/sort';
import { <PERSON><PERSON><PERSON>, MatCellDef, MatColumnDef, Mat<PERSON>eader<PERSON>ell, MatHeaderCellDef, MatRow, MatRowDef, MatTable, MatTableDataSource } from '@angular/material/table';
import { IInstance, IInstanceResult } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { environment } from '@env/environment';
import { IonicModule } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';
import { AsyncPipe, NgClass } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-people-table-user-assignments',
    imports: [IonicModule, MatTable, MatSort, Mat<PERSON><PERSON>umnDef, <PERSON><PERSON><PERSON>er<PERSON><PERSON>Def, <PERSON><PERSON><PERSON>er<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>owD<PERSON>, Mat<PERSON>ow, Ng<PERSON>lass, AsyncPipe, TranslatePipe],
    templateUrl: './people-table-user-assignments.component.html',
    styleUrl: './people-table-user-assignments.component.scss'
})
export class PeopleTableUserAssignmentsComponent implements OnInit, OnDestroy {
  @Input() instance: IInstance;
  @Input() userId: string;
  dataSource = new MatTableDataSource<IInstanceResult>();
  displayedColumns: string[] = ['icon', 'name', 'grade-status', 'progress', 'grade'];
  componentDestroyed$: Subject<boolean> = new Subject();
  height = 300;

  constructor(
    private dataService: DataService,
    private instanceService: InstanceService
  ) {}

  ngOnInit(): void {
    this.dataService
      .getPeopleTableAssignmentsByInstanceId(this.instance.id, this.userId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        this.dataSource = new MatTableDataSource(data);
      });
  }

  getCoverUrl(coverMediaAssetId: string) {
    return `${environment.contentUrl}asset/${coverMediaAssetId}/content${this.height ? '?height=' + this.height : ''}`;
  }

  navigateToInstance(instanceId: string) {
    sessionStorage.setItem('showGradingView', 'true');
    if (this.instance?.feature?.featureType?.name === 'Modifiable Learning Container Pages') {
      this.instanceService.openInstance(instanceId, null, null, 'player');
    } else {
      this.instanceService.openInstance(this.instance.id, null, null, 'player', null, true);
    }
  }

  getStatusText(status: string): string {
    switch (status) {
      case 'InProgress':
        return 'IN PROGRESS';
      case 'Completed':
        return 'COMPLETED';
      case 'GradingRequired':
        return 'GRADING REQUIRED';
      default:
        return 'NOT STARTED';
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
