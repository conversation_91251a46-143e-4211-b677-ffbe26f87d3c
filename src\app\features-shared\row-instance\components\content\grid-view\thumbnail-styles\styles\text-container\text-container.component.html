<div class="parent-container" appRowContentHover (rowContentHoverValuesChanged)="updateRowContentHoverValues($event)">
  <app-thumbnail-icons
    [row]="row"
    [content]="content"
    [instance]="instance"
    [readingMode]="readingMode"
    [isDraggable]="isDraggable"
    [isEducator]="isEducator"
    [hasAdminAccess]="hasAdminAccess"
    [hideAddButton]="hideAddButton"
    [canHover]="true"
    [onHover]="false"
    [isAssignmentRow]="isAssignmentRow"
    (contentRemoved)="emitContentRemoved()"
    (editCustomRowContent)="editContent()">
  </app-thumbnail-icons>
  @if (!rowContentHoverValues?.showHover) {
    <div
      class="parent-layout"
      style="--background-image: url('{{ imageUrl }}')"
      [ngClass]="{
        'status-inprogress': content.status === 'InProgress',
        'status-complete': content.status === 'Completed',
      }">
      <div class="content-area">
        <div class="title">
          {{ content.instanceName | translate | async }}
        </div>
        <div class="description">{{ content.instanceDescription | translate | async }}</div>
        @if (!content.action) {
          <div class="button-container">
            @if (content.actionUrl !== undefined) {
              <ion-button [title]="content.actionUrl" (click)="route()"> {{ 'OPEN NOW' | translate | async }} </ion-button>
            }
          </div>
        } @else if (content.action) {
          <div class="button-container">
            <ion-button [title]="content.actionUrl" (click)="route()">
              {{ content.action | translate | async }}
            </ion-button>
          </div>
        }
        @if (content.status) {
          <div class="status-foot-container" loading="lazy">
            @if (content.status === 'InProgress') {
              <span class="inner" style="background-color: orange">
                <span>{{ 'IN PROGRESS' | translate | async }}</span>
              </span>
            }
            @if (content.status === 'Completed') {
              <span class="inner" style="background-color: green">
                <span>{{ 'COMPLETED' | translate | async }}</span>
              </span>
            }
          </div>
        }
        @if (content?.achievementCompletion) {
          <div role="button" aria-disabled="true" class="badge-icon" (click)="openAchievementCompletionModalOnClick($event)" (keydown)="openAchievementCompletionModalOnClick($event)">
            <img ngSrc="{{ badgeImageIcon }}" fill="true" alt />
          </div>
        }
      </div>
    </div>
  }

  @if (rowContentHoverValues?.showHover) {
    <div
      appRowContentHoverForeground
      [hoverValues]="rowContentHoverValues"
      style="--background-image: url('{{ imageUrl }}')"
      [ngClass]="{
        'status-inprogress': content.status === 'InProgress',
        'status-complete': content.status === 'Completed',
        'hover-item-org': content.entityType === entityTypes.Organizations,
        'hover-item-default': content.entityType !== entityTypes.Organizations,
      }"
      class="parent-layout hover-item background-layout">
      <div class="actions-heading">
        <app-thumbnail-icons
          [row]="row"
          [content]="content"
          [instance]="instance"
          [readingMode]="readingMode"
          [isDraggable]="isDraggable"
          [isEducator]="isEducator"
          [hasAdminAccess]="hasAdminAccess"
          [hideAddButton]="hideAddButton"
          [canHover]="true"
          [onHover]="true"
          [isAssignmentRow]="isAssignmentRow"
          (contentRemoved)="emitContentRemoved()"
          (editCustomRowContent)="editContent()">
        </app-thumbnail-icons>
        <div class="tracking">
          <div class="tracking-heading">
            <app-instance-tracking [instanceId]="content.id" [status]="content.status"></app-instance-tracking>
          </div>
        </div>
        <div class="content-area">
          <div class="title">
            {{ content.instanceName | translate | async }}
          </div>
          <div class="description-hover">{{ content.instanceDescription | translate | async }}</div>
          @if (!content.action) {
            <div class="button-container">
              @if (content.actionUrl !== undefined) {
                <ion-button [title]="content.actionUrl" (click)="route()"> {{ 'OPEN NOW' | translate | async }} </ion-button>
              }
            </div>
          } @else if (content.action) {
            <div class="button-container">
              <ion-button [title]="content.actionUrl" (click)="route()">
                {{ content.action | translate | async }}
              </ion-button>
            </div>
          }
          @if (content.status) {
            <div class="status-foot-container" loading="lazy">
              @if (content.status === 'InProgress') {
                <span class="inner" style="background-color: orange">
                  <span>{{ 'IN PROGRESS' | translate | async }}</span>
                </span>
              }
              @if (content.status === 'Completed') {
                <span class="inner" style="background-color: green">
                  <span>{{ 'COMPLETED' | translate | async }}</span>
                </span>
              }
            </div>
          }
          @if (content?.achievementCompletion) {
            <div role="button" aria-disabled="true" class="badge-icon" (click)="openAchievementCompletionModalOnClick($event)" (keydown)="openAchievementCompletionModalOnClick($event)">
              <img ngSrc="{{ badgeImageIcon }}" fill="true" alt />
            </div>
          }
        </div>
      </div>
    </div>
  }
</div>
