<div class="quill-edit-parent-container">
  <quill-editor
    conveythis-no-translate
    customToolbarPosition="bottom"
    trackChanges="all"
    [(ngModel)]="value"
    format="html"
    (keyup)="onKeyUp()"
    (onContentChanged)="onContentChanged($event)"
    (onEditorCreated)="getEditorInstance($event)"
    [placeholder]="placeHolder"
    [debounceTime]="1000"
    [maxLength]="100000000"
    [readOnly]="disabled">
    <div class="quill-toolbar" quill-editor-toolbar>
      <span class="ql-formats">
        <button class="ql-bold" [title]="'bold' | translate | async"></button>
        <button class="ql-italic" [title]="'italic' | translate | async"></button>
        <button class="ql-underline" [title]="'underline' | translate | async"></button>
        <button class="ql-strike" [title]="'strikethrough' | translate | async"></button>
        <button class="ql-list" value="ordered" [title]="'ordered list' | translate | async"></button>
        <button class="ql-list" value="bullet" [title]="'bullet list' | translate | async"></button>
        <select class="ql-color" [title]="'color picker' | translate | async"></select>
        <button class="ql-link" [title]="'link' | translate | async"></button>
        <select class="ql-align"></select>
        <select class="ql-header">
          <option value="1">{{ 'Heading' | translate | async }}</option>
          <option value="2">{{ 'Subheading' | translate | async }}</option>
          <option value>{{ 'Normal Text' | translate | async }}</option>
        </select>
      </span>
      @if (!hideQuillPersonalize) {
        <span class="ql-formats">
          <button class="custom-button" (click)="personalizeContent()">{{ 'Personalize' | translate | async }}</button>
        </span>
      }
    </div>
  </quill-editor>
  @if (charCount > 0) {
    <span class="text-counter">{{ charCount }}</span>
  }
</div>
