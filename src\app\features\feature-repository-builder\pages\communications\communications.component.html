<form [formGroup]="communicationsForm">
  <ion-grid>
    <ion-row>
      <ion-col size="11" class="ion-padding-start">
        <p>{{ 'Manage the transactional communications that users receive about his feature.' | translate | async }}</p>
      </ion-col>
      <ion-col>
        @if (showSave) {
          <ion-button fill="clear" color="primary" (click)="saveCommunication()">{{ 'Save' | translate | async }}</ion-button>
        }
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-card>
          <ion-card-header>
            <ion-card-title>{{ 'Author Communications' | translate | async }}</ion-card-title>
            <ion-card-subtitle>{{ 'Manage the notifications an author or publisher will receive' | translate | async }}</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <mat-slide-toggle style="width: 100%" color="primary" formControlName="authorInstanceCompletion">{{ 'Reminder to finish completing an instance.' | translate | async }}</mat-slide-toggle>
            <mat-slide-toggle style="width: 100%" color="primary" formControlName="authorInstanceFeedback">{{ 'Notify when someone provides feedback on an instance.' | translate | async }}</mat-slide-toggle>
            <mat-slide-toggle style="width: 100%" color="primary" formControlName="authorInstanceLive">{{ 'Notify when the instance is live on the platform.' | translate | async }}</mat-slide-toggle>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-card>
          <ion-card-header>
            <ion-card-title>{{ 'User Communications' | translate | async }}</ion-card-title>
            <ion-card-subtitle>{{ 'Manage the notifications an user will receive' | translate | async }}</ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <mat-slide-toggle style="width: 100%" color="primary" formControlName="userInstanceCompletion">{{ 'Remind the user to finish completing this instance' | translate | async }}</mat-slide-toggle>
            <mat-slide-toggle style="width: 100%" color="primary" formControlName="userAchievement">{{ 'Notify the user when they earn an achievement from this instance' | translate | async }}</mat-slide-toggle>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
  </ion-grid>
</form>
