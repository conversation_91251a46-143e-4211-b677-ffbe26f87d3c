<div class="parent-container">
  <ion-grid>
    <ion-row class="top-heading-row">
      <h1>{{ 'Password' | translate | async }}</h1>
    </ion-row>
    <ion-row class="inner-container">
      <ion-col class="icon-col" size="*">
        <ion-icon name="lock-closed"></ion-icon>
      </ion-col>
      <ion-col class="password-content-col">
        <app-text-value [defaultValue]="'**********'"></app-text-value>
      </ion-col>
    </ion-row>
  </ion-grid>
</div>
