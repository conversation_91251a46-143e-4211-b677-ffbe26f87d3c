<div class="layout" [style]="'--background-image:url(' + imageUrl + ');'">
  <ion-row>
    <app-thumbnail-icons
      [row]="row"
      [content]="content"
      [instance]="instance"
      [readingMode]="readingMode"
      [isEducator]="isEducator"
      [hasAdminAccess]="hasAdminAccess"
      [hideAddButton]="hideAddButton"
      [isDraggable]="isDraggable"
      [canHover]="false"
      [isAssignmentRow]="isAssignmentRow"
      (contentRemoved)="emitContentRemoved()"
      (editCustomRowContent)="editContent()">
    </app-thumbnail-icons>
  </ion-row>
  <h1>{{ instanceName | translate | async | uppercase }}</h1>
  @if (action) {
    <div>
      @if (actionUrl !== undefined) {
        <ion-button (click)="route()">
          {{ action | translate | async }}
        </ion-button>
      }
    </div>
  }
</div>
