@if (completeDuringMonthForm) {
  <form [formGroup]="completeDuringMonthForm">
    <div class="parent-container">
      <ion-row>
        <ion-col size="auto" class="heading-col">{{ 'Complete the assigned resources during the month of:' | translate | async }}</ion-col>
        <ion-col size="auto">
          <mat-form-field color="primary" appearance="fill">
            <mat-label>{{ 'Enter a date range' | translate | async }}</mat-label>
            <mat-date-range-input [rangePicker]="picker">
              <input formControlName="minValue" matStartDate placeholder="{{ 'Start date' | translate | async }}" />
              <input formControlName="maxValue" matEndDate placeholder="{{ 'End date' | translate | async }}" />
            </mat-date-range-input>
            <mat-datepicker-toggle color="primary" matIconSuffix [for]="picker"></mat-datepicker-toggle>
            <mat-date-range-picker color="primary" #picker></mat-date-range-picker>
          </mat-form-field>
        </ion-col>
      </ion-row>
    </div>
  </form>
}
