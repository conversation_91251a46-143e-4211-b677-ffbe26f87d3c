<ion-grid class="parent-container">
  @if (!finished) {
    <div class="top-header-container centered">
      <ion-grid>
        <ion-row>
          <ion-col class="centered" size="12">
            <img alt="" class="ef_logo" src="assets/images/EdgeFactor_small-white.png" />
          </ion-col>
        </ion-row>
        @if (!this.selectedUserId) {
          <ion-row>
            @if (tagTreeLevel === 2) {
              <ion-col class="centered" size="12">
                <ion-label class="gs-modal-title">{{ 'Welcome' | translate | async }} {{ userName }}!</ion-label>
              </ion-col>
            }
          </ion-row>
        }
        @if (!this.selectedUserId) {
          <ion-row>
            @if (tagTreeLevel === 2) {
              <ion-col class="centered" size="12">
                <ion-label class="description-label-welcome">{{ 'Before you get started, let\'s ensure you\'re seeing the correct content. What best describes you?' | translate | async }}</ion-label>
              </ion-col>
            }
          </ion-row>
        }
        @if (!this.selectedUserId) {
          <ion-row>
            @if (tagTreeLevel === 2) {
              <ion-col class="centered" size="12">
                <ion-label class="description-label-info">{{ '*Usually takes people less than 30 seconds' | translate | async }}</ion-label>
              </ion-col>
            }
          </ion-row>
        }
        <ion-row>
          @if (tagTreeLevel !== 2 && !this.selectedUserId) {
            <ion-col class="centered" size="12">
              <ion-label class="description-label">{{ userMessage | translate | async }}</ion-label>
            </ion-col>
          }
        </ion-row>
      </ion-grid>
    </div>
  }

  @if (!finished) {
    <div class="group-container">
      @for (tag of tags; track tag) {
        <ion-item-group class="property">
          <ion-item class="heading-container">
            <ion-grid>
              @if (tag?.parent?.name !== 'K-8 Educator' && tag?.parent?.name !== 'High School Educator' && tag?.parent?.name !== 'School District Lead') {
                <ion-row class="checkbox-col" (click)="setRow(tag)">
                  <ion-col size="12">
                    @if (tagTreeLevel === 2 && (tag?.name === 'K-8 Educator' || tag?.name === 'High School Educator' || tag?.name === 'School District Lead')) {
                      <div class="heading">I work as a {{ tag?.name }}</div>
                    }
                    @if (tagTreeLevel === 2 && tag?.name === 'Company') {
                      <div class="heading">{{ 'I work at a' | translate | async }} {{ tag?.name }}</div>
                    }
                    @if (tagTreeLevel === 2 && (tag?.name === 'Jobseeker' || tag?.name === 'Student' || tag?.name === 'Parent')) {
                      <div class="heading">{{ 'I\'m a' | translate | async }} {{ tag?.name }}</div>
                    }
                    @if (tagTreeLevel === 2 && tag?.name === 'Postsecondary Educator') {
                      <div class="heading">I work as a {{ tag?.name }}</div>
                    }
                    @if (tagTreeLevel === 2 && tag?.name === 'Workforce Development Leader') {
                      <div class="heading">I work as a {{ tag?.name }}</div>
                    }
                    @if (
                      tagTreeLevel !== 2 &&
                      tag?.parent?.name !== 'CTE Instructor' &&
                      tag?.parent?.name !== 'SHSM Instructor' &&
                      tag?.parent?.name !== 'Technology Teacher' &&
                      tag?.parent?.name !== 'TAS Teacher'
                    ) {
                      <div class="heading">{{ tag?.name }}</div>
                    }
                  </ion-col>
                </ion-row>
              }
              @if (
                tagTreeLevel >= 3 &&
                tag?.name !== 'CTE Instructor' &&
                tag.name !== 'SHSM Instructor' &&
                tag.name !== 'Technology Teacher' &&
                tag.name !== 'TAS Teacher' &&
                (tag?.parent?.name === 'K-8 Educator' ||
                  tag?.parent?.name === 'High School Educator' ||
                  tag?.parent?.name === 'School District Lead' ||
                  tag?.parent?.name === 'CTE Instructor' ||
                  tag?.parent?.name === 'SHSM Instructor' ||
                  tag?.parent?.name === 'Technology Teacher' ||
                  tag?.parent?.name === 'TAS Teacher')
              ) {
                <ion-row class="checkbox-col" (click)="setTag(tag)">
                  <ion-col size="1">
                    <ion-checkbox [checked]="tag.hasUserTags"></ion-checkbox>
                  </ion-col>
                  <ion-col size="11">
                    <div class="heading">{{ tag?.name }}</div>
                  </ion-col>
                </ion-row>
              }
              @if (tag.name === 'CTE Instructor' || tag.name === 'SHSM Instructor' || tag.name === 'Technology Teacher' || tag.name == 'TAS Teacher') {
                <ion-row class="checkbox-col" (click)="setRow(tag)">
                  <ion-col size="12">
                    <div class="heading">{{ tag?.name }}</div>
                  </ion-col>
                </ion-row>
              }
            </ion-grid>
          </ion-item>
        </ion-item-group>
      }
    </div>
  }
  @if (finished) {
    <div class="top-header-container">
      <ion-grid>
        <ion-row>
          <ion-col class="centered" size="12">
            <img class="icon" src="assets/images/{{ icon }}.png" />
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col class="centered" size="12">
            <ion-label class="description-label">{{ finishMessage | translate | async }}</ion-label>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col class="centered" size="12">
            <ion-label class="journey-label">{{ 'Let the journey begin!' | translate | async }}</ion-label>
          </ion-col>
        </ion-row>
        @if (!this.selectedUserId) {
          <ion-row>
            <ion-col class="centered start-button" size="12">
              <ion-button (click)="onFinish()">{{ 'Start' | translate | async }}</ion-button>
            </ion-col>
          </ion-row>
        }
        @if (this.selectedUserId) {
          <ion-row>
            <ion-col class="centered finish-button" size="12">
              <ion-button (click)="onFinish()">{{ 'Finish' | translate | async }}</ion-button>
            </ion-col>
          </ion-row>
        }
      </ion-grid>
    </div>
  }
  <div class="bottom-container">
    <ion-row>
      @if (tagTreeLevel !== 2) {
        <ion-col size="4">
          <ion-button class="back-button" fill="clear" (click)="onBack()">‹ {{ 'Back' | translate | async }}</ion-button>
        </ion-col>
      }
      @if (highSchoolTags.length > 0 && !finished) {
        <ion-col class="skip-button">
          <ion-button class="skip" (click)="next()">{{ 'Next' | translate | async }}</ion-button>
        </ion-col>
      }
    </ion-row>
  </div>
</ion-grid>
