<mat-accordion>
  @if (productTabType === productTabTypeValue.About) {
    @for (feature of productFeatures; track feature) {
      <mat-expansion-panel hideToggle [disabled]="true">
        <mat-expansion-panel-header class="expansion-panel-header">
          <div class="inner-panel">
            <div class="heading">{{ feature.title | translate | async }}</div>
            <div class="sub-heading">
              <span>{{ feature.description | translate | async }} </span>
            </div>
            @if (isJoinCodeProduct === true) {
              <app-join-code [joinCode]="joinCode"></app-join-code>
            }
          </div>
        </mat-expansion-panel-header>
      </mat-expansion-panel>
    }
    @if (moreResults) {
      <div (click)="getProductFeaturesById(productId, true)" class="load-more">
        <ion-row>
          <ion-col size="12">
            <div>{{ 'Load More' | translate | async }}</div>
            <div><ion-icon name="chevron-down-outline"></ion-icon></div>
          </ion-col>
        </ion-row>
      </div>
    }
  }
  @if (productTabType === productTabTypeValue.Settings) {
    <app-product-settings
      (productOrgDomainsChange)="saveProductOrgDomains($event)"
      (orgSsoAuthChange)="saveOrgSsoAuth($event)"
      (productOrgPrivacyTypeChange)="saveProductOrgPrivacyType($event)"
      (productJoinCodesChange)="saveProductJoinCodes()"
      [userRoles]="userRoles"
      [productId]="productId"
      [productOrgId]="productOrgId"
      [privacyTypeId]="privacyTypeId"
      [organizationId]="organizationId"
      [productJoinCodeSettings]="productJoinCodeSettings"></app-product-settings>
  }
</mat-accordion>
