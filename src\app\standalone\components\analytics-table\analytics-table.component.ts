import { Date<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ng<PERSON><PERSON>, AsyncPipe } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { AnalyticsExpansionPanelHeader } from '@app/core/contracts/contract';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { DataService } from '@app/core/services/data-service';
import { RolesService } from '@app/core/services/roles.service';
import { map } from 'rxjs';
import { MatAccordion, MatExpansionPanel, MatExpansionPanelHeader } from '@angular/material/expansion';
import { MatTable, MatHeaderCell, MatCell, MatHeaderRowDef, MatHeaderRow, MatRowDef, MatRow } from '@angular/material/table';
import { CdkColumnDef, CdkHeaderCellDef, CdkCellDef } from '@angular/cdk/table';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-analytics-table',
    templateUrl: './analytics-table.component.html',
    styleUrls: ['./analytics-table.component.scss'],
    imports: [
        MatAccordion,
        MatExpansionPanel,
        MatExpansionPanelHeader,
        NgStyle,
        MatTable,
        CdkColumnDef,
        CdkHeaderCellDef,
        MatHeaderCell,
        NgClass,
        CdkCellDef,
        MatCell,
        MatHeaderRowDef,
        MatHeaderRow,
        MatRowDef,
        MatRow,
        AsyncPipe,
        TranslatePipe,
    ]
})
export class AnalyticsTableComponent implements OnInit {
  @Input() id: string;
  @Input() componentId: string;
  @Input() organizationUserBreakDown: any;
  @Input() type?: string;
  @Input() index?: number;

  expansionPanelHeader: AnalyticsExpansionPanelHeader[] = [];
  analyticTableContent: any[] = [];

  textContainerWidth: number;
  columns: Array<any>;
  displayedColumns: Array<any>;
  dataSource: any;

  constructor(
    private dataService: DataService,
    private rolesService: RolesService
  ) {}

  ngOnInit(): void {
    this.setExpansionPanelHeader();
    this.setTableContent();
  }

  hasAdminAccess() {
    return this.rolesService.hasFeatureRoleAccess([ActionTypes.Manage, ActionTypes.Publish]);
  }

  formatNumber(value: number) {
    return value?.toLocaleString();
  }

  isNumber(value: string) {
    if (value === '') {
      return false;
    }
    return !isNaN(+value);
  }

  isNumberOrDate(value: string) {
    if (this.isNumber(value)) {
      return true;
    }
    if (!isNaN(new Date(value).getDate())) {
      return true;
    }
    return false;
  }

  openGroup(panelState: boolean) {
    if (!this.hasAdminAccess() && !panelState) return;

    const userIds = this.organizationUserBreakDown.users?.map((x: any) => x.userId);
    if (!userIds || userIds.length === 0) return;

    this.dataService
      .getAnalyticsUsernames(this.componentId ?? '', 'organization', userIds.toString())
      .pipe(
        map((res: any) => {
          this.organizationUserBreakDown.users?.forEach((element: any) => {
            const index = this.analyticTableContent.findIndex((x: any) => x.name === element.userId);
            if (index === -1) return;
            this.analyticTableContent[index].name = res.users.find((x: any) => x.userId === element.userId)?.userName ?? element.userId;
          });
        })
      )
      .subscribe();
  }

  setExpansionPanelHeader() {
    switch (this.componentId) {
      case '6ab7eb96-9464-4779-9c05-da7cc097ee93':
        //Organization Overview Section
        if (this.type === 'Overview') {
          this.expansionPanelHeader = [
            { amount: this.organizationUserBreakDown.totalUsers, title: this.organizationUserBreakDown.role },
            { amount: this.organizationUserBreakDown.activeUsers, title: 'Active Users' },
            { amount: this.organizationUserBreakDown.totalSessions, title: 'Sessions' },
          ];
        } else if (this.type === 'Product Licenses') {
          this.expansionPanelHeader = [{ amount: 1, title: this.organizationUserBreakDown.licenseName }];
        }
        break;

      case '389b6d31-f192-42b6-b8d4-fe6b9b5e5837':
        //User Setup Section
        this.expansionPanelHeader = [{ amount: this.organizationUserBreakDown.totalUsers, title: this.organizationUserBreakDown.role }];
        break;

      case 'ea5200cd-07e3-40bf-bbe8-2e43fd30108d':
      case '7070024d-3f75-4a5a-bf8c-894a34ab1ada':
      case '23306e0f-e350-4991-b27b-911bdf8fe39b':
      case '2f63c972-58f2-4930-b929-2e051a3a58e9':
      case '731165c8-76f7-4f31-bd9a-9cd8847fb77a':
      case '75a3ea85-d787-4f6e-a0ec-cedbe85fed97':
        //IEPC and Training & Help Articles Sections
        this.expansionPanelHeader = [{ title: this.organizationUserBreakDown.role }, { amount: this.organizationUserBreakDown.totalCount, title: this.type }];
        break;

      case '5f4add80-54d7-413a-815a-f6d12a09f3f0':
      case 'ecd9ab23-4785-42fa-b530-a998a7c27ea8':
      case 'eb68519a-4649-498b-b7d7-75b21aaaad3e':
        //Most Popular Sections
        this.expansionPanelHeader = [
          { amount: this.index ? this.index + 1 : 1, title: this.organizationUserBreakDown.contentName },
          { amount: this.organizationUserBreakDown.totalCount, title: 'Opens' },
        ];
        break;
    }
  }

  setTableContent() {
    const datePipe: DatePipe = new DatePipe('en-US');
    this.organizationUserBreakDown.licenses?.forEach((element: any) => {
      switch (this.componentId) {
        case '6ab7eb96-9464-4779-9c05-da7cc097ee93':
          if (this.type === 'Product Licenses') {
            this.analyticTableContent.push({
              organization: element.organizationName,
              expires: datePipe.transform(element.expiryDate, 'MMM d, y'),
            });
            break;
          }
      }
    });
    this.organizationUserBreakDown.users?.forEach((element: any) => {
      switch (this.componentId) {
        case '6ab7eb96-9464-4779-9c05-da7cc097ee93':
          //Organization Overview Section
          if (this.type === 'Overview') {
            this.analyticTableContent.push({
              name: element.userId,
              persona: element.personaTags,
              lastLogin: datePipe.transform(element.lastLogin, 'MMM d, y'),
              sessions: element.itemCount,
            });
            this.analyticTableContent.sort((a, b) => b.sessions - a.sessions);
          }
          break;

        case '389b6d31-f192-42b6-b8d4-fe6b9b5e5837':
          //User Setup Section
          this.analyticTableContent.push({
            name: element.userId,
            persona: element.personaTags,
            sessions: element.itemCount,
          });
          this.analyticTableContent.sort((a, b) => b.sessions - a.sessions);
          break;

        case 'ea5200cd-07e3-40bf-bbe8-2e43fd30108d':
        case '7070024d-3f75-4a5a-bf8c-894a34ab1ada':
        case '23306e0f-e350-4991-b27b-911bdf8fe39b':
        case '2f63c972-58f2-4930-b929-2e051a3a58e9':
        case '731165c8-76f7-4f31-bd9a-9cd8847fb77a':
          //IEPC Sections
          if (this.type === 'Impressions') {
            this.analyticTableContent.push({
              name: element.userId,
              impressions: element.itemCount,
            });
            this.analyticTableContent.sort((a, b) => b.impressions - a.impressions);
          } else if (this.type === 'Opens') {
            this.analyticTableContent.push({
              name: element.userId,
              opens: element.itemCount,
            });
            this.analyticTableContent.sort((a, b) => b.opens - a.opens);
          } else if (this.type === 'Engagements') {
            this.analyticTableContent.push({
              name: element.userId,
              engagements: element.itemCount,
            });
            this.analyticTableContent.sort((a, b) => b.engagements - a.engagements);
          }
          break;

        case '75a3ea85-d787-4f6e-a0ec-cedbe85fed97':
          //Training & Help Articles Section
          if (this.type === 'Opens') {
            this.analyticTableContent.push({
              name: element.userId,
              opens: element.itemCount,
            });
            this.analyticTableContent.sort((a, b) => b.opens - a.opens);
          } else if (this.type === 'Engagements') {
            this.analyticTableContent.push({
              name: element.userId,
              engagements: element.itemCount,
            });
            this.analyticTableContent.sort((a, b) => b.engagements - a.engagements);
          } else if (this.type === 'Completions') {
            this.analyticTableContent.push({
              name: element.userId,
              impressions: element.itemCount,
            });
            this.analyticTableContent.sort((a, b) => b.impressions - a.impressions);
          }
          break;

        case '5f4add80-54d7-413a-815a-f6d12a09f3f0':
        case 'ecd9ab23-4785-42fa-b530-a998a7c27ea8':
        case 'eb68519a-4649-498b-b7d7-75b21aaaad3e':
          //Most Popular Sections
          this.analyticTableContent.push({
            name: element.userId,
            opens: element.itemCount,
          });
          this.analyticTableContent.sort((a, b) => b.opens - a.opens);
          break;
      }
    });
    this.setupTableData();
  }

  setupTableData() {
    const columndata = this.analyticTableContent
      .reduce((columns, row) => {
        return [...columns, ...Object.keys(row)];
      }, [])
      .reduce((columns: string | any[], column: any) => {
        return columns.includes(column) ? columns : [...columns, column];
      }, []);
    this.columns = columndata.map((column: string) => {
      return {
        columnDef: column,
        header: column.charAt(0).toUpperCase() + column.slice(1),
        cell: (element: any) => `${element[column] ? element[column] : ''}`,
      };
    });
    this.displayedColumns = this.columns.map(c => c.columnDef);
    this.dataSource = this.analyticTableContent;
  }
}
