import { HttpEventType } from '@angular/common/http';
import { Component, EventEmitter, forwardRef, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';
import { IAsset, IComponent, IFileOut, IInstanceSectionComponent } from '@app/core/contracts/contract';
import { AlertService } from '@app/core/services/alert-service';
import { DataService } from '@app/core/services/data-service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { environment } from '@env/environment';
import { BehaviorSubject, Subject } from 'rxjs';
import { takeUntil, tap } from 'rxjs/operators';
import { NgClass, NgStyle, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { IonicModule } from '@ionic/angular';
import { FileUploadControlDirective } from './file-upload-control.directive';
import { VideoPlayerComponent } from '@app/standalone/components/video-player/video-player.component';
import { FileDownloadControlComponent } from '@app/standalone/components/file-download-control/file-download-control.component';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';

@Component({
  selector: 'app-file-upload-control',
  templateUrl: './file-upload-control.component.html',
  styleUrls: ['./file-upload-control.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      multi: true,
      useExisting: forwardRef(() => FileUploadControlComponent),
    },
    {
      provide: NG_VALIDATORS,
      multi: true,
      useExisting: forwardRef(() => FileUploadControlComponent),
    },
  ],
  imports: [NgClass, IonicModule, NgStyle, FileUploadControlDirective, VideoPlayerComponent, FileDownloadControlComponent, AsyncPipe, TranslatePipe],
})
export class FileUploadControlComponent extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() override label: string;
  @Input() override disabled = false;
  @Input() itemBackgroundColor = '#4e4e4e';
  @Input() toolTip: string;
  @Input() identifierText: string;
  @Input() fileFormat: string;
  @Input() componentType: string | undefined;
  @Input() defaultImageUrl: string | null;
  @Input() fileTypeBw: number | undefined;
  @Input() minFileSize: number | undefined;
  @Input() maxFileSize: number | undefined;
  @Input() containerName = 'static';
  @Input() buttonText: string;
  @Input() placeHolderText: string;
  @Input() mediaId: string;
  @Input() component: IComponent;
  @Input() instanceSectionComponent: IInstanceSectionComponent;
  @Input() sidePanelPadding = false;
  @Input() noPadding = false;
  @Input() noContainer = false;
  @Input() progress = new BehaviorSubject<number>(0); //This is because the object is not updated in the DOM while in a subscribed function
  @Output() valueUpdated = new EventEmitter<string | null>();
  @Output() assetType = new EventEmitter<string | null>();

  componentDestroyed$: Subject<boolean> = new Subject();
  assetUrl: string | null;
  asset: IAsset | null;
  acceptedUploadFormat: string;
  defaultPlaceHolder = 'Select a file to upload';

  private readonly IMAGE_SOFT_LIMIT_MB = 2;
  private readonly IMAGE_HARD_LIMIT_MB = 5;
  public imageSizeWarning: string | null = null;

  public currentMediaId: string | null = null;
  public isUpdateMode = false;

  constructor(
    private dataService: DataService,
    public builderService: BuilderService,
    public alertService: AlertService
  ) {
    super();
  }

  get isEmbed() {
    if (this.asset && this.asset.embedCode && this.asset.embedCode !== '' && this.asset.mediaUploadType) {
      return this.asset.mediaUploadType === 'Embed';
    }
    return false;
  }

  get includesImage() {
    if (this.asset?.contentType) {
      return this.asset?.contentType.includes('image/');
    }
    return false;
  }

  get isPending() {
    if (this.asset) {
      return this.asset?.contentType === '' && this.asset?.size === 0;
    }
    return false;
  }

  get isVideo() {
    if (this.asset) {
      if (this.asset.urlUpload) {
        // We can add more checks here. Mp4 should be good for now
        return this.asset.urlUpload.includes('.mp4');
      } else {
        return this.builderService.videoContentTypes?.some(x => x === this.asset?.contentType);
      }
    }
    return false;
  }

  get isFile() {
    if (this.asset) {
      return this.builderService.fileContentTyps?.some(x => x === this.asset?.contentType);
    }
    return false;
  }

  ngOnInit(): void {
    this.buttonText = this.buttonText && this.buttonText !== '' ? this.buttonText : 'Upload';
    this.placeHolderText = this.placeHolderText && this.placeHolderText !== '' ? this.placeHolderText : this.defaultPlaceHolder;
    this.setAcceptedFileTypes();
    if (this.mediaId) {
      this.currentMediaId = this.mediaId;
    }
    //Run on next tick, allows this.textValue only set after validation
    setTimeout(() => {
      if (this.textValue && this.textValue !== this.defaultImageUrl && this.textValue !== '') {
        this.dataService
          .getAssetDetailsById(this.textValue)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(asset => {
            if (asset) {
              this.asset = asset;
              this.assetUrl = `${environment.contentUrl}asset/${this.textValue}/content`;
            }
          });
      } else if (this.defaultImageUrl && this.defaultImageUrl !== '') {
        this.dataService
          .getAssetDetailsById(this.defaultImageUrl)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(asset => {
            if (asset) {
              this.asset = asset;
              this.assetUrl = `${environment.contentUrl}asset/${this.defaultImageUrl}/content`;
            }
          });
      }
    }, 1);
  }

  setAcceptedFileTypes() {
    switch (this.componentType) {
      case 'Download Block':
        this.acceptedUploadFormat = this.builderService.acceptedFileFormats
          .filter(x => Number(x.id) & Number(this.fileTypeBw))
          .map(x => x.value)
          .join(',');
        break;
      case 'Media Block':
        this.acceptedUploadFormat = this.builderService.acceptedVideoFormats
          .filter(x => Number(x.id) & Number(this.fileTypeBw))
          .map(x => x.value)
          .join(',');
        break;
      case 'HTML5 Game':
        this.acceptedUploadFormat = this.builderService.acceptedHtml5Formats
          .filter(x => Number(x.id) & Number(this.fileTypeBw))
          .map(x => x.value)
          .join(',');
        break;
      default:
        switch (this.componentType) {
          case 'Page Banner':
            this.acceptedUploadFormat = this.builderService.acceptedImageAndVideoFormats
              .filter(x => Number(x.id) & Number(this.fileTypeBw))
              .map(x => x.value)
              .join(',');
            break;
          case 'Media & Text':
            this.acceptedUploadFormat = this.builderService.acceptedImageAndVideoFormats
              .filter(x => Number(x.id) & Number(this.fileTypeBw))
              .map(x => x.value)
              .join(',');
            break;
          case 'Image Upload Field':
            this.acceptedUploadFormat = this.builderService.acceptedImageFormats
              .filter(x => Number(x.id) & Number(this.fileTypeBw))
              .map(x => x.value)
              .join(',');
            break;
          default:
            this.acceptedUploadFormat = this.builderService.allFormats
              .filter(x => Number(x.id) & Number(this.fileTypeBw))
              .map(x => x.value)
              .join(',');
        }
        break;
    }
  }

  override setValue(event: any): void {
    this.valueUpdated.emit(event);
    this.writeValue(event, false);
  }

  onFileDrop(files: FileList | any) {
    this.processFile(files[0]);
  }

  fileBrowseHandler(event: any) {
    if (event?.target?.files) {
      this.processFile(event.target.files[0]);
    }
  }

  processFile(file: File) {
    let isValidType = true;
    this.imageSizeWarning = null;

    if (file.type.startsWith('image/')) {
      const fileSizeMB = file.size / (1024 * 1024); // Convert bytes to MB
      if (fileSizeMB > this.IMAGE_HARD_LIMIT_MB) {
        this.imageSizeWarning = `This image is too large to upload (${fileSizeMB.toFixed(1)}MB). Maximum size is ${this.IMAGE_HARD_LIMIT_MB}MB.`;
        this.alertService.presentAlert('File too large', this.imageSizeWarning);
        return;
      } else if (fileSizeMB > this.IMAGE_SOFT_LIMIT_MB) {
        this.imageSizeWarning = `This upload may cause performance issues when loading for the user (${fileSizeMB.toFixed(1)}MB)`;
      }
    }

    if (file.name && this.fileTypeBw !== undefined) {
      const lowerCaseName = file.name.toLowerCase();
      const baseType = file.type.split('/')[0];
      const flatList = this.acceptedUploadFormat.split(',').map(x => x.trim());

      const isValidBaseType = baseType && baseType !== '' && flatList?.some(x => x.includes(baseType));
      const isValidExtension = flatList?.some(x => lowerCaseName.endsWith(x));

      isValidType = isValidBaseType || isValidExtension;

      if (!isValidType) {
        this.alertService.presentAlert('File type is invalid', 'Please select a valid fileType/Format');
      }
    }

    //Validate FileSize
    if (file.size) {
      const kbSize = 1024;
      const fileSize = Math.round(file.size / kbSize);
      const maxKb = (this.maxFileSize ?? 0) * kbSize;
      const minKb = this.minFileSize ?? 0;
      if ((this.maxFileSize !== undefined && maxKb > 0) || (this.minFileSize !== undefined && minKb > 0)) {
        if (fileSize > maxKb) {
          this.alertService.presentAlert('Max file size reached', `Max file size ${this.maxFileSize} Mb`);
          isValidType = false;
        } else if (fileSize < minKb) {
          this.alertService.presentAlert('Min file size reached', `Min file size ${this.minFileSize} Kb`);
          isValidType = false;
        }
        //Duplicate IF ???
        // else if (fileSize < minKb && fileSize > maxKb) {
        //   this.alertService.presentAlert('Min and Max file size reached', `Min file size ${this.minFileSize} Kb And Max file size ${this.maxFileSize} Mb`);
        //   isValidType = false;
        // }
      }
    }

    if (this.validateFileFormat(file) && isValidType) {
      const formData = new FormData();
      formData.append('file', file);
      this.placeHolderText = file.name;

      const asset: IFileOut = {
        asset: formData,
        type: file.type,
      };

      this.uploadAsset(asset)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {});
    }
  }

  validateFileFormat(file: File): boolean {
    const fileFormat = this.fileFormat;
    const matches = file.type.match(fileFormat);
    if (matches && matches.length > 0) {
      return true;
    }
    return false;
  }

  deleteFile() {
    if (this.isUpdateMode) {
      this.currentMediaId = this.mediaId || this.currentMediaId;
    } else {
      this.currentMediaId = null;
    }

    this.asset = null;
    this.assetUrl = null;
    this.placeHolderText = this.defaultPlaceHolder;
    this.progress.next(0);

    if (this.textValue && this.textValue !== this.defaultImageUrl) {
      this.setValue(null);
      this.assetType.emit(null);
    } else {
      this.valueUpdated.emit(null);
    }
  }

  toggleUpdateMode() {
    this.isUpdateMode = !this.isUpdateMode;
    if (this.isUpdateMode) {
      this.currentMediaId = this.mediaId || this.currentMediaId;
      this.buttonText = 'Update';
    } else {
      this.buttonText = 'Upload';
    }
  }

  uploadAsset(asset: IFileOut) {
    const targetMediaId = this.isUpdateMode ? this.currentMediaId : this.mediaId;

    return this.dataService.postAsset(asset, this.containerName, targetMediaId).pipe(
      tap(event => {
        if (event.type === HttpEventType.UploadProgress) {
          if (event.total) {
            //Upload to API server is complete but API has not yet completed the upload of the media
            //ion-progress-bar data range is 0->1 (progress bar set to a max of 90%)
            this.progress.next(((event.loaded / event.total) * 90) / 100);
          }
        } else if (event.type === HttpEventType.Response && event.body) {
          this.progress.next(1);
          this.asset = event.body as IAsset;
          this.placeHolderText = event.body.name;
          this.setValue(event.body.id);
          this.assetType.emit(this.asset.contentType);
          this.assetUrl = `${environment.contentUrl}asset/${this.textValue}/content`;
          // Reset update mode after successful upload
          this.isUpdateMode = false;
          this.buttonText = 'Upload';
        }
      })
    );
  }

  getWarningColor(): string {
    return this.imageSizeWarning?.includes('too large') ? 'danger' : 'warning';
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
