import { NgClass, AsyncPipe } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, Component, Input, OnInit } from '@angular/core';
import { IComponent, IHeading } from '@app/core/contracts/contract';
import { IonicModule } from '@ionic/angular';
import { BaseValueComponent } from '@app/standalone/components/base-control/base-value.component';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-heading-value',
    templateUrl: './heading-value.component.html',
    styleUrls: ['./heading-value.component.scss'],
    imports: [NgClass, IonicModule, AsyncPipe, TranslatePipe],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class HeadingValueComponent extends BaseValueComponent implements OnInit {
  @Input() component: IComponent;
  @Input() inheritedPropertyValue: string | null;
  @Input() defaultText: string | undefined;
  @Input() fontSize = 27;
  @Input() color: string = '#FFFFFF';
  @Input() headingModal: IHeading = { text: '', description: '' };
  @Input() defaultStylingDirection: string = 'Left';
  stylingDirection: string;
  darkText: boolean;
  headingStyle: string;

  ngOnInit(): void {
    this.setData();
  }

  override setData() {
    if (this.instanceComponent?.value) {
      try {
        this.headingModal = JSON.parse(this.instanceComponent?.value) as IHeading;
      } catch (error) {
        this.headingModal = {
          text: this.instanceComponent?.value,
          description: '',
        };
      }
    }
    this.stylingDirection = this.instanceComponent?.component.templateField.stylingDirection ?? this.defaultStylingDirection;
    this.darkText = this.instanceComponent?.component.templateField.darkText ?? false;
    this.headingStyle = this.instanceComponent?.component.templateField.headingStyle ?? '';
  }
}
