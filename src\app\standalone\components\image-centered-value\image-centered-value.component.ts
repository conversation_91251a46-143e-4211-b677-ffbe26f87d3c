import { Component, CUSTOM_ELEMENTS_SCHEMA, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { IImageCentered } from '@app/core/contracts/contract';
import { ImageViewModalComponent } from '@app/standalone/modals/image-view-modal/image-view-modal.component';
import { environment } from '@env/environment';
import { CommonModule, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { ModalController } from '@ionic/angular';
import { Navigation, Pagination } from 'swiper/modules';
import { SwiperOptions } from 'swiper/types';
import { BaseValueComponent } from '@app/standalone/components/base-control/base-value.component';


@Component({
    selector: 'app-image-centered-value',
    templateUrl: './image-centered-value.component.html',
    styleUrls: ['./image-centered-value.component.scss'],
    imports: [CommonModule, AsyncPipe, TranslatePipe],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ImageCenteredValueComponent extends BaseValueComponent implements OnInit {
  @Input() inheritedPropertyValue: string | null;
  @ViewChild('swiper') swiperRef: ElementRef;
  imageUrl = 'assets/images/no-image.png';
  swiperConfig: SwiperOptions = {
    direction: 'horizontal',
    autoHeight: true,
    loop: false,
    allowTouchMove: true,
    observer: true,
    observeParents: true,
    parallax: true,
    modules: [Navigation, Pagination],
    pagination: {
      el: '.swiper-pagination',
      clickable: true,
    },
    navigation: {
      nextEl: '.swiper-button-next',
      prevEl: '.swiper-button-prev',
    },
  };
  imageCenteredList: IImageCentered[] = [];
  oldValue: any;

  constructor(private modalController: ModalController) {
    super();
  }

  ngOnInit(): void {
    this.setData();
  }

  override setData() {
    if (this.instanceComponent?.value) {
      const jsonObject = JSON.parse(this.instanceComponent.value);
      this.imageCenteredList = jsonObject as IImageCentered[];
    }

    if (!this.imageCenteredList.length) {
      this.imageCenteredList.push({ sortOrder: 0, caption: '' } as IImageCentered);
    }

    for (let i = 0; i < this.imageCenteredList.length; i++) {
      if (this.imageCenteredList[i].asset) {
        this.imageCenteredList[i].assetUrl = `${environment.contentUrl}asset/${this.imageCenteredList[i].asset}/content`;
      } else {
        this.imageCenteredList[i].assetUrl = 'assets/images/no-image.png';
      }
    }
  }

  async openImageModal(imageUrl?: string, caption?: string, noClickPreview?: boolean) {
    if (!imageUrl || noClickPreview === true ) {
      return;
    }

    const modal = await this.modalController.create({
      component: ImageViewModalComponent,
      componentProps: { imageUrl: imageUrl, caption: caption },
      cssClass: 'full-screen',
    });

    return await modal.present();
  }

  slideNext() {
    this.swiperRef.nativeElement.swiper.slideNext(100);
  }

  slidePrev() {
    this.swiperRef.nativeElement.swiper.slidePrev(100);
  }
}
