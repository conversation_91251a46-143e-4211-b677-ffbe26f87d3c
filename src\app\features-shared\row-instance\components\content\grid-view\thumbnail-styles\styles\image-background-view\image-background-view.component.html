<div style="width: 100%">
  <app-thumbnail-icons
    [row]="row"
    [content]="content"
    [instance]="instance"
    [readingMode]="readingMode"
    [isDraggable]="isDraggable"
    [isEducator]="isEducator"
    [hasAdminAccess]="hasAdminAccess"
    [hideAddButton]="hideAddButton"
    [canHover]="false"
    [isAssignmentRow]="isAssignmentRow"
    (contentRemoved)="emitContentRemoved()"
    (editCustomRowContent)="editContent()">
  </app-thumbnail-icons>
  <div
    (click)="route()"
    [style]="'--background-image:url(' + iconUrl + ');'"
    class="background-gradient"
    [ngClass]="{
      'content portrait': thumbnailType === 'portrait',
      'content landscape': thumbnailType !== 'portrait',
      'status-inprogress': status === 'InProgress',
      'status-complete': status === 'Completed',
    }">
    <div class="text-container">
      @if (featureName) {
        <h5>{{ featureName | translate | async | uppercase }}</h5>
      }
      @if (instanceName) {
        <h1>{{ instanceName | translate | async }}</h1>
      }
      @if (property1) {
        <h6>{{ property1 | translate | async }} &#8226; {{ property2 | translate | async }}</h6>
      }
    </div>
  </div>

  @if (status) {
    <div class="status-foot-container">
      @if (status === 'InProgress') {
        <span class="inner" style="background-color: orange"> {{ 'IN PROGRESS' | translate | async }} </span>
      }
      @if (status === 'Completed') {
        <span class="inner" style="background-color: green"> {{ 'COMPLETED' | translate | async }} </span>
      }
    </div>
  }
</div>
