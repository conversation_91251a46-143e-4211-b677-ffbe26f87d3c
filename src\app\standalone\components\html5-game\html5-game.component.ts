import { NgClass, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, Renderer2 } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
import { IAsset, IEngagementIn, IInstance, IInstanceSectionComponent, ITemplateField } from '@app/core/contracts/contract';
import { EngagementTypes } from '@app/core/enums/engagment-types.enum';
import { DataService } from '@app/core/services/data-service';
import { Events } from '@app/core/services/events-service';
import { InstanceService } from '@app/core/services/instance-service';
import { environment } from '@env/environment';
import { IonicModule } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';
import { BaseValueComponent } from '@app/standalone/components/base-control/base-value.component';


export interface CompletionObj {
  score: number;
  completed: boolean;
}

@Component({
    selector: 'app-html5-game',
    templateUrl: './html5-game.component.html',
    styleUrls: ['./html5-game.component.scss'],
    imports: [NgClass, IonicModule, AsyncPipe, TranslatePipe]
})
export class Html5GameComponent extends BaseValueComponent implements OnInit, OnDestroy {
  @Output() componentData = new EventEmitter();
  @Input() instanceSectionComponent: IInstanceSectionComponent | undefined;
  @Input() instance: IInstance;
  @Input() inheritedPropertyValue: string | null;
  @Input() defaultImageUrl: string | undefined;
  @Input() src: SafeResourceUrl | null = null;

  templateField: ITemplateField | undefined;
  imageUrl = '';
  heightPx: number | undefined;
  assetId: string;
  asset: IAsset;
  componentDestroyed$: Subject<boolean> = new Subject();
  stylesAppended = false;
  instanceId: string | undefined = undefined;

  fire = true;
  listener: () => void;
  inProgress = false;
  constructor(
    private renderer: Renderer2,
    private domsanitizer: DomSanitizer,
    private activatedRoute: ActivatedRoute,
    private dataService: DataService,
    private eventsService: Events,
    public instanceService: InstanceService
  ) {
    super();
    this.listener = this.renderer.listen(window, 'message', (evt: MessageEvent) => {
      this.handleMessage(evt);
    });
  }

  override setData(): void {
    const newAssetId =
      (this.inheritedPropertyValue && this.inheritedPropertyValue !== '') ? this.inheritedPropertyValue :
      (this.instanceSectionComponent?.value && this.instanceSectionComponent.value !== '') ? this.instanceSectionComponent.value :
      (this.defaultImageUrl && !this.defaultImageUrl.includes('http')) ? this.defaultImageUrl :
      (this.defaultImageUrl && this.defaultImageUrl.includes('http')) ? this.defaultImageUrl :
      '';

    if (newAssetId !== this.assetId) {
      this.assetId = newAssetId;
      this.getAssetDetails();
    }
  }

  private handleMessage(evt: MessageEvent) {
    try {
      if (this.instance.id === this.getRouteParamValue('instanceslug')) {
        this.instanceId = this.instance.id;
      }
      else
      {
        this.instanceId = this.getRouteParamValue('instanceslug');
      }

      if (evt && evt.data && typeof evt.data === 'string' && evt.data.length > 0) {
        if (evt.data.includes('score') && evt.data.includes('completed')) {
          const objString = (evt.data as string).replace('True', 'true').replace('False', 'false');
          const obj: CompletionObj = JSON.parse(objString);
          if (obj.score && obj.completed && obj.completed === true) {
            this.completeInstanceComponent(obj.score);
          }
        } else if (evt.data.includes('Hello from')) {
          this.inProgress = true;
          this.addInstanceSectionComponentEngagement(0);
        }
      }
    } catch {
      return;
    }
  }

  ngOnInit() {
    this.setData();
    this.templateField = this.instanceSectionComponent?.component?.templateField;
    this.setImageUrl();
  }

  addInstanceSectionComponentEngagement(progress: number) {
    this.dataService
      .addInstanceSectionComponentEngagement({
        instanceId: this.instance.id,
        instanceSectionComponentId: this.instanceSectionComponent?.id,
        engagementType: EngagementTypes.Click,
        percentageValue: progress !== undefined ? Math.round(progress) : 0,
        nominalValue: 1,
      } as IEngagementIn)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe();
  }

  getRouteParamValue(paramName: string): string {
    return this.activatedRoute.snapshot.data[paramName] ?? this.activatedRoute.snapshot.params[paramName];
  }

  completeInstanceComponent(progress: number) {
    this.dataService
      .addInstanceSectionComponentCompletion(this.instanceSectionComponent?.id ?? '', this.instanceId, Math.round(progress))
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.eventsService.publish('instanceSectionComponentCompleted', this.instanceSectionComponent?.id);
        if (this.instanceSectionComponent) {
          this.instanceSectionComponent.completed = true;
        }
      });
  }

  getAssetDetails() {
    if (!this.assetId || this.assetId.length === 0) {
      return;
    }

    this.dataService
      .getAssetDetailsById(this.assetId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(async (data: IAsset) => {
        this.asset = data;

        this.imageUrl = `${environment.html5Path}/${this.asset.id}`;

        if (this.imageUrl && this.imageUrl.length > 0) {
          this.src = this.domsanitizer.bypassSecurityTrustResourceUrl(this.imageUrl);
        }
      });
  }

  async setImageUrl() {
    if (this.inheritedPropertyValue && this.inheritedPropertyValue !== '') {
      this.assetId = this.inheritedPropertyValue;
    } else if (this.instanceSectionComponent?.value && this.instanceSectionComponent.value !== '') {
      this.assetId = this.instanceSectionComponent?.value;
    } else if (this.defaultImageUrl && !this.defaultImageUrl.includes('http')) {
      this.assetId = this.defaultImageUrl;
    } else if (this.defaultImageUrl && this.defaultImageUrl.includes('http')) {
      this.assetId = this.defaultImageUrl;
    }

    this.getAssetDetails();
  }

  ngOnDestroy() {
    this.src = null;
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
    this.componentData.complete();
    this.componentData.unsubscribe();
  }

  logAssetError() {
    return false;
  }
}
