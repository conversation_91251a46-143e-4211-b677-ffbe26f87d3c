<ion-header class="ion-no-border">
  <ion-toolbar mode="ios">
    <ion-buttons slot="end">
      <ion-button (click)="close()">
        <ion-icon slot="icon-only" name="close-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
  <ion-toolbar class="title-toolbar" mode="ios">
    @if (heading) {
      <ion-title>{{ heading | translate | async }}</ion-title>
    } @else if (linkTypeName) {
      <ion-title>{{ 'Search' | translate | async }}: {{ linkTypeName | translate | async }}</ion-title>
    }
  </ion-toolbar>
  @if (subHeading) {
    <ion-toolbar>
      <ion-card-subtitle class="selected-header">{{ subHeading | translate | async }}</ion-card-subtitle>
    </ion-toolbar>
  }
  <ion-toolbar>
    <ion-searchbar [(ngModel)]="query" (ionInput)="searchResults()" type="search" placeholder="{{ 'Search' | translate | async }}" showCancelButton="focus" enterkeyhint="enter" [debounce]="1000"></ion-searchbar>
  </ion-toolbar>
</ion-header>
<ion-content class="ion-padding" [style.height.vh]="height" [style.min-height.vh]="height" [style.max-height.vh]="height">
  @if (contentHeading) {
    <ion-header>{{ contentHeading | translate | async }}</ion-header>
  }
  <ion-grid>
    @if (results && results.length > 0) {
      <ion-row>
        <ion-grid>
          @for (instance of results; track $index) {
            <app-instance-card [keyVal]="instance" [hideIcon]="true" (keyValClicked)="add(instance)"></app-instance-card>
          }
          <hr />
        </ion-grid>
      </ion-row>
    }
  </ion-grid>
</ion-content>
<ion-footer>
  <ion-toolbar mode="ios">
    <ion-buttons slot="secondary">
      <ion-button (click)="close()" fill="clear" color="medium">{{ 'Cancel' | translate | async }}</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-footer>
