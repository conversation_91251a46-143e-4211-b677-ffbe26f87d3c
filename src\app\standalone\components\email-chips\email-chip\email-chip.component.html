<div class="parent-container" [ngClass]="{ 'no-container': noContainer, 'side-panel-input-padding': sidePanelPadding }">
  <ion-card class="card-content-container">
    <ion-item class="inner-container">
      <mat-chip-grid #chipList>
        @for (item of listItems; track item) {
          <mat-chip-row>
            <span class="text-value">{{ item.email }}</span>
            <button matChipRemove>
              <mat-icon (click)="guest.impersonate ? '' : emitRemovedSelected(item)">cancel</mat-icon>
            </button>
          </mat-chip-row>
        }
        <form class="form-container" [formGroup]="emailForm">
          <input
            type="email"
            formControlName="email"
            [placeholder]="('Add your email' | translate | async) ?? 'Add your email'"
            [matChipInputFor]="chipList"
            (input)="emailForm.controls.email.markAsTouched()"
            [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
            [matChipInputAddOnBlur]="addOnBlur"
            (matChipInputTokenEnd)="addLinkedEmail($event)"
            [disabled]="disabled" />
        </form>
      </mat-chip-grid>
    </ion-item>
  </ion-card>
</div>
