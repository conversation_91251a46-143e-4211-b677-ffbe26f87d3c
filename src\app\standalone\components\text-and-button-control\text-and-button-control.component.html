<div class="parent-container" [ngClass]="{ 'side-panel-input-padding': sidePanelPadding }">
  <ion-card class="card-container">
    <form class="form-container" [formGroup]="textForm">
      @if (component?.templateField?.caption1) {
        <p class="caption">{{ component?.templateField?.caption1 | translate | async }}</p>
      }
      @if (component?.templateField?.description1) {
        <p class="description">{{ component?.templateField?.description1 | translate | async }}</p>
      }
      <app-text-input-control
        [disabled]="disabled"
        [noPadding]="true"
        [backgroundColor]="'#1E1E1E'"
        [placeHolder]="component?.templateField?.placeHolder1 ?? 'Start typing here'"
        [label]="component?.templateField?.label1 ?? 'Title'"
        formControlName="heading"
        [itemBackgroundColor]="'#292929'"></app-text-input-control>

      @if (component?.templateField?.caption1) {
        <p class="caption">{{ component?.templateField?.caption2 | translate | async }}</p>
      }
      @if (component?.templateField?.description1) {
        <p class="description">{{ component?.templateField?.description2 | translate | async }}</p>
      }
      <app-dynamic-text-input-control
        [placeHolder]="component?.templateField?.placeHolder2 ?? 'Start typing here'"
        [label]="component?.templateField?.label2 ?? 'Description'"
        [sidePanelPadding]="true"
        [backgroundColor]="'#1E1E1E'"
        formControlName="description"></app-dynamic-text-input-control>

      @if (component?.templateField?.caption1) {
        <p class="caption">{{ component?.templateField?.caption3 | translate | async }}</p>
      }
      @if (component?.templateField?.description1) {
        <p class="description">{{ component?.templateField?.description3 | translate | async }}</p>
      }
      <app-text-input-control
        [disabled]="disabled"
        [backgroundColor]="'#1E1E1E'"
        [placeHolder]="component?.templateField?.placeHolder3 ?? 'Start typing here'"
        [toolTip]="'Button name'"
        [label]="component?.templateField?.label3 ?? 'Button name'"
        formControlName="buttonName"
        [noPadding]="true"
        [itemBackgroundColor]="'#292929'"></app-text-input-control>
      @if (component?.templateField?.caption1) {
        <p class="caption">{{ component?.templateField?.caption4 | translate | async }}</p>
      }
      @if (component?.templateField?.description1) {
        <p class="description">{{ component?.templateField?.description4 | translate | async }}</p>
      }
      <app-text-input-control
        [disabled]="disabled"
        [backgroundColor]="'#1E1E1E'"
        [placeHolder]="component?.templateField?.placeHolder4 ?? 'Start typing here'"
        [label]="component?.templateField?.label4 ?? 'Url'"
        [toolTip]="'Url'"
        formControlName="url"
        [noPadding]="true"
        [itemBackgroundColor]="'#292929'"></app-text-input-control>
      <mat-slide-toggle style="width: 100%" color="primary" formControlName="sameUrlNavigation">{{ 'Navigate on current page' | translate | async }}</mat-slide-toggle>
      <mat-slide-toggle style="width: 100%" color="primary" formControlName="darkText">{{ 'Dark text' | translate | async }}</mat-slide-toggle>
      <div class="styling-container">
        <p class="styling-heading">{{ 'Styling' | translate | async }}</p>
        <div class="styles-container">
          <div class="styling-img-container">
            <div (click)="checkboxChanged('Bottom')">
              <div class="image-container">
                <img [ngClass]="isSelected('Bottom') ? 'selected' : null" [src]="setBottomButton()" />
              </div>
            </div>
            <p class="image-text">{{ 'Button Bottom' | translate | async }}</p>
          </div>
          <div class="styling-img-container">
            <div (click)="checkboxChanged('Right')">
              <div class="image-container">
                <img [ngClass]="isSelected('Right') ? 'selected' : null" [src]="setRightButton()" />
              </div>
            </div>
            <p class="image-text">{{ 'Button Right' | translate | async }}</p>
          </div>
        </div>
      </div>
    </form>
  </ion-card>
</div>
