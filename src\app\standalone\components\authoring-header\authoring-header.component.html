<div class="parent-container">
  <ion-row class="first-row">
    <ion-col size="12" class="start-col">
      <ion-row>
        <ion-col class="inner-col">
          <div class="upper-heading">{{ 'Component' | translate | async }}</div>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col class="inner-col">
          <div class="main-heading">{{ componentName | translate | async }}</div>
        </ion-col>
      </ion-row>
    </ion-col>
  </ion-row>
</div>
