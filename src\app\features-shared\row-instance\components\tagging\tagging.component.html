<form [formGroup]="tagForm">
  <ion-card class="layout">
    <ion-card-header>
      <ion-card-title color="light"><mat-icon svgIcon="apps_outline"></mat-icon> {{ 'Row Content' | translate | async }}</ion-card-title>
      <ion-card-subtitle>{{ 'Choose how the row content will dynamically display.' | translate | async }}</ion-card-subtitle>
    </ion-card-header>
    <ion-card-content>
      <ion-card>
        <ion-card-content>
          @for (blocks of row.filter.filtersBlocks; track blocks) {
            <div style="padding: 8px" class="content">
              <ion-grid>
                <ion-row>
                  @if (blocks.sortOrder === 0) {
                    <ion-col class="ion-text-center" style="color: white; padding-top: 16px" size="1">{{ 'IF' | translate | async }}</ion-col>
                  }
                  @if (blocks.sortOrder > 0) {
                    <ion-col class="ion-text-center" style="color: white; padding-top: 16px" size="1">{{ 'OR' | translate | async }}</ion-col>
                  }
                  <ion-col size="11">
                    @for (criteria of blocks.criteria; track criteria) {
                      @if (criteria) {
                        @if (criteria.sortOrder > 0) {
                          <p>{{ 'and' | translate | async }}</p>
                        }
                        <app-row-tagging-filter [criteria]="criteria" [allowDelete]="blocks.sortOrder > 0 || criteria.sortOrder > 0" (changed)="updateCriteria(blocks, criteria.sortOrder, $event)">
                        </app-row-tagging-filter>
                      }
                    }
                  </ion-col>
                </ion-row>
                <ion-row>
                  <ion-col class="ion-text-start" push="1" size="1">
                    <ion-button color="dark" size="small" (click)="onAndBlock(blocks)">{{ 'AND' | translate | async }}</ion-button>
                  </ion-col>
                </ion-row>
              </ion-grid>
            </div>
          }
          <ion-button color="dark" size="small" (click)="onIfBlock()">{{ 'OR' | translate | async }}</ion-button>
          <div style="padding: 8px" class="content">
            <ion-grid>
              <ion-row>
                <ion-col class="ion-text-center" style="color: white; padding-top: 16px" size="1">{{ 'SHOW' | translate | async }}</ion-col>
                <ion-col size="2">
                  <ion-select interface="popover" formControlName="showField">
                    @for (item of showByListKeyValue; track item) {
                      <ion-select-option [value]="item.id">
                        {{ item.value | translate | async }}
                      </ion-select-option>
                    }
                  </ion-select>
                </ion-col>
                <ion-col class="ion-text-center" style="color: white; padding-top: 12px" size="1">{{ 'DISPLAY' | translate | async }}</ion-col>
                <ion-col size="2">
                  <ion-select interface="popover" formControlName="instanceDisplayField">
                    @for (item of instanceDisplayKeyValue; track item) {
                      <ion-select-option [value]="item.id">
                        {{ item.value | translate | async }}
                      </ion-select-option>
                    }
                  </ion-select>
                </ion-col>
              </ion-row>
            </ion-grid>
          </div>
          <ion-button disabled color="dark" size="small">{{ 'OR' | translate | async }}</ion-button>
          <hr />
          <ion-grid class="limit-section">
            <ion-row>
              <ion-col size="12">
                <ion-checkbox formControlName="limit" color="primary"></ion-checkbox>
                <ion-label>{{ 'Limit To' | translate | async }}</ion-label>
                <ion-input formControlName="limitTo" type="number"></ion-input>
                <ion-select interface="popover" formControlName="limitInterval">
                  @for (item of limitToKeyValue; track item) {
                    <ion-select-option [value]="item.id">{{ item.value | translate | async }}</ion-select-option>
                  }
                </ion-select>
                <ion-label>{{ 'Selected By' | translate | async }}</ion-label>
                <ion-select interface="popover" formControlName="selectedBy">
                  @for (item of selectedByKeyValue; track item) {
                    <ion-select-option [value]="item.id">
                      {{ item.value | translate | async }}
                    </ion-select-option>
                  }
                </ion-select>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col size="12">
                <ion-label>{{ 'Limit Row Count' | translate | async }}</ion-label>
                <ion-input formControlName="limitRowCount" type="number"></ion-input>
              </ion-col>
            </ion-row>
            <ion-row>
              <ion-col size="12">
                <ion-label>{{ 'Live Updating' | translate | async }}</ion-label>
                <ion-checkbox formControlName="liveUpdating" color="primary"> </ion-checkbox>
              </ion-col>
            </ion-row>
          </ion-grid>
        </ion-card-content>
      </ion-card>
    </ion-card-content>
  </ion-card>
  <ion-card class="layout">
    <ion-card-header>
      <ion-card-title color="light"><ion-icon name="chatbubbles-outline"></ion-icon> {{ 'Content sentence' | translate | async }}</ion-card-title>
      <ion-card-subtitle>{{ 'Tell someone why they are seeing this row.' | translate | async }}</ion-card-subtitle>
    </ion-card-header>
    <ion-card-content>
      <app-dynamic-text-input-control
        [label]="'Content Sentence Description'"
        [placeHolder]="'Enter content sentence description'"
        [toolTip]="'The description for the content sentence'"
        formControlName="contentSentence"></app-dynamic-text-input-control>
    </ion-card-content>
  </ion-card>

  <h4>{{ 'Internal Notes' | translate | async }}</h4>
  <div class="note-container">
    <ion-item>
      <ion-textarea
        formControlName="note"
        [maxlength]="1000"
        style="color: white"
        rows="6"
        cols="20"
        placeholder="{{ 'Add a note summarizing what these parameters are intended to do.' | translate | async }}"></ion-textarea>
      <span class="text-counter">
        @if (!note.value) {
          <span>0</span>
        }
        {{ note.value?.length }} / 1000</span
      >
    </ion-item>
  </div>
</form>
