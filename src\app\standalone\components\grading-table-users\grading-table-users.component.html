<div class="inner-table-container">
  <mat-table [dataSource]="dataSource" class="custom-table inner-table">
    <ng-container matColumnDef="name">
      <mat-cell *matCellDef="let person">
        <div class="name-column">
          <span class="white-name">{{ person.name }}</span>
          <span class="subheading">{{ person.email }}</span>
        </div>
      </mat-cell>
    </ng-container>
    <ng-container matColumnDef="role">
      <mat-cell *matCellDef="let person">{{ person.roleName }}</mat-cell>
    </ng-container>
    <ng-container matColumnDef="lastlogin">
      <mat-header-cell *matHeaderCellDef mat-sort-header class="responsive-hidden-header" [ngClass]="{ 'hide-column': layoutService.peopleTableCompact }">{{
        'LAST LOGIN' | translate | async
      }}</mat-header-cell>
      <mat-cell *matCellDef="let element" [ngClass]="{ 'hide-column': layoutService.peopleTableCompact }">
        {{ element.lastActivity ? timeAgo(element.lastActivity) : '-' }}
      </mat-cell>
    </ng-container>
    <ng-container matColumnDef="progress">
      <mat-cell *matCellDef="let person">
        <div class="progress-indicator">
          <div class="progress-circle">
            <svg viewBox="0 0 24 24" class="circular-chart">
              <!-- Keep the background path for structure but it's now transparent -->
              <path
                class="circle-bg"
                d="M12 2
                  a 10 10 0 0 1 0 20
                  a 10 10 0 0 1 0 -20" />
              <!-- Only the green progress arc will be visible -->
              <path
                class="circle"
                [attr.stroke-dasharray]="person.progressPercentage + ', 100'"
                d="M12 2
                  a 10 10 0 0 1 0 20
                  a 10 10 0 0 1 0 -20" />
            </svg>
          </div>
          <div class="progress-text">
            <span class="items-count">
              <span class="completed-count" style="font-weight: bold; color: white">{{ person.completedCount || 0 }}</span
              ><span class="remaining-text">/{{ person.totalCount || 0 }} {{ 'items' | translate | async }}</span>
            </span>
          </div>
        </div>
      </mat-cell>
    </ng-container>
    <ng-container matColumnDef="grade">
      <mat-cell *matCellDef="let person">
        <div class="row-space-between">
          <span class="grade-text">{{ person.grade }}%</span>
          <ion-button
            class="grade-button"
            [ngClass]="{
              required: person.isGraded === false && person.completedCount === person.totalCount,
              notrequired: !(person.isGraded === false && person.completedCount === person.totalCount),
            }"
            (click)="navigateToGrading(person)"
            >{{ person.isGraded ? ('View' | translate | async) : ('Grade' | translate | async) }}</ion-button
          >
        </div>
      </mat-cell>
    </ng-container>

    <mat-row
      *matRowDef="let person; columns: displayedColumns"
      class="example-element-row"
      [ngClass]="{
        'in-progress-row': person.status === 'InProgress',
        'completed-row': person.status === 'Completed',
        'grading-required-row': person.isGraded === false && person.status === 'Completed',
      }"></mat-row>
  </mat-table>
</div>
