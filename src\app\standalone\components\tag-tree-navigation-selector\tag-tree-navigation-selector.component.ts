import { AfterViewInit, Component, EventEmitter, Input, OnChanges, OnDestroy, Output } from '@angular/core';
import { KeyParentValue, KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { TagService } from '@app/core/services/tag.service';
import { Subject, map, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { AsyncPipe, NgClass } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-tag-tree-navigation-selector',
    templateUrl: './tag-tree-navigation-selector.component.html',
    styleUrls: ['./tag-tree-navigation-selector.component.scss'],
    imports: [IonicModule, NgClass, FormsModule, AsyncPipe, TranslatePipe]
})
export class TagTreeNavigationSelectorComponent implements AfterViewInit, OnChanges, OnDestroy {
  @Input() options!: KeyValue[];
  @Input() noPadding = false;
  @Input() noBorder = false;
  @Input() sidePanelPadding = false;
  @Input() multiple = false;
  @Input() limitTo = 5;
  @Input() displayValue = '';
  @Input() textValue: string;
  @Input() selectedOptions!: KeyValue[];
  @Input() viewType = 0;
  @Input() disabled = false;
  @Output() saveClicked = new EventEmitter<any>();
  @Output() singleTagSelected = new EventEmitter<KeyValue>();
  @Output() multipleTagsSelected = new EventEmitter<string[]>();
  currentLevel = 0;
  hasSelectionChanges = false;
  selectedTagParentTree: KeyParentValue[] = [];
  selectedTagList: KeyValue[] = [];
  selectedTagIdList: string[] = [];
  filterChipList: KeyValue[] = [];
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private dataService: DataService,
    private tagService: TagService
  ) {}

  ngAfterViewInit(): void {
    this.checkViewType();
    this.setTagData();
  }

  ngOnChanges(): void {
    this.setTagData();
  }

  getTagDetails(tagId?: any) {
    //SetCompareSelected-AgainstOptions.
    if (tagId) {
      this.selectedTagIdList = tagId as string[];
      if (this.tagService.isValidGUID(tagId)) {
        this.dataService
          .getTag(tagId)
          .pipe(
            takeUntil(this.componentDestroyed$),
            map(tag => {
              if (tag) {
                return { id: tag.id, value: tag.name, parentId: tag.parentId, tagAncestors: tag.tagAncestors, level: tag.treeLevel } as KeyValue;
              }

              return null;
            })
          )
          .subscribe(tag => {
            if (tag && tag.value) {
              this.displayValue = tag.value;
              this.selectedTagList = [];
              this.selectedTagList.push(tag);
              this.setFilterChipList();
            }
          });
      }
    } else if (this.options?.length > 0 && this.selectedOptions?.length > 0) {
      this.selectedTagIdList = this.selectedOptions.map(x => x.id) as string[];
    }

    this.setTagSelections();
  }

  getTagChildrenOnBackClick() {
    if (this.selectedTagParentTree.length > 0) {
      const existingParent = this.selectedTagParentTree.find(x => x.level === this.currentLevel);
      if (existingParent) {
        this.currentLevel--;
      }

      if (existingParent) {
        this.dataService
          .getTagChildren(existingParent?.parentId ?? null, existingParent?.level, true)
          .pipe(
            takeUntil(this.componentDestroyed$),
            map(tags => {
              return tags.map(tag => {
                return { id: tag.id, value: tag.name, parentId: tag.parentId, tagAncestors: tag.tagAncestors, level: tag.treeLevel, hasChildren: tag.inverseParent ?? false } as KeyValue;
              });
            })
          )
          .subscribe(data => {
            if (data.length > 0) {
              this.options = data;
              this.setSelectedAncestorsTree();
            }
          });
      }
    }
  }

  getTagChildrenOnClick(option: KeyValue) {
    if (option) {
      this.dataService
        .getTagChildren(option.id && option?.id ? (option.id as string) : null, option?.level, true)
        .pipe(
          takeUntil(this.componentDestroyed$),
          map(tags => {
            if (tags) {
              return tags.map(tag => {
                return { id: tag.id, value: tag.name, parentId: tag.parentId, tagAncestors: tag.tagAncestors, level: tag.treeLevel, hasChildren: tag.inverseParent ?? false } as KeyValue;
              });
            } else {
              return tags;
            }
          })
        )
        .subscribe((data: KeyValue[]) => {
          if (data && data.length > 0) {
            //SetSelectedParentTree.
            this.currentLevel = option.level as number;
            const existing = this.selectedTagParentTree?.some(x => x.id === option.id);
            if (!existing) {
              const parentValueIn: KeyParentValue = {
                parentId: option.parentId ?? '',
                id: (option.id as string) ?? '',
                level: option.level ?? 0,
              };

              this.selectedTagParentTree.push(parentValueIn);
            }

            this.options = data;
            this.setSelectedAncestorsTree();
          } else if (!this.multiple) {
            this.singleSelectTag(option);
          }
        });
    }
  }

  setTagData() {
    if (this.options) {
      if (this.multiple) {
        this.options.forEach(option => {
          const parentValueIn: KeyParentValue = {
            parentId: option.parentId ?? '',
            id: (option.id as string) ?? '',
            level: option.level ?? 0,
          };

          this.selectedTagParentTree.push(parentValueIn);
        });
      } else {
        const parentValueIn: KeyParentValue = {
          parentId: this.options[0].parentId ?? '',
          id: (this.options[0].id as string) ?? '',
          level: this.options[0].level ?? 0,
        };

        this.selectedTagParentTree.push(parentValueIn);
      }
    }

    this.getTagDetails(this.textValue ?? null);
  }

  selectMultipleSelectedOption(event: any, selectedOption: KeyValue) {
    if (this.options) {
      const selectedRefOption = this.options.find(x => x.id === selectedOption.id);
      if (selectedRefOption && this.multiple) {
        this.hasSelectionChanges = true;
        //IndeterminateSelection.
        if (selectedOption.disabled) {
          //RemoveAllChildRefsFromSelectedList.
          for (let i = 0; i < this.selectedTagList.length; i++) {
            if (this.selectedTagList[i].tagAncestors?.some(x => x.id === selectedRefOption.id)) {
              this.selectedTagList.splice(i, 1);
              i--;
            }
          }
        }

        if (event.detail.checked === true) {
          selectedRefOption.selected = true;
          this.selectedTagList.push(selectedRefOption);
        } else {
          const index = this.selectedTagList.findIndex(x => x.id === selectedOption.id);
          selectedOption.selected = false;
          this.selectedTagList.splice(index, 1);
        }

        this.setFilterChipList();
        this.setSelectedAncestorsTree();
      }
    }
  }

  setSelectedAncestorsTree() {
    if (this.selectedTagList.length > 0) {
      const ancestors = this.options.filter(x => this.selectedTagList?.some(y => y.parentId === x.id) || this.selectedTagList?.some(y => y.tagAncestors?.some(z => z.id === x.id)));
      if (ancestors != null && ancestors.length > 0) {
        ancestors.forEach(ancestor => {
          const index = this.options.findIndex(x => x.id === ancestor.id);
          const setDisabled = this.selectedTagList?.some(x => x.tagAncestors?.some(y => y.id === ancestor.parentId || (!ancestor.tagAncestors && y.id === ancestor.id)));
          if (setDisabled) {
            this.options[index].disabled = true;
          } else {
            this.options[index].disabled = false;
          }
        });
      }

      //SetCurrentSelected.
      const selectedOptions = this.options.filter(x => this.selectedTagList?.some(y => y.id === x.id));
      if (selectedOptions && selectedOptions.length > 0) {
        selectedOptions.forEach(option => {
          option.selected = true;
        });
      }
    }
  }

  setFilterChipList() {
    if (this.selectedTagList.length > 0) {
      this.filterChipList = this.selectedTagList;
    }
  }

  setTagSelections() {
    if (this.selectedTagIdList.length > 0 && this.multiple) {
      this.dataService
        .getTagsByIds(this.selectedTagIdList)
        .pipe(
          takeUntil(this.componentDestroyed$),
          map(tags => {
            if (tags) {
              return tags.map(tag => {
                return { id: tag.id, value: tag.name, parentId: tag.parentId, tagAncestors: tag.tagAncestors, level: tag.treeLevel } as KeyValue;
              });
            }

            return null;
          })
        )
        .subscribe(tags => {
          if (tags) {
            const mappedArray = tags.map(x => x.value + '');
            this.displayValue = mappedArray.join(', ');
            this.selectedTagList = tags;
            this.setSelectedAncestorsTree();
            this.setFilterChipList();
          }
        });
    }
  }

  singleSelectTag(option?: KeyValue) {
    if (option && option.value) {
      this.singleTagSelected.next(option);
    }
  }

  saveTagList() {
    if (this.selectedTagList && this.multiple === true) {
      this.multipleTagsSelected.next(this.selectedTagList.map(x => x.id as string));
    }

    this.saveClicked.next(null);
  }

  checkViewType() {
    //Disable Click On RepoTemplateBuilder.
    if (this.viewType === 3) {
      this.disabled = true;
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
