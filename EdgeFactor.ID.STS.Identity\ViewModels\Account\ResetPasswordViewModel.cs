using System.ComponentModel.DataAnnotations;

namespace EdgeFactor.ID.STS.Identity.ViewModels.Account
{
    public class ResetPasswordViewModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; }

        [Required]
        [DataType(DataType.Password)]
        [RegularExpression("^(?=.*?[0-9])(?=.*?[A-Z])(?=.*?[a-z])(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$",
            ErrorMessage = "Password must contain a special character, uppercase, lowercase, number and consist of more than 8 characters.")]
        public string Password { get; set; }

        [DataType(DataType.Password)]
        [Compare("Password", ErrorMessage = "Confirm Password and Password do not match.")]
        public string ConfirmPassword { get; set; }

        public string Code { get; set; }
    }
}








