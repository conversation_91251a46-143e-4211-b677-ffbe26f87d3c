<div class="parent-container" [ngClass]="{ 'side-panel-input-padding': sidePanelPadding }">
  <ion-card class="card-content-container">
    <form [formGroup]="itemListForm">
      <ion-reorder-group formArrayName="items" [disabled]="false" (ionItemReorder)="handleReorder($any($event))">
        @for (item of itemFormArray.controls; track item; let i = $index) {
          <div class="inner-container" [formGroupName]="i">
            @if (!disabled) {
              <div class="reorder-icon-container">
                <ion-reorder>
                  <ion-icon name="apps-outline"></ion-icon>
                </ion-reorder>
              </div>
            }
            <ion-input [disabled]="disabled" [placeholder]="'Type here...' | translate | async" formControlName="text"></ion-input>
            @if (!disabled) {
              <ion-button class="remove-button" fill="clear" (click)="remove(i)"><ion-icon name="trash-outline"></ion-icon></ion-button>
            }
          </div>
        }
        @if (!disabled) {
          <div class="icon-container">
            <ion-icon (click)="add()" name="add-circle-outline"></ion-icon>
          </div>
        }
      </ion-reorder-group>
    </form>
  </ion-card>
</div>
