import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { AsyncPipe } from '@angular/common';
import { Component, Input, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { MatChipGrid, MatChipInput, MatChipInputEvent, MatChipRemove, MatChipRow } from '@angular/material/chips';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { IComponent, ITag } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Events } from '@app/core/services/events-service';
import { UserPersonaSelectorComponent } from '@app/standalone/modals/user-persona-selector/user-persona-selector.component';
import { ModalController } from '@ionic/angular';
import { Observable, Subject, map, takeUntil } from 'rxjs';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-persona-tag-popover',
    templateUrl: './persona-tag-popover.component.html',
    styleUrls: ['./persona-tag-popover.component.scss'],
    encapsulation: ViewEncapsulation.None,
    imports: [MatFormField, MatLabel, MatChipGrid, MatChipRow, MatChipRemove, MatIcon, MatChipInput, AsyncPipe, TranslatePipe]
})
export class PersonaTagPopoverComponent implements OnInit, OnDestroy {
  @Input() selectedUserId: string | undefined;
  @Input() component: IComponent | undefined;
  componentDestroyed$: Subject<boolean> = new Subject();
  addOnBlur = true;
  existingTags$: Observable<ITag[]>;
  hasChildrenTags = false;

  readonly separatorKeysCodes = [ENTER, COMMA] as const;

  constructor(
    private dataService: DataService,
    private modalController: ModalController,
    private eventsService: Events
  ) {}

  ngOnInit() {
    this.loadExistingTags();
    this.eventsService.subscribe('persona-tag', () => {
      this.loadExistingTags();
    });
  }

  loadExistingTags() {
    this.existingTags$ = this.dataService.getUserTags(this.selectedUserId, 'persona').pipe(
      map(userTags => {
        this.hasChildrenTags = !userTags.every(tag => tag.tag?.inverseParent);
        return userTags.map(tags => {
          return { name: tags?.tag?.name, id: tags?.tag?.id } as ITag;
        });
      })
    );
  }

  async openTagModal() {
    const modal = await this.modalController.create({
      component: UserPersonaSelectorComponent,
      cssClass: 'user-persona-dialog',
      componentProps: { selectedUserId: this.selectedUserId },
      backdropDismiss: false,
    });

    modal.onDidDismiss().then(value => {
      if (value.data) {
        this.loadExistingTags();
        this.eventsService.publish('persona-tag');
      }
    });

    await modal.present();
  }

  removeDirect(tagId: string) {
    this.existingTags$
      .forEach(tags => {
        tags
          .filter(t => t.parentId === tagId)
          .forEach(t => {
            this.dataService
              .deleteUserPersonaTag(t.id, this.selectedUserId)
              .pipe(takeUntil(this.componentDestroyed$))
              .subscribe(() => {
                this.loadExistingTags();
              });
          });
      })
      .then(() => {
        this.dataService
          .deleteUserPersonaTag(tagId, this.selectedUserId)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(() => {
            this.loadExistingTags();
            this.eventsService.publish('persona-tag');
          });
      });
  }

  add(event: MatChipInputEvent): void {
    event.chipInput!.clear();
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
    this.eventsService.unsubscribe('persona-tag');
  }
}
