import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { IProductOrgDomain, IProductSetting, IUserRole } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { IonicModule } from '@ionic/angular';
import { MatIcon } from '@angular/material/icon';

@Component({
    selector: 'app-product-domain-mapping-settings',
    templateUrl: './product-domain-mapping-settings.component.html',
    styleUrls: ['./product-domain-mapping-settings.component.scss'],
    imports: [IonicModule, MatIcon, AsyncPipe, TranslatePipe]
})
export class ProductDomainMappingSettingsComponent implements OnInit, OnDestroy {
  @Input() productOrgId: string;
  @Input() userRoles: IUserRole[] = [];
  @Input() productSetting: IProductSetting;
  @Output() productOrgDomainsChange = new EventEmitter<IProductOrgDomain[]>();
  componentDestroyed$: Subject<boolean> = new Subject();
  productOrgDomains: IProductOrgDomain[] = [];
  filteredRoles: IUserRole[];
  index = 0;
  constructor(private dataService: DataService) {}

  ngOnInit() {
    if (this.productOrgId) {
      this.getProductOrganizationDomainsById(this.productOrgId);
    }
    this.filteredRoles = this.userRoles.filter(x => ['Learner', 'Instructor'].includes(x.name));
  }

  getProductOrganizationDomainsById(productOrgId: string) {
    if (productOrgId) {
      this.dataService
        .getProductOrganizationDomainsById(productOrgId)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((productOrgDomains: IProductOrgDomain[]) => {
          this.productOrgDomains = productOrgDomains;
        });
    }
  }

  addNewProductDomain() {
    this.index++;
    const newDomain: IProductOrgDomain = {
      domainName: '',
      productOrganizationId: this.productOrgId,
      roleId: '',
      index: this.index,
      isDeleted: false,
    };

    this.productOrgDomains.push(newDomain);
    this.productOrgDomainsChange.emit(this.productOrgDomains);
  }

  removeProductDomain(domain: IProductOrgDomain) {
    this.productOrgDomains.forEach((_domain, index) => {
      if (domain.id && _domain.id === domain.id) {
        _domain.isDeleted = true;
      } else if (_domain.index && _domain.index === domain.index) {
        this.productOrgDomains.splice(index, 1);
        this.index--;
      }
    });
    this.productOrgDomainsChange.emit(this.productOrgDomains);
  }

  manageChange(event: any, domain: IProductOrgDomain, controlType: string) {
    this.productOrgDomains.forEach(_domain => {
      if ((domain.id && _domain.id === domain.id) || (domain.index && _domain.index === domain.index)) {
        if (controlType === 'domain') {
          _domain.domainName = event.detail.value;
        } else if (controlType === 'role') {
          _domain.roleId = event.detail.value;
        }
      }
    });
    this.productOrgDomainsChange.emit(this.productOrgDomains);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
