import { AsyncPipe } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { QuillViewComponent } from 'ngx-quill';
import { ParseContentPipe } from '../../../shared/pipes/parse-content';
import { TranslatePipe } from '../../../shared/pipes/translate';
import { BaseValueComponent } from '@app/standalone/components/base-control/base-value.component';
import { AuthoringHeaderComponent } from '@app/standalone/components/authoring-header/authoring-header.component';




@Component({
    selector: 'app-dynamic-text-value',
    templateUrl: './dynamic-text-value.component.html',
    styleUrls: ['./dynamic-text-value.component.scss'],
    imports: [IonicModule, AuthoringHeaderComponent, QuillViewComponent, AsyncPipe, ParseContentPipe, TranslatePipe]
})
export class DynamicTextValueComponent extends BaseValueComponent implements OnInit {
  @Input() instanceId: string;
  @Input() inheritedPropertyValue: string | null;
  @Input() defaultValue: string | undefined;
  @Input() builderPreviewView = false;

  controlBackground = 'none';
  value = '';
  newText: string = '';

  get defaultText() {
    return this.instanceComponent?.component?.templateField?.defaultText ?? '';
  }

  get valueIsEmptyString() {
    return this.value === '' && this.defaultText === '';
  }

  get componentName(): string {
    return this.instanceComponent?.component?.componentType?.name ?? 'Dynamic Text';
  }

  ngOnInit(): void {
    this.setData();
  }

  override setData() {
    if (this.instanceComponent?.value && this.instanceComponent.value !== '') {
      this.value = this.removeEmptyParagraphs(this.instanceComponent.value);
    } else if (this.inheritedPropertyValue && this.inheritedPropertyValue !== '') {
      this.value = this.removeEmptyParagraphs(this.inheritedPropertyValue);
    } else if (this.defaultValue) {
      this.value = this.removeEmptyParagraphs(this.defaultValue);
    }
  }

  removeEmptyParagraphs(value: string) {
    return value.replace('/<p><br></p>/g', '<br>').replace(/<p>/g, '').replace(/<\/p>/g, '<br>');
  }
}
