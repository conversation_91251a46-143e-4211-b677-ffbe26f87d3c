import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IComponentType, IEarningCriteriaContentIn, IEarningCriteriaIn } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { AsyncPipe } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-complete-sub-type',
    templateUrl: './complete-sub-type.component.html',
    styleUrls: ['./complete-sub-type.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, AsyncPipe, TranslatePipe]
})
export class CompleteSubTypeComponent implements OnInit, OnDestroy {
  @Input() type: string;
  @Input() earningCriteria: IEarningCriteriaIn;
  @Output() criteriaUpdated: EventEmitter<IEarningCriteriaContentIn[]> = new EventEmitter();

  componentTypes: IComponentType[] = [];
  formValueChanges$: Subscription;
  completeSubTypeForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private formBuilder: FormBuilder,
    private dataService: DataService
  ) {}

  ngOnInit() {
    this.getComponentTypes();
  }

  getComponentTypes() {
    this.dataService
      .getComponentTypes()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(componentTypes => {
        if (componentTypes) {
          //FilterOnly = Media And AssesmentBlocks.
          this.componentTypes = componentTypes.filter(x => x.name === 'Assessment Block' || x.name === 'Media Block');
        }

        this.createForm();
      });
  }

  createForm() {
    this.completeSubTypeForm = this.formBuilder.group({
      refId: [this.earningCriteria.earningCriteriaContent?.[0]?.refId],
    });

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.completeSubTypeForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {});
  }

  setSelectedContent(data: any) {
    const criteriaContentIn = {
      id: this.earningCriteria.earningCriteriaContent?.[0]?.id,
      earningCriteriaId: this.earningCriteria.id,
      refId: data.id,
      type: data.type,
      name: data.name,
    } as IEarningCriteriaContentIn;

    const index = this.earningCriteria.earningCriteriaContent?.findIndex(x => x.id === criteriaContentIn.id);

    if (!index || index === -1) {
      if (this.earningCriteria.earningCriteriaContent) {
        this.earningCriteria.earningCriteriaContent.push(criteriaContentIn);
      } else {
        this.earningCriteria.earningCriteriaContent = [criteriaContentIn];
      }
    } else {
      this.earningCriteria.earningCriteriaContent[0] = criteriaContentIn;
    }

    this.criteriaUpdated.emit(this.earningCriteria.earningCriteriaContent);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
