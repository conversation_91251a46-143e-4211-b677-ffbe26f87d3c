import { Component, Input, OnD<PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { IComponent, ITag } from '@app/core/contracts/contract';
import { Subject, takeUntil } from 'rxjs';
import { DataService } from '@app/core/services/data-service';
import { ModalController } from '@ionic/angular';
import { MatChipInputEvent, MatChipGrid, MatChipOption, MatChipRemove, MatChipInput } from '@angular/material/chips';
import { COMMA, ENTER } from '@angular/cdk/keycodes';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { UserPersonaSelectorComponent } from '@app/standalone/modals/user-persona-selector/user-persona-selector.component';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-persona-chip-list',
    templateUrl: './persona-chip-list.component.html',
    styleUrls: ['./persona-chip-list.component.scss'],
    encapsulation: ViewEncapsulation.None,
    imports: [MatFormField, MatChipGrid, MatChipOption, MatChipRemove, MatIcon, MatChipInput, MatLabel, AsyncPipe, TranslatePipe]
})
export class PersonaChipListComponent implements OnDestroy, OnInit {
  @Input() selectedUserId = '';
  @Input() component: IComponent | undefined;
  @Input() existingTags: ITag[];
  componentDestroyed$: Subject<boolean> = new Subject();
  addOnBlur = true;
  hasChildrenTags = false;
  readonly separatorKeysCodes = [ENTER, COMMA] as const;

  constructor(
    private dataService: DataService,
    private modalController: ModalController
  ) {}

  ngOnInit() {
    this.hasChildrenTags = !this.existingTags.every(tag => tag.inverseParent);
  }

  loadExistingTags() {
    this.dataService.getUserTags(this.selectedUserId, 'persona').subscribe(userTags => {
      this.hasChildrenTags = !userTags.every(tag => tag.tag?.inverseParent);
      this.existingTags = userTags.map(tags => {
        return { name: tags?.tag?.name, id: tags?.tag?.id, parentId: tags?.tag?.parentId } as ITag;
      });
    });
  }

  async openTagModal() {
    const modal = await this.modalController.create({
      component: UserPersonaSelectorComponent,
      cssClass: 'user-persona-dialog',
      componentProps: { selectedUserId: this.selectedUserId },
      backdropDismiss: false,
    });

    modal.onDidDismiss().then(value => {
      if (value.data) {
        this.loadExistingTags();
      }
    });

    await modal.present();
  }

  removeDirect(tagId: string) {
    this.existingTags
      .filter(x => x.parentId === tagId)
      .forEach(tags => {
        this.dataService
          .deleteUserPersonaTag(tags.id, this.selectedUserId)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(() => {
            this.loadExistingTags();
          });
      });

    this.dataService
      .deleteUserPersonaTag(tagId, this.selectedUserId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.loadExistingTags();
      });
  }

  add(event: MatChipInputEvent): void {
    const value = (event.value || '').trim();
    event.chipInput!.clear();
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
