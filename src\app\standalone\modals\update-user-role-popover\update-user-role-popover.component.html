<div class="parent-container">
  <ion-row class="row-container">
    <ion-col class="button-container" size="2">
      <mat-icon (click)="cancel()">cancel</mat-icon>
    </ion-col>
    <ion-col size="7">
      <div class="inner-container">
        {{ 'Select Role:' | translate | async }}
        <div class="user-roles-container">
          <ion-select [value]="roleId" (ionChange)="setSelected($event)" placeholder="{{ 'Role' | translate | async }}" interface="popover">
            @for (role of userRoles; track role) {
              <ion-select-option [value]="role.id"> {{ role.name }} </ion-select-option>
            }
          </ion-select>
        </div>
      </div>
    </ion-col>
    <ion-col class="button-container" size="3">
      <ion-button (click)="close()">{{ 'Save' | translate | async }}</ion-button>
    </ion-col>
  </ion-row>
</div>
