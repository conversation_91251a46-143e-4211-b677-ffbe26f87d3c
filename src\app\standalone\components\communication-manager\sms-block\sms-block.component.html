@if (smsBlockForm) {
  <form [formGroup]="smsBlockForm" class="parent-container">
    <ion-grid>
      <ion-card class="sms-card-container">
        <ion-row>
          <ion-col>
            <div class="header-container">
              <h4>{{ 'SMS' | translate | async }}</h4>
            </div>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <div class="message-container">
              <div class="subject-container">
                <ion-label>{{ 'From:' | translate | async }}</ion-label>
                <ion-input formControlName="fromAddress" placeholder="{{ 'Type here...' | translate | async }}"></ion-input>
              </div>
              <div>
                <app-content-quill-editor
                  [hideQuillPersonalize]="hideQuillPersonalize"
                  (dataChanged)="setMessageValue($event)"
                  [value]="smsBlockForm.controls['message'].value"
                  [placeHolder]="'Enter Message'">
                </app-content-quill-editor>
              </div>
            </div>
          </ion-col>
        </ion-row>
      </ion-card>
    </ion-grid>
  </form>
}
