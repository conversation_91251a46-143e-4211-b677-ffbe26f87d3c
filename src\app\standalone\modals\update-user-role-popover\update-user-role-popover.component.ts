import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { IUserRole } from '@app/core/contracts/contract';
import { PopoverController, IonicModule } from '@ionic/angular';
import { Subject } from 'rxjs';
import { MatIcon } from '@angular/material/icon';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-update-user-role',
    templateUrl: './update-user-role-popover.component.html',
    styleUrls: ['./update-user-role-popover.component.scss'],
    imports: [IonicModule, MatIcon, AsyncPipe, TranslatePipe]
})
export class UpdateUserRoleComponent implements OnInit, OnDestroy {
  @Input() roleId: string;
  @Input() userRoles: IUserRole[] = [];
  selectedRoleId: string;
  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(private popoverController: PopoverController) {}

  ngOnInit() {
    this.userRoles = this.userRoles.filter(x => x.name === 'Administrator' || x.name === 'Instructor' || x.name === 'Learner');
  }

  cancel() {
    this.popoverController.dismiss();
  }

  setSelected(event: any) {
    this.selectedRoleId = event.detail.value;
  }

  close() {
    this.popoverController.dismiss(this.selectedRoleId);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
