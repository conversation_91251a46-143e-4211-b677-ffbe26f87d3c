<div class="confirm-user-container">
  <ion-grid>
    <ion-row>
      <ion-col class="header-col">
        <div class="step-heading">{{ 'STEP 2/2' | translate | async }}</div>
        <div class="top-heading">{{ 'Add people to...' | translate | async }}</div>
        <div class="sub-heading">
          <span>{{ "You're about to add" | translate | async }} {{ selectedUsers.length }} {{ 'people to' | translate | async }} {{ name | translate | async }}</span>
        </div>
      </ion-col>
    </ion-row>
  </ion-grid>
  <ion-content>
    <ion-grid>
      @if (selectedUsers.length > 0) {
        <ion-list>
          @for (user of selectedUsers; track user) {
            <ion-item>
              <ion-checkbox slot="start" checked="{{ user.selected }}" (ionChange)="addUser($event, user)"></ion-checkbox>
              <ion-label>
                <span class="heading">{{ user.name }}</span>
                <div class="sub-heading">
                  <span>{{ user.roleName | translate | async }}</span>
                </div>
              </ion-label>
            </ion-item>
          }
        </ion-list>
      }
    </ion-grid>
  </ion-content>

  <ion-footer>
    <div class="footer-content">
      <ion-row>
        <ion-col class="cancel-col">
          <ion-button fill="clear" (click)="close()">{{ 'Cancel' | translate | async }}</ion-button>
        </ion-col>
        <ion-col class="add-col">
          <ion-button (click)="add()">{{ 'Add' | translate | async }}</ion-button>
        </ion-col>
      </ion-row>
    </div>
  </ion-footer>
</div>
