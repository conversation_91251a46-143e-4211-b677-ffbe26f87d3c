import { Component, EventEmitter, Input, OnChang<PERSON>, OnD<PERSON>roy, OnInit, Output, SimpleChanges } from '@angular/core';
import { IEarningCriteria, IEarningCriteriaContent, IEarningCriteriaContentIn, IEarningCriteriaIn, IEarningCriteriaType } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { PopoverController, IonicModule } from '@ionic/angular';
import { Observable, Subject, debounceTime, forkJoin, takeUntil } from 'rxjs';
import { deepCopy } from '@angular-devkit/core/src/utils/object';
import { DiffArray, diff } from 'deep-diff';
import { CompletedObjectsComponent } from './completed-objects/completed-objects.component';
import { CompletePageComponent } from './complete-page/complete-page.component';
import { CompleteContentComponent } from './complete-content/complete-content.component';
import { CompleteAtleastComponent } from './complete-atleast/complete-atleast.component';
import { CreateAtleastComponent } from './create-atleast/create-atleast.component';
import { CompleteCreatedByComponent } from './complete-created-by/complete-created-by.component';
import { CompleteSubTypeComponent } from './complete-sub-type/complete-sub-type.component';
import { CompleteDuringMonthComponent } from './complete-during-month/complete-during-month.component';
import { CompletePublishedWithinComponent } from './complete-published-within/complete-published-within.component';
import { EarnXpPointsComponent } from './earn-xp-points/earn-xp-points.component';
import { AssessmentProficiencyComponent } from './assessment-proficiency/assessment-proficiency.component';
import { CompleteStartedWithinComponent } from './complete-started-within/complete-started-within.component';
import { MatIcon } from '@angular/material/icon';
import { EarningCriteriaComponent } from '../earning-criteria/earning-criteria.component';
import { AddEarningCriteriaPopoverComponent } from '@app/standalone/modals/add-earning-criteria-popover/add-earning-criteria-popover.component';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-criteria-manager-component',
    templateUrl: './criteria-manager.component.html',
    styleUrls: ['./criteria-manager.component.scss'],
    imports: [
        IonicModule,
        CompletedObjectsComponent,
        CompletePageComponent,
        CompleteContentComponent,
        CompleteAtleastComponent,
        CreateAtleastComponent,
        CompleteCreatedByComponent,
        CompleteSubTypeComponent,
        CompleteDuringMonthComponent,
        CompletePublishedWithinComponent,
        EarnXpPointsComponent,
        AssessmentProficiencyComponent,
        CompleteStartedWithinComponent,
        MatIcon,
        EarningCriteriaComponent,
        AsyncPipe,
        TranslatePipe,
    ]
})
export class CriteriaManagerComponent implements OnInit, OnChanges, OnDestroy {
  @Input() featureType: string;
  @Input() instanceId: string | null | undefined;
  @Input() status: string | undefined;
  @Output() emitCriteriaUpdated: EventEmitter<any> = new EventEmitter();
  earningCriteriaTypes: IEarningCriteriaType[] = [];
  earningCriteria: IEarningCriteria[] = [];
  earningCriteriaCopy: IEarningCriteria[] = [];

  componentDestroyed$: Subject<boolean> = new Subject();
  private criteriaChanged$ = new Subject<IEarningCriteria[]>();

  constructor(
    private dataService: DataService,
    private popoverController: PopoverController
  ) {}

  ngOnInit() {
    this.getEarningCriteriaTypes();
    this.getEarningCriteriaByInstanceId();

    // This component is self contained
    this.criteriaChanged$.pipe(debounceTime(700), takeUntil(this.componentDestroyed$)).subscribe((event: any) => {
      this.save(event);
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['status']) {
      this.getEarningCriteriaByInstanceId();
    }
  }

  getEarningCriteriaByInstanceId() {
    if (this.instanceId) {
      this.dataService
        .getEarningCriteriaByInstanceId(this.instanceId)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((earningCriteria: IEarningCriteria[]) => {
          if (earningCriteria && earningCriteria.length > 0) {
            this.earningCriteria = earningCriteria;
            this.sortEarningCriteria();
          }
        });
    }
  }

  getEarningCriteriaTypes() {
    this.dataService
      .getEarningCriteriaTypes()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((earningCriteriaTypes: IEarningCriteriaType[]) => {
        if (earningCriteriaTypes) {
          //FilterOut CompleteStartedWithin For Now As Discussed/ RyanPieter
          this.earningCriteriaTypes = earningCriteriaTypes.filter(x => x.name !== 'CompleteStartedWithin');
          this.setCompletionCriteriaTypes();
        }
      });
  }

  setCompletionCriteriaTypes() {
    //Unique only to the Achievement Completion Feature currently.
    //OnlyCompletionType Options.
    /*if (this.featureType === 'Achievement Completion') {
      this.earningCriteriaTypes = this.earningCriteriaTypes.filter((x) => x.name === 'CompleteObjects' || x.name === 'CompletePage');
    }*/
  }

  setCriteriaRowSortOrderDirect(event: any) {
    if (this.earningCriteria !== undefined && this.earningCriteria.length > 0) {
      //Find.
      const criteria = this.earningCriteria[event.detail.from];
      this.earningCriteria.splice(event.detail.from, 1);
      this.earningCriteria.splice(event.detail.to, 0, criteria);

      //Set.
      this.earningCriteria.forEach(criteriaIn => {
        const newIndex = this.earningCriteria?.findIndex(x => x.id === criteriaIn.id);
        criteriaIn.sortOrder = newIndex as number;
      });

      if (criteria) {
        event.detail.complete();
        this.emitCriteriaManagerChanges();
      }
    }
  }

  async addEarningCriteriaPopOver(event: any) {
    const popover = await this.popoverController.create({
      component: AddEarningCriteriaPopoverComponent,
      cssClass: 'criteria-add-popover',
      componentProps: { earningCriteriaTypes: this.earningCriteriaTypes },
      event: event,
      animated: true,
      side: 'bottom',
    });

    popover.onDidDismiss().then(result => {
      if (result.data) {
        const addCriteria = {
          instanceId: this.instanceId,
          earningCriteriaType: result.data,
          earningCriteriaContent: [],
          sortOrder: 0,
        } as IEarningCriteriaIn;

        this.addEarningCriteria(addCriteria as IEarningCriteria);
      }
    });

    await popover.present();
  }

  addEarningCriteria(criteriaIn: IEarningCriteria) {
    this.earningCriteria.push(criteriaIn);
    this.emitCriteriaManagerChanges();
  }

  removeEarningCriteria(removeCriteria: IEarningCriteria) {
    const indexIn = this.earningCriteria.indexOf(removeCriteria);
    if (indexIn > -1) {
      this.earningCriteria[indexIn].isDeleted = true;
    }

    this.emitCriteriaManagerChanges();
  }

  sortEarningCriteria() {
    if (this.earningCriteria && this.earningCriteria.length > 0) {
      this.earningCriteria = this.earningCriteria.sort((n1, n2) => n1.sortOrder - n2.sortOrder);
      //SetOriginalArrayCompare
      this.earningCriteriaCopy = deepCopy(this.earningCriteria);
    }
  }

  setCriteriaUpdated(earningCriteriaContent: IEarningCriteriaContentIn[], earningCriteria: IEarningCriteria) {
    if (earningCriteriaContent) {
      earningCriteria.earningCriteriaContent = earningCriteriaContent as IEarningCriteriaContent[];
      this.emitCriteriaManagerChanges();
    }
  }

  emitCriteriaManagerChanges() {
    //Only Emit Modified List/Compare Using DiffArrayCompare.
    const deepDiffCompareList = diff(this.earningCriteriaCopy, this.earningCriteria);
    if (deepDiffCompareList) {
      const modifiedList = deepDiffCompareList as DiffArray<IEarningCriteria>[];
      if (modifiedList.length > 0) {
        //Kind A/N = New Or Modified.
        const indexList = modifiedList.map(x => {
          if (x.path) {
            return x.path[0];
          }

          return x.index;
        });
        const criteriaIn = this.earningCriteria.filter((element, index) => indexList.includes(index));
        this.emitCriteriaUpdated.emit(JSON.stringify(criteriaIn));
        this.criteriaChanged$.next(criteriaIn);
      }
    }
  }

  save(criterias: IEarningCriteria[]) {
    const requestList: Observable<boolean>[] = [];
    //Values In String/Json Context (Type: Array/Multiple).
    const earningCriteriaIn = criterias;
    //DELETE
    if (earningCriteriaIn?.some(x => x.isDeleted === true)) {
      requestList.push(this.dataService.removeCriteria(earningCriteriaIn.filter(x => x.isDeleted === true)));
    }
    //ADD
    if (earningCriteriaIn?.some(x => !x.id && x.isDeleted !== true)) {
      requestList.push(this.dataService.addCriteria(earningCriteriaIn.filter(x => !x.id && x.isDeleted !== true)));
    }
    //UPDATE
    if (earningCriteriaIn?.some(x => x.id && x.isDeleted !== true)) {
      requestList.push(this.dataService.updateEarningCriteria(earningCriteriaIn.filter(x => x.id !== null && x.isDeleted !== true)));
    }

    forkJoin(requestList)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.getEarningCriteriaByInstanceId();
        // SYNC : This is big that is the reason for it being seperate
        this.dataService.syncEarningCriteria().pipe(takeUntil(this.componentDestroyed$)).subscribe();
      });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
