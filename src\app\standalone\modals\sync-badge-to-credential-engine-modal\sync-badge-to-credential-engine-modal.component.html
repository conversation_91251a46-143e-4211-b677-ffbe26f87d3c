<div class="badge-search-container">
  <form [formGroup]="searchForm">
    <ion-grid>
      <ion-row>
        <ion-col class="header-col">
          @if(showSyncedBadges){
          <div class="top-heading">{{ 'Update the status of synced badges in the Credential Engine.' | translate | async }}</div>
          <div class="sub-heading">
            <span>{{ 'Status will be auto-saved when modified.' | translate | async }}</span>
          </div>
          }@else if(!showSyncedBadges){
          <div class="top-heading">{{ 'What badges do you want to sync with Credential Engine?' | translate | async }}</div>
          <div class="sub-heading">
            <span>{{ 'Select the badge(s) you want to sync' | translate | async }}</span>
          </div>
          }
        </ion-col>
      </ion-row>
      <ion-row class="search-bar-row">
        <ion-col size="7">
          <ion-searchbar color="dark" formControlName="searchValue" (ionChange)="searchRepoValue()" type="search" placeholder="{{ 'Search Badge' | translate | async }}"
            showCancelButton="focus" debounce="600">
          </ion-searchbar>
        </ion-col>
        <ion-col size="5" class="end-col-buttons">
          <div class="buttons">
            @if(showSyncedBadges){
            <ion-button (click)="toggleSyncButton()"> {{ 'Sync Badges' | translate | async }} </ion-button>
            } @else if(!showSyncedBadges){
            <ion-button (click)="toggleSyncButton()"> {{ 'Edit Badge Status' | translate | async }}</ion-button>
            }
          </div>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
  <ion-content>
    <ion-grid>
      @if (badges && badges.length > 0) {
      @if(showSyncedBadges){
      <ng-container *ngTemplateOutlet="syncedBadges"></ng-container>
      }
      @else{
      <ng-container *ngTemplateOutlet="unsyncedBadges"></ng-container>
      }
      }
      @if (badges && badges.length === 0) {
      @if (noBadgesInTable === true) {
      <div class="no-badge-container">
        <span class="no-badge-text">{{ 'No Badges' | translate | async }}</span>
      </div>
      }
      }
    </ion-grid>
  </ion-content>

  <ion-footer>
    <div class="footer-content">
      <ion-row>
        <ion-col class="cancel-col">
          <ion-button fill="clear" (click)="close()">{{ 'Cancel' | translate | async }}</ion-button>
        </ion-col>
        <ion-col class="add-col">
          <div class="inner-container">
            @if(!showSyncedBadges){
            <ion-badge>{{ selectedBadges.length }}</ion-badge>
            <ion-button (click)="syncSelectedBadges()"> {{ 'Sync' | translate | async }} </ion-button>
            }
          </div>
        </ion-col>
      </ion-row>
    </div>
  </ion-footer>

  <ng-template #syncedBadges>
    <ion-list>
      @for (badge of badges; track badge) {
      <ion-item>
        <ion-label>
          <span class="heading">{{ badge.name }}</span>
          <app-select-option-control
            [toolTip]="'Select Status' | translate | async"
            [placeHolder]="'Select Status' | translate | async"
            [label]="'Badge Status' | translate | async"
            [backgroundColor]="controlBackground"
            [textValue]="badge.status ?? ''"
            [options]="badgeStatusTypes"
            (valueChanged)="updateStatus($event, badge.id)"></app-select-option-control>
        </ion-label>
      </ion-item>
      }
    </ion-list>
  </ng-template>
  <ng-template #unsyncedBadges>
    <ion-list>
      <ion-item class="select-container" (ionChange)="addAllBadgesToList($event, badges)">
        <div class="select-all">
          <ion-checkbox slot="start"></ion-checkbox>
          <ion-label>
            <span class="heading">{{ 'Select All' | translate | async }}</span>
          </ion-label>
        </div>
      </ion-item>
      @for (badge of badges; track badge) {
      <ion-item (ionChange)="addBadgeToList($event, badge)">
        <ion-checkbox [checked]="badge.selected" slot="start"></ion-checkbox>
        <ion-label>
          <div class="text-container">
            <span class="heading">{{ badge.name | translate | async }}</span>
            <span class="newBadge">{{badge.published ? ('Updated - Sync Required' | translate | async) : ("Not Synced" | translate | async)}}</span>
          </div>
        </ion-label>
      </ion-item>
      }
    </ion-list>
  </ng-template>
</div>
