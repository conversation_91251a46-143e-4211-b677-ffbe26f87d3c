@if (mediaBlockForm) {
  <form class="mediablock-form-container" [formGroup]="mediaBlockForm">
    <ion-grid>
      <ion-row>
        <ion-col class="checkbox-container" style="padding: 1em">
          <div>
            <ion-item>
              <ion-label>{{ 'Upload from device' | translate | async }}</ion-label>
              <ion-checkbox class="checkbox" slot="start" color="primary" formControlName="isSrcDevice"></ion-checkbox>
            </ion-item>
            <ion-item>
              <ion-label>{{ 'Select from media Repository' | translate | async }}</ion-label>
              <ion-checkbox class="checkbox" slot="start" color="primary" formControlName="isSrcRepository"></ion-checkbox>
            </ion-item>
            <ion-item>
              <ion-label>{{ 'With an embed code' | translate | async }}</ion-label>
              <ion-checkbox class="checkbox" slot="start" color="primary" formControlName="isSrcEmbedCode"></ion-checkbox>
            </ion-item>
          </div>
        </ion-col>
      </ion-row>
      <app-field-editor-base [fieldForm]="mediaBlockForm"></app-field-editor-base>
      <ion-row class="file-size-parent-container">
        <ion-col size="12">
          <ion-item #control>
            <ion-label position="stacked" class="label-header">
              {{ 'File Size' | translate | async }}
              <span class="reqAsterisk">
                <ion-icon name="information-circle-outline"></ion-icon>
              </span>
            </ion-label>
            <ion-row>
              <ion-col size="5">
                <app-text-input-control
                  [isCustom]="true"
                  [label]="'Min file size'"
                  [backgroundColor]="'#292929'"
                  [placeHolder]="'Kb'"
                  formControlName="minFileSize"
                  type="number"></app-text-input-control>
              </ion-col>
              <ion-col size="2"> <h6 style="text-align: center">to</h6> </ion-col>
              <ion-col size="5">
                <app-text-input-control
                  [isCustom]="true"
                  [label]="'Max file size'"
                  [backgroundColor]="'#292929'"
                  [placeHolder]="'Mb'"
                  formControlName="maxFileSize"
                  type="number"></app-text-input-control>
              </ion-col>
            </ion-row>
          </ion-item>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-select-option-control
            [multiple]="true"
            [label]="'File Types'"
            [options]="builderService.acceptedVideoFormats"
            [backgroundColor]="'#333333'"
            [disabled]="false"
            formControlName="fileTypeBw"></app-select-option-control>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <ion-card class="slide-fields-container-card">
            <ion-card-content>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isBlockRequired">{{ 'Make this block required for completion' | translate | async }}</mat-slide-toggle>
              @if (mediaBlockForm.controls['isBlockRequired'].value === true) {
                <ion-row class="percentage-container-row">
                  <ion-col class="counter-col" size="2">
                    <ion-input type="number" (ionChange)="checkPercentageInputLength($event)" min="0" max="100" placeHolder="%" formControlName="percentageToComplete"> </ion-input>
                  </ion-col>
                  <ion-col class="sub-text-col" size="10">{{ 'Percentage required to complete' | translate | async }}</ion-col>
                </ion-row>
              }
              <app-field-checkboxes-base [baseForm]="mediaBlockForm"></app-field-checkboxes-base>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isAuthorRequired">{{ 'Require author to build out this block' | translate | async }}</mat-slide-toggle>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isInherit">{{ 'System Property' | translate | async }}</mat-slide-toggle>
              <mat-slide-toggle style="width: 100%" color="primary" formControlName="isVisibleRepository">{{ 'Is Visible in Repository' | translate | async }}</mat-slide-toggle>
              @if (isInheritControl.value) {
                <app-system-property-selector [templateField]="component.templateField" [formGroup]="mediaBlockForm"></app-system-property-selector>
              }
            </ion-card-content>
          </ion-card>
        </ion-col>
      </ion-row>
      <ion-row>
        <ion-col>
          <app-text-input-control [backgroundColor]="'#292929'" [label]="'Upgrade Message'" [placeHolder]="'Add upgrade message...'" formControlName="upgradeMessage"></app-text-input-control>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
}
