import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { UntypedFormControl, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IAsset } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Subject, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';

@Component({
    selector: 'app-search-select-asset',
    templateUrl: './search-select-asset.component.html',
    styleUrls: ['./search-select-asset.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, AsyncPipe, TranslatePipe]
})
export class SearchSelectAssetComponent implements OnInit, OnDestroy {
  @Output() assetSelected: EventEmitter<IAsset> = new EventEmitter();
  componentDestroyed$: Subject<boolean> = new Subject();
  searchForm: UntypedFormGroup;
  assetSearch: UntypedFormControl;
  assets: IAsset[];

  constructor(private dataService: DataService) {}

  @Input() onClose = () => {};

  ngOnInit() {
    this.createFormControls();
    this.createForm();
    this.searchAssets();
  }

  createFormControls() {
    this.assetSearch = new UntypedFormControl('');
  }

  createForm() {
    this.searchForm = new UntypedFormGroup({
      assetSearch: this.assetSearch,
    });
  }

  searchAssets() {
    this.assets = [];
    if (!this.assetSearch.value || this.assetSearch.value.length < 3) {
      return;
    }
    const searchValue = encodeURIComponent(this.assetSearch.value);
    this.dataService
      .getAssets(searchValue)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((res: IAsset[]) => {
        this.assets = res;
      });
  }

  addAsset(asset: IAsset) {
    this.assetSelected.emit(asset);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
