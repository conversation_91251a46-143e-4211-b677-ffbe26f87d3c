import { Component, Inject, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { IInstanceTagIn, ITag, ITagIn, Level } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { AuthService } from '@app/core/services/auth-service';
import { Observable, Subject, takeUntil, of } from 'rxjs';
import { UntypedFormControl, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { map } from 'rxjs/operators';
import { IonicModule } from '@ionic/angular';
import { NgClass, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-tag-selector-dialog',
    templateUrl: './tag-selector-dialog.component.html',
    styleUrls: ['./tag-selector-dialog.component.scss'],
    encapsulation: ViewEncapsulation.None,
    imports: [IonicModule, FormsModule, ReactiveFormsModule, NgClass, AsyncPipe, TranslatePipe]
})
export class TagSelectorDialogComponent implements OnInit, OnDestroy {
  tags$: Observable<ITag[]>;
  hasChildren: boolean;
  page = 1;
  selectedIndex: number | null;
  componentDestroyed$: Subject<boolean> = new Subject();
  parentIdList: string[] = [];
  campaignId: string | null;
  searchForm: UntypedFormGroup;
  searchControl: UntypedFormControl;
  userTagIds: string[] = [];

  constructor(
    public dialogRef: MatDialogRef<TagSelectorDialogComponent>,
    private authService: AuthService,
    private dataService: DataService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    dialogRef.disableClose = true;
    dialogRef.backdropClick().subscribe(() => {
      dialogRef.close();
    });
  }

  ngOnInit() {
    this.loadData();
    this.createFormControls();
    this.createForm();
    if (this.authService?.guestUserContext?.browsingAs && this.authService?.guestUserContext?.id) {
      this.data.selectedUserId = this.data?.selectedUserId.length > 0 ? this.data?.selectedUserId : this.authService?.guestUserContext?.id;
    }
  }

  createFormControls() {
    this.searchControl = new UntypedFormControl('');
  }

  createForm() {
    this.searchForm = new UntypedFormGroup({
      searchControl: this.searchControl,
    });
  }

  onClose() {
    this.dialogRef.close();
  }

  onFinish() {
    const isDone = true;
    this.tags$.subscribe((tags: ITag[]) => {
      if (tags.length > 0) {
        this.page = tags[0].treeLevel;
      }
    });
    if (this.data?.campaignId && this.data?.dropDownLinkType !== 'Campaign User Tags' && this.searchControl?.value) {
      const newTag = { parentId: this.data?.tagId, name: this.searchControl?.value, treeLevel: this.page + 1 } as ITagIn;
      this.dataService
        .addTag(newTag)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((tag: ITag) => {
          const tagId = JSON.stringify(tag.id);
          this.dataService.updateCampaignTag(this.data?.campaignId, tagId).subscribe(() => {
            this.dialogRef.close(tagId);
          });
        });
    } else if (this.data?.dropDownLinkType === 'Organizations') {
      this.dataService.addOrganizationToInstance(this.data?.instanceId, this.data?.tagId).subscribe(() => {
        this.dialogRef.close(this.data?.tagId);
      });
    } else if (this.data?.dropDownLinkType === 'Instances') {
      this.dataService.addInstanceToOrganization(this.data?.organizationId, this.data?.tagId).subscribe(() => {
        this.dialogRef.close(this.data?.tagId);
      });
    } else {
      this.dialogRef.close(isDone);
    }
  }

  onSave() {
    this.dialogRef.close(this.data.tagId);
  }

  onBack() {
    if (this.parentIdList.length > 0) {
      const firstIn = this.parentIdList.pop();
      this.data.tagId = firstIn;
      this.loadData();
      this.page--;
    } else {
      this.dialogRef.close();
    }
    this.searchForm.reset();
  }

  customSort(a: Level, b: Level): number {
    const sortOrder = ['Pre-Kindergarten', 'Kindergarten', 'Primary', 'Intermediate', 'High School', 'Postsecondary'];
    return sortOrder.indexOf(a.name) - sortOrder.indexOf(b.name);
  }

  loadData() {
    if (this.data?.view === 'isTag' || this.data?.view === 'isOrganizations' || this.data?.view === 'isInstances') {
      if (this.data?.campaignId && this.data?.dropDownLinkType === 'Campaign User Tags') {
        this.tags$ = this.dataService.getTagsForCampaign(this.data?.campaignId, this.data?.tagId, null).pipe(takeUntil(this.componentDestroyed$));
      } else if (this.data?.dropDownLinkType === 'Criteria Manager') {
        this.tags$ = this.dataService
          .getCriteriaTags(this.data?.instanceId, this.data?.tagId === '' ? '26F98B8E-C4F7-47EE-90C0-E5DD9EE7D7E0' : this.data?.tagId, '')
          .pipe(takeUntil(this.componentDestroyed$));
      } else if (this.data?.dropDownLinkType === 'Organization Tags' && this.data?.organizationId) {
        this.tags$ = this.dataService.getOrganizationTagChildren(this.data?.organizationId, this.data?.tagId).pipe(takeUntil(this.componentDestroyed$));
        // Load all organizations
      } else if (this.data?.dropDownLinkType === 'Organizations') {
        this.dataService
          .searchOrganizations(this.searchControl?.value ?? '', 0, 50)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(organizations => {
            this.tags$ = of(
              organizations.map(x => {
                return { id: x.id, name: x.name } as ITag;
              })
            );
          });
        // Load all instances
      } else if (this.data?.dropDownLinkType === 'Instances') {
        this.dataService
          .searchInstances(null, this.searchControl?.value ?? '')
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe(instances => {
            this.tags$ = of(
              instances.map(x => {
                return { id: x.id, name: x.title } as ITag;
              })
            );
          });
      } else {
        this.tags$ = this.dataService
          .getInstanceComponentTagChildren(this.data?.tagId, this.data?.instanceId ?? null, this.data?.componentId ?? null, this.data?.dropDownLinkType, this.data?.selectedUserId || '', '')
          .pipe(
            takeUntil(this.componentDestroyed$),
            map(tags => {
              if (this.data?.featureTypeName === 'Modifiable Learning Container Pages') {
                const filteredTags = tags.filter(x => x.name.toLowerCase() !== 'rostered');
                const sortedTags = filteredTags.sort(this.customSort);
                return sortedTags;
              }

              this.page = tags[0]?.treeLevel ?? 1;

              return tags;
            })
          );
      }
    } else if (this.data?.dropDownLinkType === 'User Tags' && this.data?.view !== 'isTag') {
      this.dataService
        .getUserTags(this.data?.selectedUserId)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(tags => {
          this.userTagIds = tags.filter(x => x.type !== 'Persona').map(x => x.tagId);
        });
    } else if (this.data?.dropDownLinkType === 'Criteria Manager') {
      this.dataService
        .getCriteriaTags(this.data?.instanceId)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(tags => {
          this.userTagIds = tags.map(x => x.id);
        });
    } else {
      this.tags$ = this.dataService.getTagChildrenSearch(this.data?.tagId, null).pipe(takeUntil(this.componentDestroyed$));
    }
  }

  searchResults() {
    if (this.data?.campaignId && this.data?.dropDownLinkType === 'Campaign User Tags') {
      this.tags$ = this.dataService.getTagsForCampaign(this.data?.campaignId, this.data?.tagId, this.searchControl.value).pipe(takeUntil(this.componentDestroyed$));
    } else if (this.data?.view === 'isTag' && this.data?.dropDownLinkType !== 'Criteria Manager') {
      this.tags$ = this.dataService
        .getInstanceComponentTagChildren(
          this.data?.tagId,
          this.data?.instanceId ?? null,
          this.data?.componentId ?? null,
          this.data?.dropDownLinkType,
          this.data?.selectedUserId || '',
          this.searchControl.value
        )
        .pipe(takeUntil(this.componentDestroyed$));
    } else if (this.data?.dropDownLinkType === 'Criteria Manager') {
      this.tags$ = this.dataService.getCriteriaTags(this.data?.instanceId, this.data?.tagId, this.searchControl.value).pipe(takeUntil(this.componentDestroyed$));

      // Search for specific organizations
    } else if (this.data?.dropDownLinkType === 'Organizations') {
      this.dataService
        .searchOrganizations(this.searchControl?.value ?? '', 0, 50)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(organizations => {
          this.tags$ = of(
            organizations.map(x => {
              return { id: x.id, name: x.name } as ITag;
            })
          );
        });

      // Search for specific instances
    } else if (this.data?.dropDownLinkType === 'Instances') {
      this.dataService
        .searchInstances(null, this.searchControl?.value ?? '')
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(instances => {
          this.tags$ = of(
            instances.map(x => {
              return { id: x.id, name: x.title } as ITag;
            })
          );
        });
    } else {
      this.tags$ = this.dataService.getTagChildrenSearch(this.data?.tagId, this.searchControl.value).pipe(takeUntil(this.componentDestroyed$));
    }
  }

  setPropertyTag(tagId: string) {
    this.data.tagId = tagId;
  }

  setRow(_index: number, tag: ITag) {
    this.selectedIndex = _index;
    this.setPropertyTag(tag.id);
  }

  nextTag(tag: ITag) {
    this.searchControl.setValue('');
    this.searchForm.reset();
    if (tag.hasInstanceTags || tag.hasUserTags || this.data?.view === 'isFilter' || tag.inverseParent) {
      this.data.tagId = tag.id;
      this.tagHasChildren().subscribe(res => {
        if (res && this.page <= tag.treeLevel) {
          this.page++;
          this.loadData();
        }
      });

      if (tag.parentId != null) {
        this.parentIdList.push(tag.parentId);
      }
      this.data.tagId = '';
      this.selectedIndex = null;
    }
  }

  tagSelection(event: any, tag: ITag) {
    const instanceTag: IInstanceTagIn = {
      tagId: tag.id,
      instanceId: this.data.instanceId,
      componentId: this.data.componentId,
    };

    if (this.data?.dropDownLinkType === 'User Tags') {
      this.userTagIds = this.userTagIds.filter(x => x !== tag.id);
      if (event.detail.checked) {
        this.userTagIds.push(tag.id);
      } else {
        const index = this.userTagIds.findIndex(x => x === tag.id);
        if (index !== -1) {
          this.userTagIds.splice(index, 1);
        }
      }

      if (this.userTagIds) {
        this.dataService.updateRemoveUserTags(this.userTagIds, this.data?.selectedUserId).pipe(takeUntil(this.componentDestroyed$)).subscribe();
      }
    } else if (this.data?.dropDownLinkType === 'Criteria Manager') {
      this.userTagIds = this.userTagIds.filter(x => x !== tag.id);
      if (event.detail.checked) {
        this.userTagIds.push(tag.id);
      } else {
        const index = this.userTagIds.findIndex(x => x === tag.id);
        if (index !== -1) {
          this.userTagIds.splice(index, 1);
        }
      }

      if (this.userTagIds) {
        this.dataService.updateRemoveCriteriaTags(this.data?.instanceId, this.userTagIds).pipe(takeUntil(this.componentDestroyed$)).subscribe();
      }
    } else if (this.data?.campaignId && this.data?.dropDownLinkType === 'Campaign User Tags') {
      if (event.detail.checked) {
        const campaignTags: string[] = [tag.id];
        this.dataService.updateCampaignTags(campaignTags, this.data?.campaignId).pipe(takeUntil(this.componentDestroyed$)).subscribe();
      } else {
        this.dataService.deleteCampaignUserTag(tag.id, this.data?.campaignId).pipe(takeUntil(this.componentDestroyed$)).subscribe();
      }
    } else if (this.data?.campaignId && this.data?.systemPropertyType === 'Campaign') {
      if (event.detail.checked) {
        const tagId = JSON.stringify(tag.id);
        this.dataService.updateCampaignTag(this.data?.campaignId, tagId).pipe(takeUntil(this.componentDestroyed$)).subscribe();
      } else {
        this.dataService.deleteCampaignTag(this.data?.campaignId).pipe(takeUntil(this.componentDestroyed$)).subscribe();
      }
    } else if (this.data?.dropDownLinkType === 'Organization Tags' && this.data?.organizationId) {
      const tagId = JSON.stringify(tag.id);
      this.dataService.updateRemoveOrganizationTag(this.data?.organizationId, tagId).pipe(takeUntil(this.componentDestroyed$)).subscribe();
    } else {
      if (event.detail.checked) {
        this.dataService.addToInstanceTag(instanceTag).pipe(takeUntil(this.componentDestroyed$)).subscribe();
      } else {
        this.dataService.deleteInstanceTag(instanceTag.tagId, instanceTag.instanceId, instanceTag.componentId).pipe(takeUntil(this.componentDestroyed$)).subscribe();
      }
    }

    this.searchControl.setValue('');
    this.searchForm.reset();
  }

  tagHasChildren(): Observable<boolean> {
    return this.dataService.tagHasChildren(this.data.tagId);
  }

  ngOnDestroy(): void {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
