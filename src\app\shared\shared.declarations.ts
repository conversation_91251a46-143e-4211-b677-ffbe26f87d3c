import { CdkTableModule } from '@angular/cdk/table';
import { MatBadgeModule } from '@angular/material/badge';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipsModule } from '@angular/material/chips';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatDialogModule } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatListModule } from '@angular/material/list';
import { MatMenuModule } from '@angular/material/menu';
import { MatPaginatorModule } from '@angular/material/paginator';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { MatSortModule } from '@angular/material/sort';
import { MatStepperModule } from '@angular/material/stepper';
import { MatTableModule } from '@angular/material/table';
import { MatTabsModule } from '@angular/material/tabs';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MaskitoModule } from '@maskito/angular';
import { NgxChartsModule } from '@swimlane/ngx-charts';
import { QRCodeComponent } from 'angularx-qrcode';
import { ColorPickerModule } from 'ngx-color-picker';
import { NgPipesModule } from 'ngx-pipes';
import { QuillModule } from 'ngx-quill';
import { FileSizePipe } from './pipes/file-size-display';
import { IndexToLetterPipe } from './pipes/index-to-letter';
import { MapToIterable } from './pipes/map-to-iterable';
import { MemoizePipe } from './pipes/memoize';
import { PageViewComponentFilterPipe } from './pipes/page-view-component-filter';
import { ParseContentPipe } from './pipes/parse-content';
import { TabFilterPipe } from './pipes/tab-filter';
import { DescriptorPipe } from './pipes/descriptor';
import { TranslatePipe } from './pipes/translate';

export const sharedPipes: any[] = [MapToIterable, FileSizePipe, ParseContentPipe, TabFilterPipe, MemoizePipe, IndexToLetterPipe, PageViewComponentFilterPipe, DescriptorPipe, TranslatePipe];

export const material: any[] = [
  MatSelectModule,
  MatTabsModule,
  MatTooltipModule,
  MatTableModule,
  MatPaginatorModule,
  MatSortModule,
  MatSlideToggleModule,
  MatStepperModule,
  MatExpansionModule,
  MatDialogModule,
  MatIconModule,
  MatCheckboxModule,
  MatMenuModule,
  MatButtonModule,
  MatListModule,
  MatChipsModule,
  MatBadgeModule,
  MatSidenavModule,
  MatDatepickerModule,
  MatNativeDateModule,
  MatProgressBarModule,
  MatRadioModule,
  CdkTableModule,
];

export const externals: any[] = [NgPipesModule, QuillModule.forRoot(), ColorPickerModule, QRCodeComponent, MaskitoModule, NgxChartsModule];
