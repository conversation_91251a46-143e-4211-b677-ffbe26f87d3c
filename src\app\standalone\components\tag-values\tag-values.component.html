<div class="parent-container">
  <ion-grid>
    <ion-row class="inner-container">
      @if (textOnly === false) {
        <ion-col class="icon-col" size="*">
          <ion-icon name="pricetag"></ion-icon>
        </ion-col>
      }
      <ion-col class="chiplist-content-col">
        <mat-chip-listbox>
          @for (tag of tags$ | async; track tag) {
            <mat-chip-option>
              <ion-grid>
                <ion-row>
                  @if (tag?.asset?.id) {
                    <ion-col class="image-column">
                      <mat-chip-avatar>
                        <img src="{{ contentUrl }}asset/{{ tag?.asset?.id }}/content" alt="{{ 'Photo' | translate | async }}" />
                      </mat-chip-avatar>
                    </ion-col>
                  }
                  <ion-col>
                    @if (!tag?.tagRiasecDetails) {
                      <ion-row>
                        <div class="parent-tag">
                          {{ tag?.parent?.name | translate | async }}
                        </div>
                      </ion-row>
                    }
                    <ion-row>
                      <div class="child-tag">{{ tag?.name | translate | async }}</div>
                    </ion-row>
                    @if (tag?.tagRiasecDetails) {
                      <ion-row>
                        <div class="parent-tag">{{ tag?.tagRiasecDetails?.onetSocCode }} - {{ tag.tagRiasecDetails?.riasecCode }}</div>
                      </ion-row>
                    }
                  </ion-col>
                </ion-row>
              </ion-grid>
            </mat-chip-option>
          }
        </mat-chip-listbox>
      </ion-col>
    </ion-row>
  </ion-grid>
</div>
