import { AsyncPipe } from '@angular/common';
import { Component, Input } from '@angular/core';
import { IInstanceSectionComponent } from '@app/core/contracts/contract';
import { IonicModule } from '@ionic/angular';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { TextValueComponent } from '../text-value/text-value.component';

@Component({
    selector: 'app-password-value',
    templateUrl: './password-value.component.html',
    styleUrls: ['./password-value.component.scss'],
    imports: [IonicModule, TextValueComponent, AsyncPipe, TranslatePipe]
})
export class PasswordValueComponent {
  @Input() instanceSectionComponent: IInstanceSectionComponent | undefined;
  constructor() {}
}
