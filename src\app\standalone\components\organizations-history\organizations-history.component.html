<ion-grid>
  <ion-row class="view-options-row">
    <ion-col>
      <!-- <ion-button class="btn-container"> <mat-icon svgIcon="filter"></mat-icon>Filters</ion-button>
      <ion-button class="btn-container" style="margin-left: 15px;"> <mat-icon svgIcon="sort"></mat-icon> Sort</ion-button> -->
    </ion-col>
    @if (isNetworkProduct !== true) {
      <ion-button (click)="openModal()" style="justify-content: flex-end">{{ 'Renew' | translate | async }}</ion-button>
    }
  </ion-row>
</ion-grid>
<mat-accordion>
  <ng-container>
    @for (productOrg of productOrganizationsHistory; track productOrg) {
      <mat-expansion-panel hideToggle [disabled]="true">
        <mat-expansion-panel-header class="expansion-panel-header">
          <div class="inner-panel">
            <div class="sub-heading">
              @if (productOrg.period) {
                <span style="font-weight: bold">{{ productOrg.period | uppercase }}, </span>
              }
              {{ subscriptionStatus(productOrg.expiryDate) | translate | async }}
              {{ 'on' | translate | async }}
              <span class="expiry-date">{{ productOrg.expiryDate | date: 'MMM d, y' }}</span>

              @if (productOrg.isNetworkProduct) {
                <span class="tag">{{ 'Network Product' | translate | async }}</span>
              } @else {
                <span class="tag">{{ 'Organization Product' | translate | async }}</span>
              }

              @if (productOrg.expiredByNetwork) {
                <span class="tag">{{ 'Expired by Network' | translate | async }}</span>
              }
            </div>
          </div>
        </mat-expansion-panel-header>
      </mat-expansion-panel>
    }
    @if (moreResults) {
      <div (click)="getProductOrganizationHistory(orgId, productId, true)" class="load-more">
        <ion-row>
          <ion-col size="12">
            <div>{{ 'Load More' | translate | async }}</div>
            <ion-icon name="chevron-down-outline"></ion-icon>
          </ion-col>
        </ion-row>
      </div>
    }
  </ng-container>
</mat-accordion>
