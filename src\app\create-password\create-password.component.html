@if (!buttonDisabled) {
  <div class="container">
    <form [formGroup]="passwordForm" class="container" (ngSubmit)="onSubmit()">
      <div class="card">
        <img class="logo" src="/assets/images/EFLogo_White.png" />
        <span>{{ 'Create Password' | translate | async }}</span>
        <ion-item>
          <ion-input
            placeholder="{{ 'Password' | translate | async }}"
            type="password"
            pattern="^(?=.*?[0-9])(?=.*?[A-Z])(?=.*?[a-z])(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$"
            formControlName="password"></ion-input>
          @if (password.touched && password.invalid) {
            <ion-note slot="error">{{ 'Password Requirements' | translate | async }}</ion-note>
          }
        </ion-item>
        <ion-item>
          <ion-input
            placeholder="{{ 'Confirm Password' | translate | async }}"
            type="password"
            pattern="^(?=.*?[0-9])(?=.*?[A-Z])(?=.*?[a-z])(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$"
            formControlName="confirmPassword"></ion-input>
          @if (confirmPassword.touched && confirmPassword.invalid) {
            <ion-note slot="error">{{ 'Passwords Do Not Match' | translate | async }}</ion-note>
          }
        </ion-item>
        <ion-button [disabled]="buttonDisabled" type="submit" fill="solid" color="primary">{{ 'Submit' | translate | async }}</ion-button>
      </div>
    </form>
  </div>
}
@if (buttonDisabled) {
  <ion-grid class="scormloader">
    <ion-row class="ion-align-items-center">
      <ion-col size="12">
        <img class="img" src="assets/images/EdgeFactor-EF_rings-2018-white_small_png.png" />
        <br />
        <ion-spinner color="primary" name="dots"></ion-spinner>
      </ion-col>
    </ion-row>
  </ion-grid>
}
