<div class="org-search-container">
  <form [formGroup]="searchForm">
    <ion-grid>
      <ion-row>
        <ion-col class="header-col">
          @if (showAddOrgs) {
            <div class="top-heading">{{ 'Who do you want to add?' | translate | async }}</div>
            <div class="sub-heading">
              <span>{{ 'Select the organization(s) you want to add' | translate | async }}</span>
            </div>
          } @else {
            <div class="top-heading">{{ 'Selected organization(s)' | translate | async }}</div>
            <div class="sub-heading">
              <span>{{ 'These are your selected organization(s)' | translate | async }}</span>
            </div>
          }
        </ion-col>
      </ion-row>
      <ion-row class="search-bar-row">
        <ion-col size="7">
          @if (showAddOrgs) {
            <ion-searchbar color="dark" formControlName="searchValue" (ionChange)="searchRepoValue()" type="search" placeholder="{{ 'Search Organization' | translate | async }}" showCancelButton="focus" debounce="600">
            </ion-searchbar>
          }
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
  <ion-content [style]="!showAddOrgs ? 'height: 77%' : null">
    <ion-grid>
      @if (orgs && orgs.length > 0) {
        <ion-list>
          @for (org of orgs; track org) {
            @if (showAddOrgs || (!showAddOrgs && org.selected)) {
              <ion-item (ionChange)="multiSelect ? addOrgToList($event, org) : addOrg($event, org)">
                <ion-checkbox [checked]="org.selected" slot="start"> </ion-checkbox>
                <ion-label>
                  <span class="heading">{{ org.name | translate | async }}</span>
                </ion-label>
              </ion-item>
            }
          }
          @if (moreResults) {
            <div (click)="setOrgs(true)" class="load-more">
              <ion-row>
                <ion-col size="12">
                  <div>{{ 'Load More' | translate | async }}</div>
                  <div><ion-icon name="chevron-down-outline"></ion-icon></div>
                </ion-col>
              </ion-row>
            </div>
          }
        </ion-list>
      }
      @if (noOrganizationsInTable) {
        <div class="no-orgs-container">
          <span class="no-orgs-text">{{ 'No Organizations' | translate | async }}</span>
        </div>
      }
    </ion-grid>
  </ion-content>
  <ion-footer>
    <div class="footer-content">
      <ion-row>
        <ion-col class="cancel-col">
          <ion-button fill="clear" (click)="showAddOrgs ? toggleAddOrgs() : close()">{{ 'Cancel' | translate | async }}</ion-button>
        </ion-col>
        <ion-col class="add-col">
          <div class="inner-container">
            @if (showAddOrgs) {
              <ion-badge>{{ selectedOrgIds.length }}</ion-badge>
              <ion-button (click)="add()">{{ 'Add' | translate | async }}</ion-button>
            } @else {
              @if (selectionChanged) {
                <ion-button (click)="add()">{{ 'Update' | translate | async }}</ion-button>
              }
              <ion-button (click)="toggleAddOrgs()">{{ 'Add More' | translate | async }}</ion-button>
            }
          </div>
        </ion-col>
      </ion-row>
    </div>
  </ion-footer>
</div>
