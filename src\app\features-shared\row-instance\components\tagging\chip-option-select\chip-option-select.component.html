<div class="parent-chip-list-container">
  <ion-row>
    <ion-col size="11">
      @if (filterChipList.length > 0) {
        <app-chip-list [type]="'ChipOptionSelect'" (selectedToRemoveOut)="removeChipById($event)" [listItems]="filterChipList"></app-chip-list>
      }
    </ion-col>
    <ion-col class="pop-over-trigger-col" size="1">
      <div class="trigger">
        <ion-icon id="option-select-popover" name="chevron-down-outline"></ion-icon>
      </div>
    </ion-col>
  </ion-row>
  <ion-popover class="chip-option-select-popover" #selectionPopover trigger="option-select-popover" side="bottom" alignment="end">
    <ng-template>
      <ion-row>
        <ion-col class="cancel-col">
          <ion-button fill="clear" color="primary" (click)="closePopover()">{{ 'Cancel' | translate | async }}</ion-button>
        </ion-col>
        @if (hasSelectionChanges) {
          <ion-col class="save-col">
            <ion-button fill="clear" color="primary" (click)="saveSelectionList()">{{ 'Save' | translate | async }}</ion-button>
          </ion-col>
        }
      </ion-row>
      <ion-list>
        @for (keyValue of listItems; track keyValue) {
          <ion-item (click)="selectionChanged($event, keyValue)" lines="none">
            <ion-checkbox [checked]="checkIsCheckboxSelected(keyValue)" slot="start"></ion-checkbox>
            <ion-label>
              <span>{{ keyValue.value | translate | async }}</span>
            </ion-label>
          </ion-item>
        }
      </ion-list>
    </ng-template>
  </ion-popover>
</div>
