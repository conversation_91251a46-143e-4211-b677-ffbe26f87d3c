@if (networkAnalytics && networkAnalytics.length > 0) {
  <div class="parent-container">
    <ion-row>
      <ion-col style="display: flex">
        <div class="header-container">
          <div class="heading">
            {{ (instanceComponent?.component?.templateField?.label ?? '') | translate | async }}
          </div>
        </div>
      </ion-col>
    </ion-row>
    <div class="card">
      <div class="card-content">
        <div class="inner-panel">
          <div class="description">
            {{ (instanceComponent?.component?.templateField?.helpDescription ?? '') | translate | async }}
          </div>
        </div>
      </div>
      <div class="black-underline"></div>
      @for (item of networkAnalytics; track item) {
        <app-network-item [network]="item"></app-network-item>
      }
    </div>
  </div>
}
