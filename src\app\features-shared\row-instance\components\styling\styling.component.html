@if (stylingForm) {
  <form [formGroup]="stylingForm">
    @if (row) {
      <ion-grid>
        <ion-row>
          <ion-col>
            <ion-label>
              {{ 'Thumbnail Style' | translate | async }}
              <span title="Changes the thumbnail style for this row content" class="reqAsterisk">
                <span> * </span>
                <ion-icon name="information-circle-outline"></ion-icon>
              </span>
            </ion-label>
            @if (thumbnailTypes.length > 0) {
              <ion-select interface="popover" formControlName="thumbnailTypeId">
                @for (type of thumbnailTypes; track type) {
                  <ion-select-option [value]="type.id">
                    {{ type.name | translate | async }}
                  </ion-select-option>
                }
              </ion-select>
            }
          </ion-col>
          <ion-col>
            <ion-label>
              {{ 'Display Type' | translate | async }}
              <span title="Changes the Display type for this row content" class="reqAsterisk">
                <span> * </span>
                <ion-icon name="information-circle-outline"></ion-icon>
              </span>
            </ion-label>
            <ion-select interface="popover" formControlName="displayType">
              @for (item of displayTypeKeyValue; track item) {
                <ion-select-option [value]="item.id">
                  {{ item.value | translate | async }}
                </ion-select-option>
              }
            </ion-select>
          </ion-col>
          <ion-col>
            <ion-label>
              {{ 'Row Title Style' | translate | async }}
            </ion-label>
            <ion-select interface="popover" formControlName="titleStyle">
              <ion-select-option value="none">{{ 'No styling' | translate | async }}</ion-select-option>
              <ion-select-option value="h1">{{ 'h1' | translate | async }}</ion-select-option>
              <ion-select-option value="h2">{{ 'h2' | translate | async }}</ion-select-option>
              <ion-select-option value="h3">{{ 'h3' | translate | async }}</ion-select-option>
              <ion-select-option value="h4">{{ 'h4' | translate | async }}</ion-select-option>
            </ion-select>
          </ion-col>
          @if (showAlignmentDropDown) {
            <ion-col>
              <ion-label>
                {{ 'Thumbnail Text Allignment' | translate | async }}
                <span title="{{ 'Changes the text alignment for this row content' | translate | async }}" class="reqAsterisk">
                  <span> * </span>
                  <ion-icon name="information-circle-outline"></ion-icon>
                </span>
              </ion-label>
              <ion-select interface="popover" formControlName="alignment">
                @for (item of alignmentKeyValue; track item) {
                  <ion-select-option [value]="item.id">
                    {{ item.value | translate | async }}
                  </ion-select-option>
                }
              </ion-select>
            </ion-col>
          }
        </ion-row>
      </ion-grid>
    }
  </form>
}
