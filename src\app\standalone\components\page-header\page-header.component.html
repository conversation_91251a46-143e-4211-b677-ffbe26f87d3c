@if (instance.feature.featureType.name !== 'V7 Landing Page' && onlyContent !== true) {
  <div
    [@fade]="minimized"
    class="image-container"
    [ngClass]="{
      sticky: minimized === true && isCoverVideo !== true && isPlayerView !== true,
      minimized: minimized === true,
      'video-header': isCoverVideo === true && isPlayerView !== true,
      'video-header-min': minimized === true && isCoverVideo === true && isPlayerView !== true,
    }"
    [ngStyle]="{ '--background-image': (isPlayerView || !coverPhotoUrl) && !isCoverVideo ? 'none' : 'url(' + coverPhotoUrl + ')' }">
    <div [class.page-margins-player]="viewType === viewTypes.Player" [class.page-margins]="viewType !== viewTypes.Player">
      @if (isCoverVideo && isPlayerView !== true) {
        <video id="autoplay-vid-cover" class="bg-video" autoplay loop muted preload="metadata" playsinline>
          <source [src]="coverPhotoUrl" type="video/mp4" />
        </video>
      }
      <div class="container">
        <app-breadcrumbs
          [breadcrumbs]="breadcrumbService.getBreadcrumbs(viewType)"
          [hasPageTitle]="viewType !== viewTypes.Player"
          [isScorm]="isScorm"
          [routeParams]="routeParams"
          [isFirstLevelView]="isCurrentFirstLevelView"
          [onlyContent]="onlyContent"></app-breadcrumbs>
        @if (viewType !== viewTypes.Player) {
          <ion-row class="header-container-parent">
            @if (isOrgManager && iconUrl) {
              <ion-col [ngClass]="minimized ? 'icon-col-min-org' : 'icon-col-org'" class="icon-col-main-org">
                <img class="icon-org" [src]="iconUrl" alt="Logo Icon" onerror="this.src=''; this.style.display='none'; this.style.backgroundColor='white';" />
              </ion-col>
            }
            <ion-col>
              @if (instance.isJoinCodeInstance === true && instance.isDefault !== true && instance?.feature?.featureType?.name !== 'Landing Pages') {
                <app-join-code [joinCode]="instance.joinCode" [featureTypeName]="instance?.feature?.featureType?.name"></app-join-code>
              }
              <div [ngClass]="isOrgManager ? 'header-container org' : 'header-container'">
                <div>
                  <h1>
                    @if (instance.feature.featureType.name === 'Favorites' || instance.feature.featureType.name === 'Portfolio') {
                      <span> {{ authService?.guestUserContext?.browsingAs ?? authService.userContext?.fullName }}'s </span>
                    }
                    {{ instance?.title ?? '' | parsePipe: instance?.id | async | translate | async }}
                  </h1>
                </div>
                @if (minimized !== true && isOrgManager === true) {
                  <span class="sub-heading" [innerHTML]="instance?.feature?.descriptors ?? '' | parsePipe: instance.id : null : true | async | translate | async"></span>
                }
                @if (minimized !== true && isOrgManager !== true) {
                  <h6>{{ instance?.description ?? '' | parsePipe: instance?.id | async | translate | async }}</h6>
                }
              </div>
            </ion-col>
          </ion-row>
        }
        <div class="edit-button">
          @if (showEditBtn() === true) {
            <ion-button fill="clear" class="inner-container" (click)="openBuilder(instance.id)">
              <ion-icon name="pencil"></ion-icon>
              {{ 'Edit' | translate | async }}
            </ion-button>
          }
          @if (showShareBtn()) {
            <ion-button fill="clear" class="inner-container share-button" (click)="onShare()">
              <mat-icon svgIcon="share"></mat-icon>
              {{ 'Share' | translate | async }}
            </ion-button>
          }
          @if (showLikeBtn()) {
            @if (liked) {
              <ion-button fill="clear" class="inner-container like-button" (click)="onLike()">
                <mat-icon svgIcon="favourite-icon"></mat-icon>
                {{ (liked ? 'Liked' : 'Like') | translate | async }}
              </ion-button>
            } @else {
              <ion-button fill="clear" class="inner-container like-button" (click)="onLike()">
                <mat-icon svgIcon="heart-icon"></mat-icon>
                {{ (liked ? 'Liked' : 'Like') | translate | async }}
              </ion-button>
            }
          }
        </div>
      </div>
    </div>
  </div>
  <ion-content id="ionContent" [ngClass]="{ 'grid-view-color': isGridView }" [scrollEvents]="true" (ionScroll)="onWindowScroll($event)">
    <ng-container *ngTemplateOutlet="content"></ng-container>
  </ion-content>
}
@if (instance.feature.featureType.name === 'V7 Landing Page' || onlyContent) {
  <div class="container-absolute">
    @if (viewType !== viewTypes.Builder) {
      <app-breadcrumbs
        [hasPageTitle]="viewType !== viewTypes.Player"
        [onlyContent]="onlyContent"
        [breadcrumbs]="breadcrumbService.getBreadcrumbs(viewType)"
        [isScorm]="isScorm"
        [isFirstLevelView]="isCurrentFirstLevelView"
        [routeParams]="routeParams"></app-breadcrumbs>
    }
    @if (featureTabs.length > 1) {
      <div class="center-row" [ngClass]="viewType !== viewTypes.Builder ? 'center-row-fab' : ''" [class.center-row-padding]="isCurrentFirstLevelView">
        <ion-segment mode="ios" color="dark" [value]="selectedIndex()">
          @for (ftTab of featureTabs; track $index; let i = $index) {
            <ion-segment-button [value]="i" (click)="tabChanged(i)">
              <ion-label>{{ ftTab.tab.name | translate | async }}</ion-label>
            </ion-segment-button>
          }
        </ion-segment>
      </div>
    }

    <div class="edit-button">
      @if (
        isFirstLevelPage === false &&
        (hasEditAccess() || hasAdminAccess()) &&
        ((featureTab?.type?.name === 'Instance Builder' && viewType !== viewTypes.Builder) || (featureTab?.tab?.name === 'ASSIGNMENTS' && viewType !== viewTypes.Builder))
      ) {
        <ion-button fill="clear" class="inner-container" (click)="openBuilder(instance.id)">
          <ion-icon name="pencil"></ion-icon>
          Edit
        </ion-button>
      }
      @if (showShareBtn()) {
        <ion-button fill="clear" class="inner-container share-button" (click)="onShare()">
          <mat-icon svgIcon="share"></mat-icon>
          {{ 'Share' | translate | async }}
        </ion-button>
      }
      @if (showLikeBtn()) {
        @if (liked) {
          <ion-button fill="clear" class="inner-container like-button" (click)="onLike()">
            <mat-icon svgIcon="favourite-icon"></mat-icon>
            {{ liked ? ('Liked' | translate | async) : ('Like' | translate | async) }}
          </ion-button>
        } @else {
          <ion-button fill="clear" class="inner-container like-button" (click)="onLike()">
            <mat-icon svgIcon="heart-icon"></mat-icon>
            {{ liked ? ('Liked' | translate | async) : ('Like' | translate | async) }}
          </ion-button>
        }
      }
    </div>
  </div>
  <ng-container id="ionContent" class="content-no-header">
    <ng-container *ngTemplateOutlet="content"></ng-container>
  </ng-container>
}
