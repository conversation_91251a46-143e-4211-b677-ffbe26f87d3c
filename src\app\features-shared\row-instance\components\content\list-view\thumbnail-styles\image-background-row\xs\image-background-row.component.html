<div [ngClass]="!readingMode ? 'builder-row' : 'preview-row'">
  @if (!readingMode) {
    <div class="icon-container">
      <div class="cdk-drag-handle" cdkDragHandle>
        <mat-icon svgIcon="drag"></mat-icon>
      </div>
      @if (row.rowType.name === 'Deliverable') {
        <div class="icon-container">
          <div class="delete-icon" (click)="deleteInstanceFromRow($event)">
            <mat-icon svgIcon="delete"></mat-icon>
          </div>
        </div>
      }
    </div>
  }
  <div class="container-height" [ngClass]="content.status && (content.status === 'InProgress' ? 'status-inprogress' : content.status === 'Completed' ? 'status-complete' : null)">
    <mat-expansion-panel (opened)="openGroup(true, $event)" (closed)="openGroup(false, $event)" #panel hideToggle class="hover-item">
      <mat-expansion-panel-header [ngClass]="{ expanded: panel.expanded }">
        <ion-grid>
          @if (panel.expanded) {
            <ion-row class="controls">
              @if (rowType === 'Modifiable Package') {
                <div class="items">
                  <span class="number-items">{{ itemCount }}</span> &#160;
                  @if (itemCount > 1 || itemCount === 0) {
                    <span class="items-description">{{ 'items' | translate | async }}</span>
                  }
                  @if (itemCount === 1) {
                    <span class="items-description">{{ 'item' | translate | async }}</span>
                  }
                </div>
              }
              @if (rowType !== 'Modifiable Package') {
                <!-- <div class="share-icon">
                <mat-icon svgIcon="share"></mat-icon>
              </div> -->
                @if (row.rowType.name !== 'Deliverable') {
                  <div class="add-button-container">
                    <ion-button size="small"
                      ><mat-icon>playlist_add</mat-icon><span class="button-text">{{ 'Add' | translate | async }}</span></ion-button
                    >
                  </div>
                }
              }
            </ion-row>
          }
          <ion-row>
            <ion-col size="auto">
              @if (content?.actionBW === actionTypes.View) {
                <div class="lock-container">
                  <div>
                    <mat-icon matTooltip="You don't have access to this." class="lock-icon" style="color: white">lock</mat-icon>
                  </div>
                </div>
              }
            </ion-col>
            <ion-col (click)="goToInstance($event)">
              <app-instance-header-control
                [status]="content?.status"
                [instanceId]="instance?.id"
                [instanceTitle]="instance?.title"
                [featureTitle]="instance?.feature?.title"
                [iconAssetId]="content?.iconAssetId">
              </app-instance-header-control>
            </ion-col>
          </ion-row>
        </ion-grid>
        @if (panel.expanded) {
          <div class="row"></div>
        }
        @if (content.status && !rowContentHoverValues?.showHover) {
          <div class="status-foot-container" loading="lazy">
            @if (content.status === 'InProgress') {
              <span class="inner" style="background-color: orange">
                <span>{{ 'IN PROGRESS' | translate | async }}</span>
              </span>
            }
            @if (content.status === 'Completed') {
              <span class="inner" style="background-color: green">
                <span>{{ 'COMPLETED' | translate | async }}</span>
              </span>
            }
          </div>
        }
      </mat-expansion-panel-header>
      @if (currentPanelState && (instanceSections?.length ?? 0)) {
        <ion-grid>
          <ion-row class="middle-row margin-removal">
            <div class="list-item-container scroll-left-side">
              @if (content?.entityType === entityTypes.Instances) {
                <app-instance-section-hover-components
                  [routeParams]="contentParams"
                  [onlyHover]="true"
                  [displayShowMore]="true"
                  [mediaBlocks]="false"
                  [iconAssetId]="content?.iconAssetId"
                  [showInstanceHeader]="false"></app-instance-section-hover-components>
              }
            </div>
          </ion-row>
          <ion-row class="bottom-row">
            <h5 class="top-header">Included Items:</h5>
            <div [class.scroll-right-side]="instanceSections?.length ?? 0 > 5">
              @for (instanceSection of instanceSections; track instanceSection; let i = $index) {
                <div class="include-item" [ngStyle]="checkSectionCompleted(instanceSection) ? { border: '2px solid green', 'border-left-width': '10px' } : {}" (click)="route()">
                  <ion-row>
                    @if (checkSectionCompleted(instanceSection)) {
                      <ion-col size="1" class="completed-check-col">
                        <ion-icon name="checkmark-circle-outline"></ion-icon>
                      </ion-col>
                    }
                    <ion-col class="component-col" [size]="checkSectionCompleted(instanceSection) ? 10 : 11">
                      <ion-row style="line-height: 1.1em">
                        <span class="component-block-title">{{ instanceSection?.section?.title | translate | async }}</span>
                      </ion-row>
                      <ion-row class="sub-row-container">
                        <!-- <ion-col>
                      {{ component.componentType.name }}
                    </ion-col> -->
                        <ion-col class="completed-date-container">
                          @if (setLastModifiedDate && checkSectionCompleted(instanceSection)) {
                            {{ 'Completed' | translate | async }}<span class="heading">{{ setLastModifiedDate | date: 'MMMM d' }}</span>
                          }
                        </ion-col>
                      </ion-row>
                    </ion-col>
                    @if ((rowType === 'Modifiable Package' || rowType === 'Accredited Package') && hasAdminAccess()) {
                      <ion-col class="toggle-col" size="1">
                        @if (content?.property2) {
                          <div>
                            <span class="runtime" [class.modifiable-package-runtime]="rowType === 'Modifiable Package'">{{ content?.property2 + ' min' | translate | async }}</span>
                          </div>
                        }
                        <!-- <div class="slide-toggle-col">
                    <mat-slide-toggle color="primary" checked="{{ component?.templateField?.isVisible }}" (change)="saveVisibilityCheck(component?.templateField?.id, $event)"></mat-slide-toggle>
                  </div> -->
                      </ion-col>
                    }
                  </ion-row>
                </div>
              }
            </div>
          </ion-row>
        </ion-grid>
      }
    </mat-expansion-panel>
  </div>
</div>
