import { Component, Input, OnInit } from '@angular/core';
import { environment } from '@env/environment';
import html2canvas from 'html2canvas';
import { GlobalToastService } from '@app/core/services/global-toast.service';
import { QRCodeComponent } from 'angularx-qrcode';
import { IonicModule } from '@ionic/angular';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
  selector: 'app-qr-code',
  templateUrl: './qr-code.component.html',
  styleUrls: ['./qr-code.component.scss'],
  imports: [QRCodeComponent, IonicModule, AsyncPipe, TranslatePipe],
})
export class QrCodeComponent implements OnInit {
  @Input() qrCodeData: string | null;
  qrUrl: string;
  constructor(private toast: GlobalToastService) {}

  ngOnInit() {
    if (this.qrCodeData) {
      this.qrUrl = `${environment.appUrl}user/joincode/${this.qrCodeData}`;
    } else {
      this.qrUrl = `${environment.appUrl}my-journey`;
    }
  }

  downloadSnippet() {
    const element = document.getElementById('screen-canvas');
    if (element) {
      html2canvas(element, { allowTaint: true, useCORS: true, backgroundColor: null }).then(canvas => {
        const link = document.createElement('a');
        link.href = canvas.toDataURL('image/png');
        link.download = this.qrCodeData + '.png';
        link.click();
      });
    }
  }

  copy() {
    if (!navigator.clipboard) {
      // use old commandExec() way
      const selBox = document.createElement('textarea');
      selBox.style.position = 'fixed';
      selBox.style.left = '0';
      selBox.style.top = '0';
      selBox.style.opacity = '0';
      selBox.value = this.qrUrl ?? '';
      document.body.appendChild(selBox);
      selBox.focus();
      selBox.select();
      document.execCommand('copy');
      document.body.removeChild(selBox);
      this.toast.presentToast('Copied successfully');
    } else {
      navigator.clipboard.writeText(this.qrUrl ?? '').then(() => {
        this.toast.presentToast('Copied successfully');
      });
    }
  }
}
