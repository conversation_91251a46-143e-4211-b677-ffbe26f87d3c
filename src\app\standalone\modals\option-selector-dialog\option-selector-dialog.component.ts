import { Component, Input } from '@angular/core';
import { IKeyValue } from '@app/core/contracts/contract';
import { HeadingValueComponent } from '@app/standalone/components/heading-value/heading-value.component';
import { IonicModule, PopoverController } from '@ionic/angular';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-option-selector-dialog-media',
    templateUrl: './option-selector-dialog.component.html',
    styleUrls: ['./option-selector-dialog.component.scss'],
    imports: [IonicModule, HeadingValueComponent, AsyncPipe, TranslatePipe]
})
export class OptionSelectorDialogComponent {
  @Input() header: string | undefined;
  @Input() options: IKeyValue<string, string>[];
  constructor(private popover: PopoverController) {}

  selectType(option: IKeyValue<string, string>) {
    //NoLogic Context For Options?
    this.popover.dismiss(option);
  }
}
