@if (bannerService.bannerState().visible) {
  <div [ngClass]="screenSize === 'xs' ? 'fixed-banner-mobile fixed-banner' : 'fixed-banner'">
    <div
      [style.margin-right.vw]="screenSize === 'lg' ? 13 : 0"
      [class]="bannerService.bannerState().type === BannerType.COMPLETED_ALL ? 'banner-content green-banner' : 'banner-content orange-banner'">
      @if (bannerService.bannerState().type === BannerType.COMPLETED_ALL) {
        <!-- Completed All Banner -->
        <div class="banner-message">
          <ion-img [style.height.px]="screenSize === 'xs' ? 20 : 25" src="/assets/images/Celebrate Icon.png" alt="Celebration icon" class="celebration-icon" />
          <span>{{ "You've completed all of the required items!" | translate | async }}</span>
          <ion-button (click)="onContinue()" class="continue-btn">{{ 'CONTINUE' | translate | async }}</ion-button>
        </div>
      } @else if (bannerService.bannerState().type === BannerType.COMPLETED_SOME) {
        <!-- Completed Some Banner -->
        <div class="banner-message">
          <span>
            {{ "You've completed" | translate | async }} <b>{{ bannerService.bannerState().completed }}</b> {{ 'of the' | translate | async }} <b>{{ bannerService.bannerState().totalCount }}</b>
            {{ 'required items' | translate | async }}.
          </span>
        </div>
      } @else if (bannerService.bannerState().type === BannerType.MINIMUM_REQUIRED) {
        <!-- Minimum Required Banner -->
        <div class="banner-message">
          <span>
            {{ 'Complete at least' | translate | async }} <b>{{ bannerService.bannerState().minimumItems }}</b> {{ 'of these items' | translate | async }}.
          </span>
        </div>
      }
    </div>
  </div>
}
