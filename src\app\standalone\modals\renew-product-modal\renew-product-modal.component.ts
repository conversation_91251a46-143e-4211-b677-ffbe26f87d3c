import { Component, Input } from '@angular/core';
import { IProductRenew } from '@app/core/contracts/contract';
import { ModalController, IonicModule } from '@ionic/angular';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { RenewProductComponent } from '../../components/renew-product/renew-product.component';

@Component({
    selector: 'app-renew-product-modal',
    templateUrl: './renew-product-modal.component.html',
    styleUrls: ['./renew-product-modal.component.scss'],
    imports: [IonicModule, RenewProductComponent, AsyncPipe, TranslatePipe]
})
export class RenewProductModalComponent {
  @Input() currentSubscriptionExpires: any;
  @Input() featureType: string;
  @Input() networkOrgCount?: number;
  productRenew: IProductRenew;

  constructor(private modalController: ModalController) {}

  updateProduct(productRenew: IProductRenew) {
    this.productRenew = productRenew;
  }

  public renewProduct() {
    this.modalController.dismiss(this.productRenew);
  }

  public close() {
    this.modalController.dismiss();
  }
}
