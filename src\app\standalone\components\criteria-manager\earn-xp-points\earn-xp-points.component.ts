import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IEarningCriteriaContentIn, IEarningCriteriaIn } from '@app/core/contracts/contract';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { AsyncPipe } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-earn-xp-points',
    templateUrl: './earn-xp-points.component.html',
    styleUrls: ['./earn-xp-points.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, AsyncPipe, TranslatePipe]
})
export class EarnXpPointsComponent implements OnInit, OnDestroy {
  @Input() type: string;
  @Input() earningCriteria: IEarningCriteriaIn;
  @Output() criteriaUpdated: EventEmitter<IEarningCriteriaContentIn[]> = new EventEmitter();

  formValueChanges$: Subscription;
  xpForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private formBuilder: FormBuilder) {}

  ngOnInit() {
    this.createForm();
  }

  createForm() {
    this.xpForm = this.formBuilder.group({
      minValue: [this.earningCriteria?.minValue],
    });

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.xpForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.setObjectValues();
    });
  }

  setObjectValues() {
    if (this.xpForm.valid) {
      if (this.xpForm.controls['minValue'].value) {
        this.earningCriteria.minValue = this.xpForm.controls.minValue.value;
        this.criteriaUpdated.emit(this.earningCriteria.earningCriteriaContent);
      }
    }
  }

  checkInputLength(event: any) {
    const numberVal = Number(event.detail.value);
    if (numberVal < 0) {
      this.xpForm.controls.minValue.setValue(0);
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
