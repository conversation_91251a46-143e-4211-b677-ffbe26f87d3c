<div class="user-search-container">
  <form [formGroup]="searchForm">
    <ion-grid>
      <ion-row>
        <ion-col class="header-col">
          <div class="step-heading">{{ 'STEP 1/2' | translate | async }}</div>
          <div class="top-heading">{{ 'Who do you want to add?' | translate | async }}</div>
          <div class="sub-heading">
            <span>{{ 'Select the person/s you want to add' | translate | async }}</span>
          </div>
        </ion-col>
      </ion-row>
      <ion-row class="search-bar-row">
        <!--<ion-col size="3" style="justify-content: flex-start">
        <ion-button> <ion-icon name="options-outline"></ion-icon>Filters</ion-button>
      </ion-col>-->
        <!--<ion-col size="3" style="justify-content: flex-end">
      <ion-button> <ion-icon name="swap-vertical-outline"></ion-icon> Sort</ion-button>
    </ion-col>-->
        <ion-col size="6">
          <ion-searchbar
            color="dark"
            formControlName="userSearch"
            (ionChange)="searchUsers()"
            type="search"
            placeholder="{{ 'Search Users' | translate | async }}"
            showCancelButton="focus"
            debounce="100">
          </ion-searchbar>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
  <ion-content>
    <ion-grid>
      @if (users !== null && users.length > 0) {
        <ion-list>
          <ion-item class="select-container" (ionChange)="addAllUsersToList($event, users)">
            <div class="select-all">
              <ion-checkbox slot="start"></ion-checkbox>
              <ion-label>
                <span class="heading">{{ 'Select All' | translate | async }}</span>
              </ion-label>
            </div>
          </ion-item>
          @for (user of users; track user) {
            <ion-item (ionChange)="addUserToList($event, user)" [ngClass]="[selectedStyle]">
              <ion-checkbox [checked]="user.selected" slot="start"></ion-checkbox>
              <ion-label>
                <span class="heading">{{ user.name }}</span>
                <div class="sub-heading">
                  <span>{{ user.roleName | translate | async }}</span>
                </div>
              </ion-label>
            </ion-item>
          }
        </ion-list>
      } @else {
        <ion-item>
          <ion-label>
            <div class="sub-heading">
              <span>{{ 'No users found that match your search.' | translate | async }}</span>
            </div>
          </ion-label>
        </ion-item>
      }
    </ion-grid>
  </ion-content>

  <ion-footer>
    <div class="footer-content">
      <ion-row>
        <ion-col class="cancel-col">
          <ion-button fill="clear" (click)="close()">{{ 'Cancel' | translate | async }}</ion-button>
        </ion-col>
        <ion-col class="add-col">
          <div class="inner-container">
            <ion-badge>{{ selectedUsers.length }}</ion-badge>
            <ion-button (click)="add()"> {{ 'Add' | translate | async }} </ion-button>
          </div>
        </ion-col>
      </ion-row>
    </div>
  </ion-footer>
</div>
