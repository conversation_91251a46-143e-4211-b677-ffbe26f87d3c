import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { AsyncPipe } from '@angular/common';
import { UntypedFormControl, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IEarningCriteriaContentSearch, IFeatureSearchLite, IInstance, IOrganizationSearch, IProductSearch } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { PopoverController, IonicModule } from '@ionic/angular';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { debounceTime, Subject, takeUntil } from 'rxjs';
import { InstanceCardComponent } from '../add-to-dialog/components/instance-card/instance-card.component';

@Component({
    selector: 'app-search-modal',
    templateUrl: './add-search-modal.component.html',
    styleUrls: ['./add-search-modal.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, InstanceCardComponent, AsyncPipe, TranslatePipe]
})
export class AddSearchModalComponent implements OnInit, OnDestroy {
  @Input() linkTypeName?: string;
  @Input() heading?: string;
  @Input() subHeading?: string;
  @Input() contentHeading?: string;
  @Input() criteriaType: string;
  @Input() options!: KeyValue[];
  @Input() height = 60;
  results: KeyValue[] = [];
  currentAmount = 0;
  getAmount = 50;
  query = '';

  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(
    private popoverController: PopoverController,
    private dataService: DataService
  ) {}

  ngOnInit() {
    if (this.linkTypeName === 'Organizations') {
      this.heading = 'Which organization?';
      this.subHeading = `You're connected to multiple organizations.  Select the organization that this is associated with.`;
      this.contentHeading = 'Your organizations';
    }

    this.searchResults();
  }

  searchResults() {
    const searchValue = this.query;
    if (this.criteriaType) {
      if (this.query !== '') {
        this.dataService
          .searchEarningCriteriaContent(this.criteriaType, searchValue, this.currentAmount, this.getAmount)
          .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
          .subscribe((features: IEarningCriteriaContentSearch[]) => {
            this.results = features.map(t => {
              return { id: t.id, value: t.name } as KeyValue;
            });
          });
      } else {
        this.results = [];
      }
    } else {
      switch (this.linkTypeName) {
        case 'Organizations':
          this.dataService
            .searchOrganizations(searchValue, this.currentAmount, this.getAmount)
            .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
            .subscribe((organizations: IOrganizationSearch[]) => {
              this.results = organizations.map(t => {
                return { id: t.id, value: t.name } as KeyValue;
              });
            });
          break;
        case 'Features':
          this.dataService
            .searchFeatures(searchValue, this.currentAmount, this.getAmount)
            .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
            .subscribe((features: IFeatureSearchLite[]) => {
              this.results = features.map(t => {
                return { id: t.id, value: t.name } as KeyValue;
              });
            });
          break;
        case 'Instances':
          this.dataService
            .searchInstances(null, searchValue)
            .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
            .subscribe((instances: IInstance[]) => {
              this.results = instances.map(t => {
                return { id: t.id, value: t.title } as KeyValue;
              });
            });
          break;
        case 'Products':
          this.dataService
            .searchProducts(searchValue, this.currentAmount, this.getAmount)
            .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
            .subscribe((products: IProductSearch[]) => {
              this.results = products.map(t => {
                return { id: t.id, value: t.name } as KeyValue;
              });
            });
          break;
        case 'Educators':
          //OptionsListSearch.
          if (this.options?.length > 0) {
            //SetNameValues - data.Name.
            this.results = this.options.map(t => {
              return { id: t.id, value: t.value } as KeyValue;
            });

            //Filter
            if (this.query?.length > 0) {
              this.results = this.results.filter(x => x.value?.includes(searchValue));
            }
          }
          break;
        default:
          break;
      }
    }
  }

  close() {
    this.popoverController.dismiss();
  }

  add(selectedData: KeyValue) {
    if (selectedData) {
      this.popoverController.dismiss({ ...selectedData, name: selectedData.value, type: this.criteriaType } as IEarningCriteriaContentSearch);
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
