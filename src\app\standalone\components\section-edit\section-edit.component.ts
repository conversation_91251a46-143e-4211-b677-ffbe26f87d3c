import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IComponent, ISection, ISectionActionIn, ISectionIn, ISidePanelBuilder } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { SidePanelBuilderType } from '@app/core/enums/builder-selection-types';
import { SectionTypes } from '@app/core/enums/section-types.enum';
import { DataService } from '@app/core/services/data-service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { Observable, Subject, Subscription, debounceTime, map, takeUntil } from 'rxjs';
import { NgClass, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { IonicModule } from '@ionic/angular';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { TextAreaInputControlComponent } from '../text-area-input-control/text-area-input-control.component';
import { SelectOptionControlComponent } from '../select-option-control/select-option-control.component';
import { ColorPickerModule } from 'ngx-color-picker';
import { MatSlideToggle } from '@angular/material/slide-toggle';
import { AlertService } from '@app/core/services/alert-service';

@Component({
    selector: 'app-section-edit',
    templateUrl: './section-edit.component.html',
    styleUrls: ['./section-edit.component.scss'],
    imports: [
        FormsModule,
        ReactiveFormsModule,
        NgClass,
        IonicModule,
        TextInputControlComponent,
        TextAreaInputControlComponent,
        SelectOptionControlComponent,
        ColorPickerModule,
        MatSlideToggle,
        AsyncPipe,
        TranslatePipe,
    ]
})
export class SectionEditComponent implements OnInit, OnChanges, OnDestroy {
  @Input() panelBuilder?: ISidePanelBuilder;
  @Input() section: ISection | null;
  @Input() templateId: string;
  @Input() templateForm: UntypedFormGroup;
  @Input() persistSortOrder = false;
  @Input() backGroundColor: string | null = null;
  @Output() sectionColorChanged = new EventEmitter<string | null>();

  color = '#1e1e1e';
  sectionForm: UntypedFormGroup;
  sectionTypes: KeyValue[];
  builderType = SidePanelBuilderType;
  formValueChanges$: Subscription;
  componentDestroyed$: Subject<boolean> = new Subject();
  sectionTypesEnum = SectionTypes;
  dynamicComponentList: IComponent[] = [];
  selectedComponentIds: string[] = [];
  actions$: Observable<KeyValue[]>;
  customBackgroundColour = false;

  constructor(
    private dataService: DataService,
    private builderService: BuilderService,
    private alertService: AlertService
  ) {}

  ngOnInit() {
    this.createSectionForm();

    this.actions$ = this.dataService.getActions().pipe(
      map(action => {
        return action.map(t => {
          return { id: t.id, value: t.name } as KeyValue;
        });
      })
    );

    this.color = this.backGroundColor ?? this.section?.backgroundColor ?? '#1e1e1e';
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['section']) {
      if (this.formValueChanges$) {
        this.formValueChanges$.unsubscribe();
      }
      this.setFormValues();
    }
  }

  openCustomBackgroundColour() {
    this.customBackgroundColour = !this.customBackgroundColour;
  }

  createSectionForm() {
    if (this.section) {
      this.sectionForm = this.templateForm.get(this.section.id) as UntypedFormGroup;
    }

    this.sectionForm = new UntypedFormGroup({
      title: new UntypedFormControl(this.section?.title, [Validators.required]),
      description: new UntypedFormControl(this.section?.description),
      caption: new UntypedFormControl(this.section?.caption),
      hideBackground: new UntypedFormControl(this.section?.hideBackground),
      showOnInstanceViewer: new UntypedFormControl(this.section?.showOnInstanceViewer),
      showTitleOnPlayer: new UntypedFormControl(this.section?.showTitleOnPlayer),
      showOnPlayerSidepanel: new UntypedFormControl(this.section?.showOnPlayerSidepanel),
      showDescOnPlayer: new UntypedFormControl(this.section?.showDescOnPlayer),
      isEditable: new UntypedFormControl(this.section?.isEditable),
      sortOrder: new UntypedFormControl(this.section?.sortOrder),
      isCompletable: new UntypedFormControl(this.section?.isCompletable),
      actions: new UntypedFormControl(this.section?.actions ? this.section?.actions?.map(x => x.id) : null),
      backgroundColor: new UntypedFormControl(this.backGroundColor ?? this.section?.backgroundColor),
      isContinuousFeedback: new UntypedFormControl(this.section?.isContinuousFeedback != null ? this.section?.isContinuousFeedback : true),
      webMaxWidth: new UntypedFormControl(this.section?.webMaxWidth),
      mobileMaxWidth: new UntypedFormControl(this.section?.mobileMaxWidth),
    });

    this.subscribeToFormChanges();
  }

  setFormValues() {
    if (!this.sectionForm) {
      return;
    }

    this.sectionForm.controls.title.setValue(this.section?.title);
    this.sectionForm.controls.description.setValue(this.section?.description);
    this.sectionForm.controls.caption.setValue(this.section?.caption);
    this.sectionForm.controls.hideBackground.setValue(this.section?.hideBackground);
    this.sectionForm.controls.showOnInstanceViewer.setValue(this.section?.showOnInstanceViewer);
    this.sectionForm.controls.showTitleOnPlayer.setValue(this.section?.showTitleOnPlayer);
    this.sectionForm.controls.showOnPlayerSidepanel.setValue(this.section?.showOnPlayerSidepanel);
    this.sectionForm.controls.showDescOnPlayer.setValue(this.section?.showDescOnPlayer);
    this.sectionForm.controls.sortOrder.setValue(this.section?.sortOrder);
    this.sectionForm.controls.isEditable.setValue(this.section?.isEditable);
    this.sectionForm.controls.isCompletable.setValue(this.section?.isCompletable);
    this.sectionForm.controls.actions.setValue(this.section?.actions !== null ? this.section?.actions?.map(x => x.id) : null);
    this.sectionForm.controls.backgroundColor.setValue(this.backGroundColor ?? this.section?.backgroundColor);
    this.sectionForm.controls.isContinuousFeedback.setValue(this.section?.isContinuousFeedback != null ? this.section?.isContinuousFeedback : true);
    this.sectionForm.controls.webMaxWidth.setValue(this.section?.webMaxWidth);
    this.sectionForm.controls.mobileMaxWidth.setValue(this.section?.mobileMaxWidth);

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.sectionForm.valueChanges.pipe(debounceTime(700), takeUntil(this.componentDestroyed$)).subscribe(() => {
      const sectionIn = {
        id: this.section?.id,
        title: this.sectionForm.controls.title.value,
        description: this.sectionForm.controls.description.value,
        caption: this.sectionForm.controls.caption.value,
        hideBackground: this.sectionForm.controls.hideBackground.value,
        showOnInstanceViewer: this.sectionForm.controls.showOnInstanceViewer.value,
        showTitleOnPlayer: this.sectionForm.controls.showTitleOnPlayer.value,
        showOnPlayerSidepanel: this.sectionForm.controls.showOnPlayerSidepanel.value,
        showDescOnPlayer: this.sectionForm.controls.showDescOnPlayer.value,
        sortOrder: this.sectionForm.controls.sortOrder.value ?? 0,
        typeId: this.section?.typeId,
        isEditable: this.sectionForm.controls.isEditable.value,
        isCompletable: this.sectionForm.controls.isCompletable.value,
        sectionActions: this.sectionForm.controls.actions.value?.map((x: string) => ({ id: null, actionId: x, sectionId: this.section?.id }) as ISectionActionIn),
        backgroundColor: this.sectionForm.controls.backgroundColor.value,
        isContinuousFeedback: this.sectionForm.controls.isContinuousFeedback.value,
        webMaxWidth: this.sectionForm.controls.webMaxWidth.value,
        mobileMaxWidth: this.sectionForm.controls.mobileMaxWidth.value,
      } as ISectionIn;

      const background = this.sectionForm.controls.backgroundColor.value?.trim();
      if (this.section && background) {
        this.color = background;
        if (!this.backGroundColor) {
          this.section.backgroundColor = background;
        } else {
          this.sectionColorChanged.next(background);
        }
      }

      if (this.sectionForm.valid) {
        this.updateSection(sectionIn);
      }
    });
  }

  updateSection(section: ISectionIn) {
    this.dataService
      .updateSection(section, this.persistSortOrder)
      .pipe(debounceTime(700), takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.builderService.sectionUpdated$.next({ ...this.section, ...section } as ISection);
      });
  }

  back() {
    this.builderService.selectedSection$.next(null);
  }

  async toggleSectionVisibility() {
    if (this.sectionForm.controls.showOnInstanceViewer.value == false) {
      const result = await this.alertService.presentAlertOptions('Confirm Hide Section', 'Are you sure you want to hide this section? Doing so will set all related toggles to false.');
      if (result) {
        this.sectionHiddenState();
      } else {
        this.sectionForm.controls.showOnInstanceViewer.setValue(true);
      }
    }
  }

  sectionHiddenState() {
    this.sectionForm.controls.showTitleOnPlayer.setValue(false);
    this.sectionForm.controls.showOnPlayerSidepanel.setValue(false);
    this.sectionForm.controls.showDescOnPlayer.setValue(false);
    this.sectionForm.controls.isCompletable.setValue(false);
  }

  public captureColour(event: any): void {
    this.sectionForm.controls.backgroundColor.patchValue(event);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
