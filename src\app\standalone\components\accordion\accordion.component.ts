import { Component, forwardRef, Input, OnDestroy, OnInit } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR } from '@angular/forms';
import { IAccordionItem, IComponent } from '@app/core/contracts/contract';
import { ComponentUpdateSignalService } from '@app/core/services/component-update-signals.service';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { FileUploadControlComponent } from '@app/standalone/components/file-upload-control/file-upload-control.component';
import { IonicModule } from '@ionic/angular';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { Subject, takeUntil } from 'rxjs';

@Component({
    selector: 'app-accordion',
    templateUrl: './accordion.component.html',
    styleUrls: ['./accordion.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => AccordionComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => AccordionComponent),
        },
    ],
    imports: [IonicModule, FileUploadControlComponent, AsyncPipe, TranslatePipe]
})
export class AccordionComponent extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() component!: IComponent;
  @Input() type: string;
  @Input() description: string;
  @Input() override label: string;
  componentDestroyed$: Subject<boolean> = new Subject();
  accordionItemList: IAccordionItem[] = [];

  constructor(private signalService: ComponentUpdateSignalService) {
    super();
  }

  ngOnInit() {
    this.fieldValueChanged.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.setAccordion();
    });
  }

  setAccordion() {
    if (this.textValue) {
      this.accordionItemList = JSON.parse(this.textValue) as IAccordionItem[];
      this.sortOrder();
    }

    this.setDefaultFirstValue();
  }

  setDefaultFirstValue() {
    if (this.accordionItemList.length === 0) {
      this.addItem();
    }
  }

  setInstanceAccordionRowSortOrderDirect(event: any) {
    if (this.accordionItemList.length > 0) {
      //Find.
      const item = this.accordionItemList[event.detail.from];
      this.accordionItemList.splice(event.detail.from, 1);
      this.accordionItemList.splice(event.detail.to, 0, item);

      if (item) {
        event.detail.complete();
        this.setAccordionSortOrder();
      }
    }
  }

  setAccordionSortOrder() {
    //SortOrder = IndexPosition.
    if (this.accordionItemList.length > 0) {
      this.accordionItemList.forEach((item, index) => {
        let headingCount = index;
        headingCount += 1;

        //Card - Item Heading.
        const heading = this.type === 'Accordion' ? 'ITEM' : 'CARD';
        item.sortOrder = index;
        item.heading = heading + ' ' + headingCount;
      });
    }

    this.updateAccordion();
  }

  sortOrder() {
    this.accordionItemList = this.accordionItemList.sort((n1, n2) => n1.sortOrder - n2.sortOrder);
  }

  saveInputTextDirect(event: any, item: IAccordionItem, type: string) {
    const index = this.accordionItemList.findIndex(x => x.sortOrder === item.sortOrder);
    if (index !== -1) {
      if (type === 'title') {
        this.accordionItemList[index].title = event.detail.value;
      } else if (type === 'picture') {
        this.accordionItemList[index].title = event ?? '';
      } else if (type === 'description') {
        this.accordionItemList[index].description = event.detail.value;
      }
    }

    this.updateAccordion();
  }

  updateAccordion() {
    const valueAsString = JSON.stringify(this.accordionItemList);
    this.setValue(valueAsString);
    if(this.component.id)
      {
        this.signalService.triggerSignal({ componentId: this.component.id, updateValue: valueAsString });
      }
  }

  override setValue(value: string) {
    this.writeValue(value);
  }

  addItem() {
    const newItem: IAccordionItem = {
      heading: 'ITEM',
      title: '',
      description: '',
      sortOrder: 0,
    };

    this.accordionItemList.push(newItem);
    this.setAccordionSortOrder();
  }

  removeItem(item: IAccordionItem) {
    const index = this.accordionItemList.findIndex(x => x.sortOrder === item.sortOrder);
    this.accordionItemList.splice(index, 1);
    this.setAccordionSortOrder();
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
