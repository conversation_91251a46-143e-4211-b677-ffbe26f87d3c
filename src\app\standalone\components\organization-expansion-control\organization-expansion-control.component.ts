import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { IOrganization, IOrganizationUser, IRole } from '@app/core/contracts/contract';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { Subject } from 'rxjs';
import { MatAccordion, MatExpansionPanel, MatExpansionPanelHeader } from '@angular/material/expansion';
import { IonicModule } from '@ionic/angular';

@Component({
    selector: 'app-organization-expansion-control',
    templateUrl: './organization-expansion-control.component.html',
    styleUrls: ['./organization-expansion-control.component.scss'],
    imports: [MatAccordion, MatExpansionPanel, MatExpansionPanelHeader, IonicModule, AsyncPipe, TranslatePipe]
})
export class OrganizationExpansionControlComponent implements OnInit, OnDestroy {
  @Input() id: string;
  @Input() featureType: string;
  organizations: IOrganization[] = [];
  currentAmount = 0;
  getAmount = 25;
  moreResults = false;
  actionTypes = ActionTypes;
  accessRoles: string[] = ['Administrator'];
  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(
    private dataService: DataService,
    private instanceService: InstanceService
  ) {}

  ngOnInit() {
    //UniqueForUserManager:
    if (this.id === undefined && this.featureType === 'User Manager') {
      this.id = '00000000-0000-0000-0000-000000000000';
    }

    this.getOrganizationsById(this.id, false);
  }

  getOrgUserRoleById(organization: IOrganization) {
    if (organization != null) {
      this.dataService.getOrgUserRoleById(organization.id).subscribe((orgUserRole: IRole) => {
        if (orgUserRole != null) {
          organization.orgUserRoleName = orgUserRole.name;
        }
      });
    }
  }

  getOrganizationsById(id: string, loadMore: boolean) {
    if (id !== undefined) {
      this.dataService.getOrganizationsById(this.id, this.featureType, this.currentAmount, this.getAmount).subscribe((organizations: IOrganization[]) => {
        if (organizations.length > 0) {
          //OnLoadMoreData
          if (!loadMore) {
            this.organizations = organizations;
            this.currentAmount += organizations.length;
          } else {
            organizations.forEach(organization => {
              this.organizations = [...this.organizations, organization];
            });
            this.currentAmount += organizations.length;
          }

          if (organizations.length < this.getAmount) {
            this.moreResults = false;
          } else {
            this.moreResults = true;
          }

          this.organizations.forEach(organization => {
            this.getOrgUserRoleById(organization);
          });
        }
      });
    }
  }

  getOrgClaimUserDataById(orgId: string) {
    this.dataService.getOrgUserById(orgId).subscribe((orgUser: IOrganizationUser) => {
      if (orgUser) {
        this.organizations.forEach(org => {
          if (orgId === org.id) {
            org.claimUser = orgUser;
          }
        });
      }
    });
  }

  openGroup(panelState: boolean, orgId: string) {
    this.organizations.forEach(org => {
      if (org.id === orgId) {
        org.panelState = panelState;
      }
    });

    //GetClaimOrgUserData
    if (panelState) {
      this.getOrgClaimUserDataById(orgId);
    } else {
      //ResetPanelData
      this.organizations.forEach(org => {
        if (orgId === org.id) {
          org.claimUser = undefined;
        }
      });
    }
  }

  routeToOrg(orgId: string) {
    this.instanceService.openInstance('my-organization', orgId);
  }

  //RolesAndAccess.
  checkRoleAndAccess(roleName: string, typeBw: number) {
    if (this.accessRoles.includes(roleName) || typeBw & this.actionTypes.Assign || typeBw & this.actionTypes.Publish || typeBw & this.actionTypes.Manage) {
      return true;
    } else {
      return false;
    }
  }

  //Roles.
  checkRoles(roleName: string) {
    if (this.accessRoles.includes(roleName)) {
      return true;
    } else {
      return false;
    }
  }

  //WriteAccess.
  checkWriteAccess(typeBw: number) {
    if (typeBw & this.actionTypes.Assign) {
      return true;
    }
    return false;
  }

  //PublishAccess.
  checkPublishAccess(typeBw: number) {
    if (typeBw & this.actionTypes.Publish) {
      return true;
    }
    return false;
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
