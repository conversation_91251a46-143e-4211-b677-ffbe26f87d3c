<div class="parent-container" [ngClass]="{ floating: floating === true }">
  @if (!isCustom) {
    <div [ngClass]="showSelected ? 'selected' : 'normal'">
      <ion-item class="ion-no-padding inner-container" [ngClass]="{ 'no-padding': noPadding, 'no-border': noBorder, 'side-panel-input-padding': sidePanelPadding }" [title]="toolTip" #control>
        @if (showSelected !== true && showLabel) {
          <ion-label position="stacked" class="label-header">
            {{ label | translate | async }}
            <span title="text" class="reqAsterisk">
              @if (required) {
                <span>*</span>
              }
              <ion-icon name="information-circle-outline"></ion-icon>
            </span>
            @if (identifierText) {
              <span class="identifier">{{ identifierText }}</span>
            }
          </ion-label>
        }
        @if (isSearchDropDownModal) {
          <ion-input
            [value]="displayValue"
            [disabled]="disabled"
            [ngStyle]="{ 'background-color': backgroundColor }"
            placeholder="{{ placeHolder | translate | async }}"
            (click)="addSearchModalOpen($event)">
          </ion-input>
          @if (displayValue) {
            <ion-button class="clear-button" (click)="clear()" slot="end" size="small" fill="clear" color="medium">
              <ion-icon name="close"></ion-icon>
            </ion-button>
          }
          @if (errorMessage) {
            <ion-text>
              {{ errorMessage | translate | async }}
            </ion-text>
          }
        }
        @if (!isSearchDropDownModal) {
          <ion-select
            #select
            [multiple]="multiple"
            [ngStyle]="{ 'background-color': backgroundColor, width: '100%' }"
            (ionChange)="valueChange($event)"
            placeholder="{{ placeHolder | translate | async }}"
            [disabled]="disabled"
            (blur)="onTouched()"
            [value]="textValue"
            interface="popover">
            <ng-container>
              @for (item of options; track item) {
                <ion-select-option [value]="item.id">
                  {{ item.value | translate | async }}
                </ion-select-option>
              }
            </ng-container>
          </ion-select>
        }
      </ion-item>
    </div>
  }
  @if (isCustom) {
    <div class="earning-criteria-ref-type-container normal" [ngClass]="{ 'no-margin': noMargin }">
      <ion-input [value]="displayValue" [disabled]="disabled" (click)="addSearchModalOpen($event)"> </ion-input>
    </div>
  }
</div>
