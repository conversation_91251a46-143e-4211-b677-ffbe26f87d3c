import { Component, Input } from '@angular/core';
import { AsyncPipe } from '@angular/common';
import { IEarningCriteriaType } from '@app/core/contracts/contract';
import { PopoverController, IonicModule } from '@ionic/angular';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-add-earning-criteria-popover',
    templateUrl: './add-earning-criteria-popover.component.html',
    styleUrls: ['./add-earning-criteria-popover.component.scss'],
    imports: [IonicModule, AsyncPipe, TranslatePipe]
})
export class AddEarningCriteriaPopoverComponent {
  @Input() earningCriteriaTypes: IEarningCriteriaType[] = [];
  constructor(private popover: PopoverController) {}

  selectType(selectedType: IEarningCriteriaType) {
    this.popover.dismiss(selectedType);
  }
}
