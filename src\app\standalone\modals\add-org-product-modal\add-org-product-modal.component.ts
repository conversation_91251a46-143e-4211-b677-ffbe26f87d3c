import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { AsyncPipe } from '@angular/common';
import { UntypedFormControl, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IProductRenew, IProductSearch } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { ModalController, IonicModule } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';
import { MatAccordion, MatExpansionPanel, MatExpansionPanelHeader, MatExpansionPanelTitle } from '@angular/material/expansion';
import { RenewProductComponent } from '../../components/renew-product/renew-product.component';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-add-org-product-modal',
    templateUrl: './add-org-product-modal.component.html',
    styleUrls: ['./add-org-product-modal.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, MatAccordion, MatExpansionPanel, MatExpansionPanelHeader, MatExpansionPanelTitle, RenewProductComponent, AsyncPipe, TranslatePipe]
})
export class AddOrgProductModalComponent implements OnInit, OnDestroy {
  @Input() orgId: string;
  @Input() type: string;
  componentDestroyed$: Subject<boolean> = new Subject();
  products: IProductSearch[] = [];
  originalProducts: IProductSearch[] = [];
  searchForm: UntypedFormGroup;
  orgSearch: UntypedFormControl;
  currentDate = new Date();
  currentSubscriptionExpires = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth(), 1);
  productRenew: IProductRenew | null;
  productSelected: string | null = null;
  constructor(
    private modalController: ModalController,
    private dataService: DataService
  ) {}

  ngOnInit() {
    this.createFormControls();
    this.createForm();
    this.getProducts();
  }

  createFormControls() {
    this.orgSearch = new UntypedFormControl('');
  }

  createForm() {
    this.searchForm = new UntypedFormGroup({
      orgSearch: this.orgSearch,
    });
  }

  searchProduct() {
    this.products = this.originalProducts;
    const searchTerm = this.orgSearch.value.toLowerCase();
    this.products = this.products.filter(x => x.name.toLowerCase().includes(searchTerm));
  }

  updateProduct(productRenew: IProductRenew, productId: string) {
    const newProduct = {
      id: productId,
      expiryDate: productRenew.expiryDate,
      period: productRenew.period,
    } as IProductRenew;
    this.productRenew = newProduct;
  }

  getProducts() {
    this.dataService
      .getProductsAvailableToOrg(this.orgId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(res => {
        if (res) {
          this.products = res;
          this.originalProducts = res;
        }
      });
  }

  selected(prodId: any) {
    if (this.productSelected === prodId) {
      this.productSelected = null;
    } else {
      this.productSelected = prodId;
    }
  }

  close() {
    this.modalController.dismiss();
  }

  add() {
    this.modalController.dismiss(this.productRenew);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
