<ion-content class="ion-padding" [style.height.vh]="height" [style.min-height.vh]="height" [style.max-height.vh]="height">
  <h1 position="stacked">{{ headerText | translate | async }}</h1>
  <p>{{ bodyText | translate | async }}</p>
</ion-content>
<ion-footer>
  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button color="medium" (click)="cancel()">{{ cancelButtonText | translate | async }}</ion-button>
    </ion-buttons>
    <ion-buttons slot="end">
      <ion-button class="button-highlight" fill="solid" (click)="confirm()">{{ buttonText | translate | async }}</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-footer>
