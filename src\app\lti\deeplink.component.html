<div class="add-content-container">
  <form class="search-container" [formGroup]="searchForm">
    <ion-grid>
      <div class="heading-row">
        <div class="heading-col">
          <h5>{{ 'Search for content' | translate | async }}</h5>
          <div class="under-line"><hr /></div>
        </div>
      </div>

      <ion-row>
        <ion-col>
          <ion-searchbar
            color="dark"
            formControlName="instanceSearch"
            type="search"
            placeholder="{{ 'Search the platform' | translate | async }}"
            showCancelButton="focus"
            style="background-color: #1e1e1e"></ion-searchbar>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>

  <ion-content class="parent-content-container">
    @if (instances?.length > 0) {
      @for (instance of instances; track instance) {
        <ion-card (click)="submit(instance)" style="cursor: pointer">
          <ion-row>
            @if (instance.iconAssetId) {
              <ion-col class="thumbnail-col" size="3">
                <img src="{{ currentEnvironment }}asset/{{ instance.iconAssetId }}/content" alt="icon" />
              </ion-col>
            }
            <ion-col class="headings-col" size="9">
              <div class="instance-title">{{ instance.title }}</div>
              <div class="feature-descripitor">
                <span class="inner-text" [innerHTML]="instance?.feature?.descriptors ?? '' | parsePipe: instance.id : null : true | async | translate | async"></span>
              </div>
            </ion-col>
          </ion-row>
        </ion-card>
      }
    }
  </ion-content>
</div>
