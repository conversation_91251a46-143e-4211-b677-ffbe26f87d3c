<div class="parent-container">
  <ion-grid>
    <ion-row class="top-heading-row">
      <h1>{{ 'Contact Info' | translate | async }}</h1>
    </ion-row>
    <ion-row class="mail-row">
      <ion-col class="icon-col" size="*">
        <ion-icon name="mail"></ion-icon>
      </ion-col>
      <ion-col class="email-content-col">
        <ion-grid>
          <ion-row>
            <ion-col>
              <app-text-value [noMargin]="true" [defaultValue]="getPrimaryEmail() ?? 'Primary Email'"></app-text-value>
            </ion-col>
          </ion-row>
          <ion-row>
            <ion-col>
              <app-email-chips-value></app-email-chips-value>
            </ion-col>
          </ion-row>
        </ion-grid>
      </ion-col>
    </ion-row>
    <ion-row class="number-row">
      <ion-col>
        <app-phone-number-value></app-phone-number-value>
      </ion-col>
    </ion-row>
  </ion-grid>
</div>
