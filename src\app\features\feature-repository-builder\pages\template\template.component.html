<mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start" color="accent">
  <mat-tab [label]="('FEATURE OVERVIEW' | translate | async) || 'FEATURE OVERVIEW'">
    <ng-template matTabContent>
      @if (feature) {
        <app-feature-repository-overview [feature]="feature" (featureUpdated)="updateFeature($event)"></app-feature-repository-overview>
      }
    </ng-template>
  </mat-tab>
  <mat-tab [label]="('TAB BUILDER' | translate | async) || 'TAB BUILDER'">
    <ng-template matTabContent>
      @if (feature) {
        <app-feature-repository-builder-fields [feature]="feature"></app-feature-repository-builder-fields>
      }
    </ng-template>
  </mat-tab>
</mat-tab-group>
