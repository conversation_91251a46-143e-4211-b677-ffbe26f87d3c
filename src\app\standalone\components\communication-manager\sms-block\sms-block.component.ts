import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { ICommunicationBlock } from '@app/core/contracts/contract';
import { IonicModule } from '@ionic/angular';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { ContentQuillEditorComponent } from '../../content-quill-editor/content-quill-editor.component';

@Component({
    selector: 'app-sms-block',
    templateUrl: './sms-block.component.html',
    styleUrls: ['./sms-block.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, ContentQuillEditorComponent, AsyncPipe, TranslatePipe]
})
export class SmsBlockComponent implements OnInit, OnDestroy {
  @Input() communicationId: string;
  @Input() smsBlock: ICommunicationBlock | null;
  @Output() communicationBlockUpdated: EventEmitter<ICommunicationBlock> = new EventEmitter();
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  smsBlockForm: UntypedFormGroup;
  formValueChanges$: Subscription;
  hideQuillPersonalize = true;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private formBuilder: UntypedFormBuilder) {}

  ngOnInit() {
    this.createForm();
  }

  createForm() {
    this.smsBlockForm = this.formBuilder.group({
      message: [this.smsBlock?.message],
      fromAddress: [this.smsBlock?.fromAddress],
    });

    this.subscribeToFormChanges();
  }

  setMessageValue(quillData: any) {
    this.smsBlockForm.controls.message.setValue(quillData);
  }

  setObjectValues() {
    if (this.smsBlockForm.valid) {
      let communicationBlock = {
        id: this.smsBlock?.id,
        communicationId: this.communicationId,
        message: this.smsBlockForm.controls.message.value,
        fromAddress: this.smsBlockForm.controls.fromAddress.value,
        blockType: 'SMS',
      } as ICommunicationBlock;

      //Merge
      if (this.smsBlock) {
        communicationBlock = { ...this.smsBlock, ...communicationBlock };
      }

      this.communicationBlockUpdated.emit(communicationBlock);
    }
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.smsBlockForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.smsBlockForm.valid);
      this.setObjectValues();
    });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
