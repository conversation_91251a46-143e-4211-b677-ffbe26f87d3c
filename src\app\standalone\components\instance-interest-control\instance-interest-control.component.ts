import { Component, Input, OnD<PERSON>roy, OnInit, forwardRef } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR, UntypedFormControl, UntypedFormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IComponent, IInstanceInterest, IInstanceInterestText } from '@app/core/contracts/contract';
import { Subject, takeUntil } from 'rxjs';
import { NgClass, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { IonicModule } from '@ionic/angular';
import { DynamicTextInputControlComponent } from '../dynamic-text-input-control/dynamic-text-input-control.component';
import { TextInputControlComponent } from '../text-input-control/text-input-control.component';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';

@Component({
    selector: 'app-instance-interest-control',
    templateUrl: './instance-interest-control.component.html',
    styleUrls: ['./instance-interest-control.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => InstanceInterestControlComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => InstanceInterestControlComponent),
        },
    ],
    imports: [NgClass, IonicModule, FormsModule, ReactiveFormsModule, TextInputControlComponent, DynamicTextInputControlComponent, AsyncPipe, TranslatePipe]
})
export class InstanceInterestControlComponent extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() override label: string;
  @Input() description: string;
  @Input() component!: IComponent;
  @Input() sidePanelPadding = false;
  instanceInterestForm: UntypedFormGroup;
  instanceInterest: IInstanceInterest;
  componentDestroyed$: Subject<boolean> = new Subject();
  interestText: IInstanceInterestText[] = [];

  constructor() {
    super();
  }

  ngOnInit() {
    this.fieldValueChanged.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      if (this.textValue) {
        this.instanceInterest = JSON.parse(this.textValue) as IInstanceInterest;
      } else {
        this.extractButtonText();
      }

      this.createForm();
      this.formChanges();
    });
  }

  extractButtonText() {
    if (this.component?.templateField?.default3) {
      this.interestText = JSON.parse(this.component.templateField.default3) as IInstanceInterestText[];
    }
  }

  getInterestText(sortOrder: number) {
    return this.interestText?.find(x => x.sortOrder === sortOrder)?.text ?? '';
  }

  createForm() {
    this.instanceInterestForm = new UntypedFormGroup({
      heading: new UntypedFormControl(this.instanceInterest?.heading ?? this.component.templateField.default1, [Validators.required]),
      description: new UntypedFormControl(this.instanceInterest?.description ?? this.component.templateField.default2, [Validators.required]),
      interestNoneText: new UntypedFormControl(this.instanceInterest?.interestNoneText ?? this.getInterestText(0)),
      interestMediumText: new UntypedFormControl(this.instanceInterest?.interestMediumText ?? this.getInterestText(1)),
      interestHighText: new UntypedFormControl(this.instanceInterest?.interestHighText ?? this.getInterestText(2)),
    });
  }

  formChanges() {
    this.instanceInterestForm.valueChanges.subscribe(() => {
      this.updateComponent(this.instanceInterestForm.value as IInstanceInterest);
    });
  }

  updateComponent(instanceInterestIn: IInstanceInterest) {
    this.setValue(JSON.stringify(instanceInterestIn));
  }

  override setValue(value: string) {
    this.writeValue(value);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
