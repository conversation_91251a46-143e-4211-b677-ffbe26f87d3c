import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ICommunicationBlock } from '@app/core/contracts/contract';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { AsyncPipe } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { ContentQuillEditorComponent } from '../../content-quill-editor/content-quill-editor.component';

@Component({
    selector: 'app-email-block',
    templateUrl: './email-block.component.html',
    styleUrls: ['./email-block.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, TextInputControlComponent, ContentQuillEditorComponent, AsyncPipe, TranslatePipe]
})
export class EmailBlockComponent implements OnInit, OnDestroy {
  @Input() communicationId: string;
  @Input() emailBlock: ICommunicationBlock | null;
  @Output() communicationBlockUpdated: EventEmitter<ICommunicationBlock> = new EventEmitter();
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  emailBlockForm: UntypedFormGroup;
  formValueChanges$: Subscription;
  backgroundColor = '#181818';
  hideQuillPersonalize = true;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private formBuilder: UntypedFormBuilder) {}

  ngOnInit() {
    this.createForm();
  }

  createForm() {
    this.emailBlockForm = this.formBuilder.group({
      fromAddress: [this.emailBlock?.fromAddress],
      templateId: [this.emailBlock?.templateId],
      subject: [this.emailBlock?.title],
      message: [this.emailBlock?.message],
    });

    this.subscribeToFormChanges();
  }

  setMessageValue(quillData: any) {
    this.emailBlockForm.controls.message.setValue(quillData);
  }

  setObjectValues() {
    if (this.emailBlockForm.valid) {
      let communicationBlock = {
        templateId: this.emailBlockForm.controls.templateId.value,
        fromAddress: this.emailBlockForm.controls.fromAddress.value,
        id: this.emailBlock?.id,
        communicationId: this.communicationId,
        title: this.emailBlockForm.controls.subject.value,
        message: this.emailBlockForm.controls.message.value,
        blockType: 'Email',
      } as ICommunicationBlock;

      //Merge
      if (this.emailBlock) {
        communicationBlock = { ...this.emailBlock, ...communicationBlock };
      }

      this.communicationBlockUpdated.emit(communicationBlock);
    }
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.emailBlockForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.emailBlockForm.valid);
      this.setObjectValues();
    });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
