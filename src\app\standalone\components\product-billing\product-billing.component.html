<div class="table-container">
  <table class="table-grid" mat-table [dataSource]="dataSource" matSort #sort="matSort">
    <ng-container matColumnDef="invoiceId">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'INVOICE ID' | translate | async }}</th>
      <td mat-cell *matCellDef="let element">{{ element.invoiceId }}</td>
    </ng-container>
    <ng-container matColumnDef="dueDate">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'DUE DATE' | translate | async }}</th>
      <td mat-cell *matCellDef="let element">{{ element.dueDate | date: 'M/d/yyyy' }}</td>
    </ng-container>
    <ng-container matColumnDef="amount">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'AMOUNT' | translate | async }}</th>
      <td mat-cell *matCellDef="let element">{{ element.amount | currency }}</td>
    </ng-container>
    <ng-container matColumnDef="status">
      <th mat-header-cell *matHeaderCellDef mat-sort-header>{{ 'STATUS' | translate | async }}</th>
      <td mat-cell *matCellDef="let element">{{ element.status | translate | async }}</td>
    </ng-container>
    <ng-container matColumnDef="download">
      <th style="border-right: 0px" mat-header-cell *matHeaderCellDef></th>
      <td mat-cell *matCellDef="let element">
        <span class="download-link">{{ 'DOWNLOAD' | translate | async }}</span>
      </td>
    </ng-container>
    <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
  </table>
  <mat-paginator #paginator showFirstLastButtons [pageSizeOptions]="[10, 25, 50, 100]"></mat-paginator>
</div>
