import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { IOrganizationSsoAuth, IOrganizationSsoAuthIn, IOrgPrivacyTypeIn, IProductJoinCodeSetting, IProductOrgDomain, IProductSetting, IUserRole } from '@app/core/contracts/contract';
import { MatAccordion, MatExpansionPanel, MatExpansionPanelHeader } from '@angular/material/expansion';
import { ProductDomainMappingSettingsComponent } from '../product-domain-mapping-settings/product-domain-mapping-settings.component';
import { ProductJoinCodeSettingsComponent } from '../product-join-code-settings/product-join-code-settings.component';
import { PrivacyManagerSetupComponent } from '../privacy-manager-setup/privacy-manager-setup.component';
import { D2lPortalSettingsComponent } from '../d2l-portal-settings/d2l-portal-settings.component';

@Component({
    selector: 'app-product-settings',
    templateUrl: './product-settings.component.html',
    styleUrls: ['./product-settings.component.scss'],
    imports: [
        MatAccordion,
        MatExpansionPanel,
        MatExpansionPanelHeader,
        ProductDomainMappingSettingsComponent,
        ProductJoinCodeSettingsComponent,
        PrivacyManagerSetupComponent,
        D2lPortalSettingsComponent,
        AsyncPipe,
        TranslatePipe,
    ]
})
export class ProductSettingsComponent implements OnInit {
  @Input() productId: string;
  @Input() organizationId: string;
  @Input() productOrgId: string;
  @Input() privacyTypeId: string | undefined;
  @Input() userRoles: IUserRole[] = [];
  @Input() productOrgDomains: IProductOrgDomain[] = [];
  @Input() productJoinCodeSettings?: IProductJoinCodeSetting[];
  @Input() productOrgSsoAuth: IOrganizationSsoAuth;
  @Output() productOrgDomainsChange = new EventEmitter<IProductOrgDomain[]>();
  @Output() orgSsoAuthChange = new EventEmitter<IOrganizationSsoAuthIn>();
  @Output() productJoinCodesChange = new EventEmitter<any>();
  @Output() productOrgPrivacyTypeChange = new EventEmitter<IOrgPrivacyTypeIn>();
  isParentPanelClosed = false;
  productSettings: IProductSetting[] = [];
  index = 0;
  constructor() {}

  ngOnInit() {
    this.getProductSettings();
  }

  openGroup(panelState: boolean) {
    this.isParentPanelClosed = panelState;
  }

  getProductSettings() {
    //CurrentStaticCategories
    const staticSettings: IProductSetting[] = [
      { id: '1', title: 'D2L | SSO', description: 'Allow users to sign in from their D2L portal', isActive: false },
      { id: '2', title: 'Domain Mapping', description: 'Auto add people to your organization based on their email domains', isActive: false },
      { id: '3', title: 'Join Codes', description: 'Let people self-enrol by sharing with them a secret join code.', isActive: false },
      { id: '4', title: 'Privacy Experience Manager', description: 'Limit the amount of personal data that this product is collecting about your users.', isActive: false },
    ];

    this.productSettings = staticSettings;
  }

  toggleIsActive(selectedProductSetting: IProductSetting, isActive: boolean) {
    if (isActive === true) {
      selectedProductSetting.isActive = true;
    } else {
      selectedProductSetting.isActive = false;
    }
  }

  saveOrgSsoAuth(orgSsoAuthIn: IOrganizationSsoAuthIn) {
    this.orgSsoAuthChange.emit(orgSsoAuthIn);
  }

  saveProductOrgDomains(domainsIn: IProductOrgDomain[]) {
    this.productOrgDomainsChange.emit(domainsIn);
  }

  saveProductJoinCodes() {
    this.productJoinCodesChange.emit(null);
  }

  saveProductOrgPrivacyType(privacyTypeIn: IOrgPrivacyTypeIn) {
    this.productOrgPrivacyTypeChange.emit(privacyTypeIn);
  }
}
