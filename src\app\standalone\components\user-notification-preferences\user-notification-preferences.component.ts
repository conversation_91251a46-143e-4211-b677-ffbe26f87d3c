import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ITag, IUserCommunicationPreference } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { forkJoin, Subject, takeUntil } from 'rxjs';
import { SelectOptionControlComponent } from '../select-option-control/select-option-control.component';
import { IonicModule } from '@ionic/angular';
import { UserNotificationPreferencesExpanderComponent } from '../user-notification-preferences-expander/user-notification-preferences-expander.component';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-user-notification-preferences',
    templateUrl: './user-notification-preferences.component.html',
    styleUrls: ['./user-notification-preferences.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, SelectOptionControlComponent, IonicModule, UserNotificationPreferencesExpanderComponent, AsyncPipe, TranslatePipe]
})
export class UserNotificationPreferencesComponent implements OnInit, OnDestroy {
  emails: KeyValue[];
  formGroup: UntypedFormGroup;
  categories: ITag[];
  userCommunicationPreference: IUserCommunicationPreference;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private dataService: DataService,
    private formBuilder: UntypedFormBuilder,
    private systemPropertyService: SystemPropertiesService
  ) {}

  ngOnInit() {
    this.initData();
  }

  initData() {
    const property = this.systemPropertyService.userProperties.find(x => x.key === 'Users.email');
    this.emails = [{ id: property?.value, value: property?.value } as KeyValue];

    const requestList = [this.dataService.getTagChildrenByParentName('Communication Categories'), this.dataService.getUserCommunicationPreference()];
    forkJoin(requestList)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        this.userCommunicationPreference =
          (data[1] as IUserCommunicationPreference) ?? ({ id: null, preferredEmail: null, combineNotifications: false, userCommunicationPreferenceSettings: [] } as IUserCommunicationPreference);
        this.categories = data[0] as ITag[];

        this.setupForm();
      });
  }

  setupForm() {
    this.formGroup = this.formBuilder.group({
      email: [this.userCommunicationPreference?.preferredEmail],
      combine: [this.userCommunicationPreference?.combineNotifications ?? false],
    });
  }

  subscribeToFormChanges() {
    this.formGroup.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.updateUserCommunicationPreference();
    });
  }

  updateUserCommunicationPreference() {
    this.dataService.updateUserCommunicationPreferences(this.userCommunicationPreference).pipe(takeUntil(this.componentDestroyed$)).subscribe();
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
