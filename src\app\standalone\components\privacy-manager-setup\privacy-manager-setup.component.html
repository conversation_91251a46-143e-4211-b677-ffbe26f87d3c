<div class="parent-container">
  @if (dataLoaded) {
    <ion-radio-group [ngClass]="{ 'layout-xs': layoutService.currentScreenSize === 'xs' }" [value]="privacyTypeId" class="radio-group-container">
      @for (type of privacyTypes; track type) {
        <ion-item>
          <ion-row (click)="selectedType(type.id)" class="item-row">
            <ion-col>
              <div class="top-heading">
                <div class="top-inline">
                  <h1>{{ (type.name ?? '') | translate | async }}</h1>
                  <div><ion-radio [value]="type.id"></ion-radio></div>
                </div>
                <h2>{{ (type.description ?? '') | translate | async }}</h2>
              </div>
              <div class="bottom-heading">
                <h1>{{ 'Implications' | translate | async }}</h1>
                <h2>{{ (type.implications ?? '') | translate | async }}</h2>
              </div>
            </ion-col>
          </ion-row>
        </ion-item>
      }
    </ion-radio-group>
  }
</div>
