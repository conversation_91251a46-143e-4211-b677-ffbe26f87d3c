@if (row) {
  <div [ngClass]="{ 'full-height': routeParams?.viewType === viewTypes.Player, 'ng-hide': row.hidden }">
    <!-- PAGE ROW -->
    @if (row.displayType.name === 'Page') {
      <div class="page-row" [attr.data-page]="instance.title">
        <!-- CONTENT SENTENCE -->
        @if (row.contentSentence && row.contentSentence.length > 0) {
          <ion-row text-center>
            <mat-icon class="auto-margin" svgIcon="library"></mat-icon>
            <quill-view [content]="row.contentSentence | parsePipe: instance?.id : rowId | async | translate | async" format="html" theme="snow"></quill-view>
          </ion-row>
        }
        <!-- LIST VIEW -->
        @if (routeParams?.viewType === viewTypes.List) {
          <ng-container>
            <app-list-view
              [row]="row"
              [instance]="instance"
              [searchFilter]="searchFilter"
              [routeParams]="routeParams"
              [isEducator]="isEducator"
              [hasAdminAccess]="hasAdminAccess"
              [hideAddButton]="hideAddButton"
              (selectedChanged)="setSelected($event)"
              (deliverableAddClickedOut)="addRowToClassroom()"></app-list-view>
          </ng-container>
          <!-- PLAYER VIEW -->
        } @else if (routeParams?.viewType === viewTypes.Player && isPlayerSidePanel === true) {
          <app-player-view-row
            [row]="row"
            [isPlayerSidePanel]="isPlayerSidePanel"
            [instance]="instance"
            [searchFilter]="searchFilter"
            [routeParams]="routeParams"
            [selectedUserId]="selectedUserId"
            [playerSidePanel]="playerSidePanel"
            (selectedChanged)="setSelected($event.event, $event.actionBw)"
            (rowContentLoaded)="loadedRowContent($event)"></app-player-view-row>
          <!-- GRID VIEW -->
        } @else {
          <app-grid-view
            [searchFilter]="searchFilter"
            [row]="row"
            [readingMode]="readingMode"
            [isDraggable]="false"
            [instance]="instance"
            [instanceComponentId]="instanceSectionComponentId"
            [isEducator]="isEducator"
            [hasAdminAccess]="hasAdminAccess"
            [hideAddButton]="hideAddButton"
            [entityId]="getEntityId()"
            [routeParams]="routeParams"
            (selectedChanged)="setSelected($event)"
            (isUpdateCustomRowContent)="addEditCustomRowContent($event)"
            (gridContentRemoved)="rowContentRemoved()"></app-grid-view>
        }
      </div>
      <!-- STANDARD ROW -->
    } @else if (row.displayType.name === 'Standard') {
      <div
        [ngStyle]="{ 'background-color': !readingMode ? '#222' : 'none' }"
        class="full-height"
        [ngClass]="{
          'row-main-container': routeParams?.viewType !== viewTypes.Player && routeParams?.viewType !== viewTypes.Builder,
          'row-container': routeParams?.viewType !== viewTypes.Player && routeParams?.viewType !== viewTypes.Builder,
          'row-container-player': (routeParams?.viewType === viewTypes.Player || routeParams?.viewType === viewTypes.Builder) && !selectedUserId,
          'row-container-player-grading': routeParams?.viewType === viewTypes.Player && selectedUserId,
          'deliverable-row': isDeliverableRow,
          'max-width': instance?.feature?.isFullWidth === true,
        }"
        [attr.data-instance]="instance?.id">
        <!-- NOT PLAYER -->
        @if (routeParams?.viewType !== viewTypes.Player) {
          <div
            [ngStyle]="{ 'padding-left': !readingMode ? '5px' : '0' }"
            [ngClass]="{
              'header-image': row.rowType.typeBw > rowTypes.ModifiablePackage,
            }"
            [class.header-image-classroom]="row.rowType.typeBw === rowTypes.ModifiablePackage"
            [style]="row.rowType.typeBw >= rowTypes.ModifiablePackage ? '--background-image:url(' + assetUrl + ');' : null">
            <ion-grid
              [class.container-row-margin-sides]="isDeliverableRow || routeParams?.viewType !== viewTypes.Builder"
              class="top-text-container"
              [ngStyle]="{ padding: isDeliverableRow === true && routeParams?.viewType === viewTypes.Builder ? '8px' : null }"
              [ngClass]="{ 'hide-grid': routeParams?.viewType === viewTypes.Player }">
              <ion-row>
                <ion-col>
                  <div [ngStyle]="{ 'text-align': row.alignment }">
                    <!-- CONTENT SENTENCE -->
                    @if (row.contentSentence && row.contentSentence.length > 0) {
                      <ion-row text-center>
                        <mat-icon class="auto-margin" svgIcon="library"></mat-icon>
                        <quill-view [content]="row.contentSentence | parsePipe: instance?.id : rowId | async | translate | async" format="html" theme="snow"></quill-view>
                      </ion-row>
                    }
                    <!-- TITLE -->
                    @if (row && row.title && (row.title.length ?? 0 > 0)) {
                      <div
                        class="header"
                        [ngClass]="{
                          header1: row.titleHtmltag === 'h1',
                          header2: row.titleHtmltag === 'h2',
                          header3: row.titleHtmltag === 'h3',
                          header4: row.titleHtmltag === 'h4',
                          'header-none': row.titleHtmltag === 'none' || !row.titleHtmltag,
                        }">
                        {{ row.title | translate | async }}
                      </div>
                    }
                  </div>
                </ion-col>
              </ion-row>
              <ion-row>
                <!-- DESCRIPTION -->
                @if (row.description) {
                  <ion-col class="row-description-col">
                    <!-- descriptionHtmltag -->
                    @if (row.descriptionHtmltag) {
                      <div class="row-description-container">
                        @switch (row.descriptionHtmltag) {
                          @case ('p') {
                            <p>{{ row.description | translate | async }}</p>
                          }
                          @case ('i') {
                            <i>{{ row.description | translate | async }}</i>
                          }
                          @case ('strong') {
                            <strong>{{ row.description | translate | async }}</strong>
                          }
                        }
                      </div>
                      <!-- description -->
                    } @else if (row.description) {
                      <div class="row-description-container">
                        <p>{{ row.description | translate | async }}</p>
                      </div>
                    }
                  </ion-col>
                }
              </ion-row>
              <!-- DELIVERABLE ROW HEADER LINE -->
              @if (isDeliverableRow === true) {
                <div class="header-line"></div>
              }
            </ion-grid>
          </div>
        }
        <!-- PLAYER -->
        <ion-grid
          [class.container-row-margin-sides]="isDeliverableRow || (routeParams?.viewType !== viewTypes.Player && routeParams?.viewType !== viewTypes.Builder)"
          [ngClass]="{ carousel: row.courouselFlag }">
          <!-- DELIVERABLE ROW ADD BTN -->
          @if (row.rowType.typeBw === rowTypes.Deliverable && builderView === true) {
            <button class="add-btn" mat-fab color="primary" (click)="deliverableAddClicked($event)">
              <mat-icon>add</mat-icon>
            </button>
          }
          <!-- NOT PLAYER / NOT READINGMODE -->
          @if (routeParams?.viewType !== viewTypes.Player && !readingMode) {
            <ion-row>
              <ion-col>
                <div class="dynamic-buttons-col">
                  <!-- SAVE BTN -->
                  @if (validForm && changedTabs.length > 0) {
                    <ion-button (click)="saveAll()" color="primary" fill="solid">
                      <mat-icon>save</mat-icon>
                      {{ 'SAVE' | translate | async }}
                    </ion-button>
                  }
                  <!-- ADD NON CUSTOM ROW CONTENT -->
                  @if (
                    (row.rowType.typeBw === rowTypes.Manual || row.rowType.typeBw >= rowTypes.ModifiablePackage) &&
                    selectedTabIndex === 0 &&
                    row.rowType.typeBw !== rowTypes.Deliverable &&
                    instance?.feature?.featureType?.name !== 'Portfolio'
                  ) {
                    <ion-button class="add-content" (click)="addRowContent(null)" color="primary" fill="solid">
                      <mat-icon>add</mat-icon>
                      {{ 'Add Content' | translate | async }}
                    </ion-button>
                    <!-- ADD CUSTOM ROW CONTENT -->
                  } @else if (row.rowType.typeBw === rowTypes.Custom) {
                    <ion-button class="add-content" (click)="addEditCustomRowContent()" color="primary" fill="solid">
                      <mat-icon>add</mat-icon>
                      {{ 'Add Content' | translate | async }}
                    </ion-button>
                  }
                </div>
              </ion-col>
            </ion-row>
          }
          <ion-row>
            <ion-col>
              <!-- READING MODE -->
              @if (readingMode) {
                <!-- PLAYER VIEW -->
                @if (routeParams?.viewType === viewTypes.Player && isPlayerSidePanel === true) {
                  <app-player-view-row
                    [reload$]="reload$"
                    [row]="row"
                    [isPlayerSidePanel]="isPlayerSidePanel"
                    [instance]="instance"
                    [readingMode]="readingMode"
                    [searchFilter]="searchFilter"
                    [routeParams]="routeParams"
                    [selectedUserId]="selectedUserId"
                    [instanceComponentId]="instanceSectionComponentId"
                    [playerSidePanel]="playerSidePanel"
                    (selectedChanged)="setSelected($event.event, $event.actionBw)"
                    (rowContentLoaded)="loadedRowContent($event)">
                  </app-player-view-row>
                  <!-- LIST / DELIVERABLE VIEW -->
                } @else if (routeParams?.viewType === viewTypes.List || row.rowType.typeBw === rowTypes.Deliverable) {
                  <app-list-view
                    style="width: 100%"
                    [row]="row"
                    [instance]="instance"
                    [save$]="save$"
                    [reload$]="reload$"
                    [instanceComponentId]="instanceSectionComponentId"
                    [searchFilter]="searchFilter"
                    [readingMode]="readingMode"
                    [isEducator]="isEducator"
                    [hasAdminAccess]="hasAdminAccess"
                    [hideAddButton]="hideAddButton"
                    [entityId]="getEntityId()"
                    [routeParams]="routeParams"
                    (selectedChanged)="setSelected($event)"
                    (contentRemoved)="rowContentRemoved()"
                    (deliverableAddClickedOut)="addRowToClassroom()">
                  </app-list-view>
                  <!-- GRID VIEW -->
                } @else {
                  <app-grid-view
                    style="width: 100%"
                    [instance]="instance"
                    [row]="row"
                    [save$]="save$"
                    [reload$]="reload$"
                    [searchFilter]="searchFilter"
                    [routeParams]="routeParams"
                    (afterSave)="tabSaved('CONTENT')"
                    (changed)="tabChanged('CONTENT')"
                    (selectedChanged)="setSelected($event)"
                    [readingMode]="readingMode"
                    [isEducator]="isEducator"
                    [hasAdminAccess]="hasAdminAccess"
                    [hideAddButton]="hideAddButton"
                    [instanceComponentId]="instanceSectionComponentId"
                    [entityId]="getEntityId()"
                    (isUpdateCustomRowContent)="addEditCustomRowContent($event)"
                    (gridContentRemoved)="rowContentRemoved()"></app-grid-view>
                }
              }
              <!-- NOT READINGMODE -->
              @if (!readingMode) {
                <!-- Achievement Manager / Favorites / Portfolio -->
                @if (instance?.feature?.featureType?.name === 'Achievement Manager' || instance?.feature?.featureType?.name === 'Favorites' || instance?.feature?.featureType?.name === 'Portfolio') {
                  <!-- LIST VIEW -->
                  @if (routeParams?.viewType === viewTypes.List) {
                    <app-list-view
                      style="width: 100%"
                      [row]="row"
                      [instance]="instance"
                      [save$]="save$"
                      [reload$]="reload$"
                      [instanceComponentId]="instanceSectionComponentId"
                      [isEducator]="isEducator"
                      [hasAdminAccess]="hasAdminAccess"
                      [hideAddButton]="hideAddButton"
                      [searchFilter]="searchFilter"
                      [readingMode]="readingMode"
                      [entityId]="getEntityId()"
                      [routeParams]="routeParams"
                      (selectedChanged)="setSelected($event)"
                      (deliverableAddClickedOut)="addRowToClassroom()">
                    </app-list-view>
                    <!-- GRID VIEW -->
                  } @else if (routeParams?.viewType !== viewTypes.Player) {
                    <app-grid-view
                      style="width: 100%"
                      [instance]="instance"
                      [row]="row"
                      [readingMode]="readingMode"
                      [save$]="save$"
                      [reload$]="reload$"
                      [searchFilter]="searchFilter"
                      [instanceComponentId]="instanceSectionComponentId"
                      [entityId]="getEntityId()"
                      [isEducator]="isEducator"
                      [hasAdminAccess]="hasAdminAccess"
                      [hideAddButton]="hideAddButton"
                      [routeParams]="routeParams"
                      (afterSave)="tabSaved('CONTENT')"
                      (changed)="tabChanged('CONTENT')"
                      (selectedChanged)="setSelected($event)"
                      (isUpdateCustomRowContent)="addEditCustomRowContent($event)"
                      (gridContentRemoved)="rowContentRemoved()">
                    </app-grid-view>
                  }
                  <!-- NOT Achievement Manager / Favorites / Portfolio -->
                } @else {
                  <mat-tab-group
                    [ngClass]="{ 'assignment-tabs': this.routeParams?.tabName?.toLowerCase() === 'assignments' }"
                    mat-stretch-tabs="true"
                    mat-align-tabs="start"
                    #matTabGroup
                    [disableRipple]="true"
                    [(selectedIndex)]="selectedTabIndex"
                    (selectedTabChange)="onTabChanged($event)">
                    <mat-tab label="{{ 'CONTENT' | translate | async }}">
                      <ng-template matTabContent>
                        <!-- PLAYER VIEW -->
                        @if (routeParams?.viewType === viewTypes.Player && isPlayerSidePanel === true) {
                          <app-player-view-row
                            [reload$]="reload$"
                            [row]="row"
                            [isPlayerSidePanel]="isPlayerSidePanel"
                            [instance]="instance"
                            [readingMode]="readingMode"
                            [searchFilter]="searchFilter"
                            [routeParams]="routeParams"
                            [selectedUserId]="selectedUserId"
                            [playerSidePanel]="playerSidePanel"
                            [instanceComponentId]="instanceSectionComponentId"
                            (selectedChanged)="setSelected($event.event, $event.actionBw)"
                            (rowContentLoaded)="loadedRowContent($event)"></app-player-view-row>
                          <!-- LIST / DELIVERABLE VIEW -->
                        } @else if (routeParams?.viewType === viewTypes.List || row.rowType.typeBw === rowTypes.Deliverable) {
                          <app-list-view
                            style="width: 100%"
                            [row]="row"
                            [instance]="instance"
                            [save$]="save$"
                            [reload$]="reload$"
                            [instanceComponentId]="instanceSectionComponentId"
                            [searchFilter]="searchFilter"
                            [isEducator]="isEducator"
                            [hasAdminAccess]="hasAdminAccess"
                            [hideAddButton]="hideAddButton"
                            [readingMode]="readingMode"
                            [entityId]="getEntityId()"
                            [routeParams]="routeParams"
                            (selectedChanged)="setSelected($event)"
                            (contentRemoved)="rowContentRemoved()"
                            (deliverableAddClickedOut)="addRowToClassroom()">
                          </app-list-view>
                          <!-- GRID VIEW -->
                        } @else {
                          <app-grid-view
                            style="width: 100%"
                            [instance]="instance"
                            [row]="row"
                            [save$]="save$"
                            [reload$]="reload$"
                            [searchFilter]="searchFilter"
                            [readingMode]="readingMode"
                            [instanceComponentId]="instanceSectionComponentId"
                            [entityId]="getEntityId()"
                            [builderView]="builderView"
                            [isEducator]="isEducator"
                            [hasAdminAccess]="hasAdminAccess"
                            [hideAddButton]="hideAddButton"
                            [routeParams]="routeParams"
                            (afterSave)="tabSaved('CONTENT')"
                            (changed)="tabChanged('CONTENT')"
                            (selectedChanged)="setSelected($event)"
                            (isUpdateCustomRowContent)="addEditCustomRowContent($event)"
                            (gridContentRemoved)="rowContentRemoved()">
                          </app-grid-view>
                        }
                      </ng-template>
                    </mat-tab>

                    <mat-tab label="{{ 'SETTINGS' | translate | async }}">
                      <ng-template matTabContent>
                        <app-row-settings
                          style="width: 100%"
                          [row]="row"
                          [instanceId]="instance?.id"
                          [save$]="save$"
                          (formState)="formState($event)"
                          (afterSave)="tabSaved('SETTINGS')"
                          (changed)="tabChanged('SETTINGS')"></app-row-settings>
                      </ng-template>
                    </mat-tab>

                    <!-- NOT ModifiablePackage / AccreditedPackage / Deliverable -->
                    @if (row.rowType.typeBw !== rowTypes.ModifiablePackage && row.rowType.typeBw !== rowTypes.AccreditedPackage && row.rowType.typeBw !== rowTypes.Deliverable) {
                      <mat-tab label="{{ 'STYLING' | translate | async }}">
                        <ng-template matTabContent>
                          <app-row-styling style="width: 100%" [row]="row" [save$]="save$" (afterSave)="tabSaved('STYLING')" (changed)="tabChanged('STYLING')"></app-row-styling>
                        </ng-template>
                      </mat-tab>
                    }
                    <!-- DYNAMIC -->
                    @if (row.rowType.typeBw === rowTypes.Dynamic) {
                      <mat-tab label="{{ 'TAGGING' | translate | async }}">
                        <ng-template matTabContent>
                          <app-row-tagging style="width: 100%" [row]="row" [save$]="save$" (afterSave)="tabSaved('TAGGING')" (changed)="tabChanged('TAGGING')"></app-row-tagging>
                        </ng-template>
                      </mat-tab>
                    }
                    <!-- ModifiablePackage -->
                    @if (row.rowType.typeBw === rowTypes.ModifiablePackage) {
                      <mat-tab label="{{ 'GRADES' | translate | async }}">
                        <ng-template matTabContent>
                          <app-row-grades
                            [row]="row"
                            [instance]="instance"
                            [type]="row.rowType.name"
                            style="width: 100%"
                            (afterSave)="tabSaved('GRADES')"
                            (changed)="tabChanged('GRADES')"></app-row-grades>
                        </ng-template>
                      </mat-tab>
                    }
                  </mat-tab-group>
                }
              }
            </ion-col>
          </ion-row>
        </ion-grid>
      </div>
    }
  </div>
}
