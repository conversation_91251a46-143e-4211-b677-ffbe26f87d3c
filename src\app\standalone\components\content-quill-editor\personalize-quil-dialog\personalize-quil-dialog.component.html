<div class="layout">
  <div>
    <h3>{{ 'Insert Personalization Token' | translate | async }}</h3>
    <ion-label position="stacked" class="reqAsterisk">
      {{ 'Select a property' | translate | async }}
      <span title="{{ 'Select a property' | translate | async }}" class="reqAsterisk">
        <span>* </span>
        <ion-icon name="information-circle-outline"></ion-icon>
      </span>
    </ion-label>
    <select matNativeControl class="dropdown" (change)="optionSelect()">
      @for (item of personalizationOptions; track item) {
        <option [value]="item">
          {{ item | translate | async }}
        </option>
      }
    </select>
    <div class="searchbar">
      <div class="bar">
        <input type="text" #searchbar [(ngModel)]="searchInput" class="dropdown" (keyup)="fetchSeries($event)" placeholder="{{ 'Search here...' | translate | async }}" />
      </div>
      <select matNativeControl class="dropdown" (change)="optionSelect()">
        @for (item of searchResult; track item) {
          <option [value]="item">
            {{ item.name | translate | async }}
          </option>
        }
      </select>
    </div>
    <ion-button (click)="onClose(true)" style="width: 100%">{{ 'Add' | translate | async }}</ion-button>
  </div>
  <div mat-dialog-actions [align]="'end'">
    <ion-button (click)="onClose(true)">{{ 'Save changes' | translate | async }}</ion-button>
    <ion-button color="medium" (click)="onClose()">{{ 'Cancel' | translate | async }}</ion-button>
  </div>
</div>
