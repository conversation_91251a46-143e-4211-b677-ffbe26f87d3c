<div class="parent-container">
  <ion-item class="inner-container" [ngClass]="{ 'no-padding': noPadding, 'no-border': noBorder, 'side-panel-input-padding': sidePanelPadding }">
    @if (showSelected !== true) {
      <ion-label position="stacked">
        {{ label | translate | async }}
        <span class="reqAsterisk">
          @if (required) {
            <span>{{ '* ' }}</span>
          }
          <ion-icon name="information-circle-outline" matTooltip="{{ toolTip | translate | async }}"></ion-icon>
        </span>
        @if (identifierText) {
          <span class="identifier">{{ identifierText | translate | async }}</span>
        }
      </ion-label>
    }
    <ion-textarea style="--background:{{ backgroundColor }};" (keyup)="setValue($event)" [placeholder]="placeHolder | translate | async" [disabled]="disabled" [value]="textValue"></ion-textarea>
    @if (errorMessage) {
      <ion-text>
        {{ errorMessage | translate | async }}
      </ion-text>
    }
  </ion-item>
</div>
