<ion-grid class="parent-container">
  @if (instanceInterest) {
    <ng-container>
      <ion-row class="full-parent-height">
        <div class="text-main-container">
          <div>
            <app-heading-value [inheritedPropertyValue]="instanceInterest.heading" [fontSize]="22" [defaultStylingDirection]="'Center'"></app-heading-value>
            <app-dynamic-text-value [instanceId]="instanceId" [inheritedPropertyValue]="instanceInterest.description"></app-dynamic-text-value>
          </div>
        </div>
      </ion-row>
      <div class="icon-container">
        <button (click)="setSelectedIcon(instanceInterestTypes.dislike)" class="icon">
          @if (selectedIcon === instanceInterestTypes.dislike) {
            <mat-icon svgIcon="Dislike_Icon_Selected"></mat-icon>
          } @else {
            <mat-icon svgIcon="Dislike_Icon_Default"></mat-icon>
          }
          <div class="text">
            <span>{{ instanceInterest.interestNoneText | translate | async }}</span>
          </div>
        </button>
        <button (click)="setSelectedIcon(instanceInterestTypes.like)" class="icon">
          @if (selectedIcon === instanceInterestTypes.like) {
            <mat-icon svgIcon="Like_Icon_Selected"></mat-icon>
          } @else {
            <mat-icon svgIcon="Like_Icon_Default"></mat-icon>
          }
          <div class="text">
            <span>{{ instanceInterest.interestMediumText | translate | async }}</span>
          </div>
        </button>
        <button (click)="setSelectedIcon(instanceInterestTypes.love)" class="icon">
          @if (selectedIcon === instanceInterestTypes.love) {
            <mat-icon svgIcon="Love_Icon_Selected"></mat-icon>
          } @else {
            <mat-icon svgIcon="Love_Icon_Default"></mat-icon>
          }
          <div class="text">
            <span>{{ instanceInterest.interestHighText | translate | async }}</span>
          </div>
        </button>
      </div>
    </ng-container>
  }
</ion-grid>
