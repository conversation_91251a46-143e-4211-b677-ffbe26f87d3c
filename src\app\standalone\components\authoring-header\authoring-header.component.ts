import { Component, Input } from '@angular/core';
import { IonicModule } from '@ionic/angular';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-authoring-header',
    templateUrl: './authoring-header.component.html',
    styleUrls: ['./authoring-header.component.scss'],
    imports: [IonicModule, AsyncPipe, TranslatePipe]
})
export class AuthoringHeaderComponent {
  @Input() componentName: string;
  constructor() {}
}
