import { Component, forwardRef, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, FormsModule, NG_VALIDATORS, NG_VALUE_ACCESSOR, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatSlideToggle } from '@angular/material/slide-toggle';
import { IComponent, IHeading } from '@app/core/contracts/contract';
import { IonicModule } from '@ionic/angular';
import { Subject, takeUntil, tap } from 'rxjs';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { TextAreaInputControlComponent } from '@app/standalone/components/text-area-input-control/text-area-input-control.component';

import { ComponentUpdateSignalService } from '@app/core/services/component-update-signals.service';
import { Alignment<PERSON>ontrolComponent } from '@app/standalone/components/alignment-control/alignment-control.component';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-heading-control',
    templateUrl: './heading-control.component.html',
    styleUrls: ['./heading-control.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => HeadingControlComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => HeadingControlComponent),
        },
    ],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, TextInputControlComponent, AlignmentControlComponent, TextAreaInputControlComponent, MatSlideToggle, AsyncPipe, TranslatePipe]
})
export class HeadingControlComponent extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() override label: string;
  @Input() description: string;
  @Input() component!: IComponent;

  options = [
    { value: 'h1', label: 'Heading 1' },
    { value: 'h2', label: 'Heading 2' },
    { value: 'h3', label: 'Heading 3' },
    { value: 'h4', label: 'Heading 4' },
    { value: 'h5', label: 'Heading 5' },
    { value: 'h6', label: 'Heading 6' },
    { value: '', label: 'Default Style' },
  ];
  headingModal: IHeading = { text: '', description: '' };
  headingForm: FormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private formBuilder: FormBuilder,
    private signalService: ComponentUpdateSignalService
  ) {
    super();
  }

  ngOnInit() {   
    this.createForm(); 
    this.initModal();
    this.fieldValueChanged
      .pipe(
        takeUntil(this.componentDestroyed$),
        tap(() => {
          this.initModal();
          this.updateForm();
        })
      )
      .subscribe();
  }
 
  private createForm() {
    this.headingForm = this.formBuilder.group({
      text: [this.headingModal.text ?? null, Validators.required],
      description: [this.headingModal.description ?? null],
      stylingDirection: [this.component.templateField?.stylingDirection ?? 'Left', Validators.required],
      darkText: [this.component.templateField?.darkText ?? false],
      headingStyle: [this.component.templateField?.headingStyle ?? ''],
    });

    this.subscribeToFormChanges();
  }

  private initModal() {    
    if (this.textValue) {
      try {
        this.headingModal = JSON.parse(this.textValue) as IHeading;
      } catch (error) {
        this.headingModal = {
          text: this.textValue,
          description: '',
        };
      }
    }
  }

  onHeadingStyleChanged(event: any) {
    this.headingForm.patchValue({
      headingStyle: event,
    },
      { emitEvent: false }
    );
    this.setValue();
  }
  
  private updateForm() {
    this.headingForm.patchValue(
      {
        text: this.headingModal.text,
        description: this.headingModal.description,
      },
      { emitEvent: false }
    );
  }

  private subscribeToFormChanges() {
    this.headingForm.valueChanges
      .pipe(
        takeUntil(this.componentDestroyed$),
        tap(() => this.setValue())
      )
      .subscribe();
  }

  override setValue() {
    if (this.headingForm.valid && this.component.templateField) {
      const { stylingDirection, darkText, text, description, headingStyle } = this.headingForm.value;
      Object.assign(this.component.templateField, { stylingDirection, darkText, headingStyle });
      //Call this last after all values have been set to triggers the Save
      this.forceWriteValue(JSON.stringify({ text, description }));

      //Update the Change detection on the Signal
      var changedValue = JSON.stringify({ text, description, stylingDirection, darkText, headingStyle });
      if(this.component.id)
      {
        this.signalService.triggerSignal({ componentId: this.component.id, updateValue: changedValue });
      }
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
