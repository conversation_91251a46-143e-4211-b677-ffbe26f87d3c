import { Async<PERSON>ip<PERSON>, Ng<PERSON>lass } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { IInstance } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { ParseService } from '@app/core/services/parse-service';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { ParseContentPipe } from '@app/shared/pipes/parse-content';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { HeaderImageBaseComponent } from '@app/standalone/components/header-image-base/header-image-base.component';
import { IonicModule } from '@ionic/angular';

@Component({
    selector: 'app-instance-card',
    imports: [IonicModule, AsyncPipe, ParseContentPipe, NgClass, TranslatePipe],
    providers: [BuilderService, ParseService, DataService, SystemPropertiesService],
    templateUrl: './instance-card.component.html',
    styleUrl: './instance-card.component.scss'
})
export class InstanceCardComponent extends HeaderImageBaseComponent implements OnInit {
  @Output() instanceClicked = new EventEmitter<IInstance>();
  @Output() keyValClicked = new EventEmitter<KeyValue>();
  @Input() isDropdown = false;
  @Input() hideIcon = false;
  @Input() isModalOpen = false;
  @Input() selected = false;
  @Input() keyVal?: KeyValue;

  constructor(builderService: BuilderService, parseService: ParseService, dataService: DataService, systemPropertiesService: SystemPropertiesService) {
    super(parseService, builderService, dataService, systemPropertiesService);
    this.derivedComponentType = 'instance-card';
  }

  ngOnInit(): void {
    this.setIconAndCoverUrl();
  }

  clicked() {
    if (this.instance) {
      this.instanceClicked.next(this.instance);
    } else if (this.keyVal) {
      this.keyValClicked.next(this.keyVal);
    }
  }
}
