import { animate, AnimationMetadata, state, style, transition, trigger } from '@angular/animations';
import { Async<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { Component, Input, OnChanges, OnDestroy, OnInit, computed, signal, SimpleChanges, TemplateRef, Output, EventEmitter, input } from '@angular/core';
import { Router } from '@angular/router';
import { IFeature, IFeatureTab, IRouteParams } from '@app/core/contracts/contract';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { ViewType } from '@app/core/enums/view-type';
import { AuthService } from '@app/core/services/auth-service';
import { BreadcrumbService } from '@app/core/services/breadcrumb-service';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { ParseService } from '@app/core/services/parse-service';
import { RolesService } from '@app/core/services/roles.service';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { BuilderService } from '@app/features/feature-repository-builder/services/builder-service';
import { ParseContentPipe } from '@app/shared/pipes/parse-content';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { IonicModule, ViewDidEnter } from '@ionic/angular';
import { MatIconModule } from '@angular/material/icon';
import { first, Subject, takeUntil } from 'rxjs';
import { BreadcrumbsComponent } from '../breadcrumbs/breadcrumbs.component';
import { HeaderImageBaseComponent } from '../header-image-base/header-image-base.component';
import { JoinCodeComponent } from '../join-code/join-code.component';
import { ModalController } from '@ionic/angular/standalone';
import { AddToDialogComponent } from '@app/standalone/modals/add-to-dialog/add-to-dialog.component';
import { AddToDialogActionTypes } from '@app/core/enums/add-to-dialog-action-types';
import { environment } from '@env/environment';
import { MetaTagService } from '@app/core/services/meta-tag.service';

@Component({
    selector: 'app-page-header',
    templateUrl: './page-header.component.html',
    styleUrls: ['./page-header.component.scss'],
    animations: [
        trigger('fade', [
            state('false', style({
                height: 'fit-content',
            })),
            state('true', style({
                minHeight: '75px;',
                height: 'fit-content',
            })),
            transition('* => false', [animate('0.2s')]),
            transition('* => true', [animate('0.2s')]),
        ] as AnimationMetadata[]),
    ],
    imports: [NgClass, NgStyle, BreadcrumbsComponent, IonicModule, JoinCodeComponent, NgTemplateOutlet, AsyncPipe, ParseContentPipe, MatIconModule, TranslatePipe],
    providers: [ModalController]
})
export class PageHeaderComponent extends HeaderImageBaseComponent implements OnInit, OnChanges, OnDestroy, ViewDidEnter {
  @Input() content: TemplateRef<any>;
  @Input() scrollPosition: number;
  @Input() set viewType(value: number) {
    this.viewTypeSignal.set(value);
  }
  get viewType(): number {
    return this.viewTypeSignal();
  }
  viewTypeSignal = signal<number>(0);
  @Input() onlyContent = false;
  @Input() routeParams: IRouteParams;
  @Input() featureTab: IFeatureTab;
  @Input() featureTabs: IFeatureTab[];
  @Input() isCurrentFirstLevelView: boolean;
  @Input() selectedIndex = signal(0);
  @Input() liked: boolean;
  @Input() share: () => void;
  @Output() like: EventEmitter<boolean> = new EventEmitter<boolean>();
  minimized = false;
  refresh = new Subject<any>();
  timeLeft = 1;
  interval: any;
  scrollHeight = 0;
  iconId: string | null;
  viewTypes = ViewType;
  isScorm = false;
  showShareBtn = computed(() => {
    if (this.viewTypeSignal() === ViewType.Player || this.viewTypeSignal() === ViewType.Builder) {
      return false;
    }
    return environment.showShareBtn;
  });
  showLikeBtn = computed(() => {
    return this.routeParams?.featureSlug === 'my-organization';
  });
  showEditBtn = signal(false);
  isFirstLevelPage = false;

  constructor(
    builderService: BuilderService,
    parseService: ParseService,
    dataService: DataService,
    systemPropertiesService: SystemPropertiesService,
    private modalController: ModalController,
    public authService: AuthService,
    private rolesService: RolesService,
    private instanceService: InstanceService,
    public breadcrumbService: BreadcrumbService,
    private router: Router,
    private metaTagService: MetaTagService,
    private parseContentPipe: ParseContentPipe
  ) {
    super(parseService, builderService, dataService, systemPropertiesService);
  }

  get isGridView(): boolean {
    return this.viewTypeSignal() === ViewType.Grid;
  }

  get isPlayerView(): boolean {
    return this.viewTypeSignal() === ViewType.Player;
  }

  ngOnInit() {
    this.isFirstLevelPage = this.breadcrumbService.firstLevelViewSlugs.includes(this.routeParams.featureSlug ?? '');
    this.isScorm = this.router.url.indexOf('scorm') !== -1;

    if (this.routeParams?.viewType !== undefined) {
      this.viewTypeSignal.set(this.routeParams.viewType);
    }

    this.updateEditButtonVisibility();

    this.setIconAndCoverUrl$.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      if (this.instance && this.instance.status != 'private') {
        this.parseContentPipe
          .transform(this.instance.feature.featureType.name.toLowerCase() === 'internal' ? (this.instance.feature.description ?? '') : (this.instance.description ?? ''), this.instance.id, null, true)
          .pipe(first())
          .subscribe((result: string) => {
            if (result) {
              this.metaTagService.updateMetaTags(this.instance.title, result, this.iconUrl);
            }
          });
      }
    });

    this.setIconAndCoverUrl();

    if (this.viewTypeSignal() === ViewType.Player) {
      const scrollPosition = document.getElementById('ionContent')?.scrollTop || 0;
      this.minimized = scrollPosition > 0;
    } else {
      this.minimized = false;
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['routeParams'] || changes['viewType']) {
      if (changes['routeParams'] && this.routeParams?.viewType !== undefined) {
        this.viewTypeSignal.set(this.routeParams.viewType);
      }

      if (this.viewTypeSignal() !== ViewType.Player) {
        this.minimized = false;
      }

      this.isFirstLevelPage = this.breadcrumbService.firstLevelViewSlugs.includes(this.routeParams.featureSlug ?? '');
      this.updateEditButtonVisibility();
    }

    if (changes['featureTab']) {
      this.updateEditButtonVisibility();
    }
  }

  ionViewDidEnter(): void {
    this.isFirstLevelPage = this.breadcrumbService.firstLevelViewSlugs.includes(this.routeParams.featureSlug ?? '');

    if (this.routeParams?.viewType !== undefined) {
      this.viewTypeSignal.set(this.routeParams.viewType);
    }

    this.updateEditButtonVisibility();
  }

  /**
   * Updates the edit button visibility based on current state
   */
  private updateEditButtonVisibility() {
    // Default to hiding the button
    let shouldShow = false;

    try {
      if (this.instance) {
        // Check for user access permissions
        const hasAccess = this.hasEditAccess() || this.hasAdminAccess();

        let featureTabCondition = false;
        if (this.featureTab) {
          featureTabCondition =
            (this.featureTab.type?.name === 'Instance Builder' && this.viewTypeSignal() !== this.viewTypes.Builder) ||
            (this.featureTab.tab?.name === 'ASSIGNMENTS' && this.viewTypeSignal() !== this.viewTypes.Builder);
        }

        const instanceCondition =
          this.instance.feature?.featureType?.name === 'Accredited Learning Container Pages' || this.instance.feature?.featureType?.name === 'Modifiable Learning Container Pages';

        const isNotDefault = this.instance.isDefault !== true;

        shouldShow = hasAccess && (featureTabCondition || (instanceCondition && isNotDefault));
      }
    } catch (error) {
      shouldShow = false;
    }

    // Update the signal
    this.showEditBtn.set(shouldShow);
  }

  hasAdminAccess() {
    return this.rolesService.hasFeatureRoleAccess([ActionTypes.Manage, ActionTypes.Publish]);
  }

  hasEditAccess() {
    if (!this.featureTab || !this.featureTab.featureTabEditActions) {
      return this.rolesService.hasFeatureRoleAccess([ActionTypes.Publish, ActionTypes.Assign, ActionTypes.Manage]);
    }

    return this.featureTab.featureTabEditActions.length === 0
      ? this.rolesService.hasFeatureRoleAccess([ActionTypes.Publish, ActionTypes.Assign, ActionTypes.Manage])
      : this.rolesService.hasFeatureRoleAccess(this.featureTab.featureTabEditActions.map(x => x.actionBw));
  }

  async openBuilder(instanceId: string) {
    if (
      (this.instance.feature.featureType.name === 'Accredited Learning Container Pages' || this.instance.feature.featureType.name === 'Modifiable Learning Container Pages') &&
      this.viewTypeSignal() !== this.viewTypes.Player
    ) {
      const modal = await this.modalController.create({
        component: AddToDialogComponent,
        componentProps: {
          selectedInstance: this.instance,
          instanceId: this.instance.id,
          featureTypeName: this.instance?.feature?.featureType?.name,
          actionType: this.instance.feature.featureType.name === 'Accredited Learning Container Pages' ? AddToDialogActionTypes.EditAssignment : AddToDialogActionTypes.EditClass,
          reload: true,
        },
        cssClass: 'my-instances-dialog',
      });

      modal.onDidDismiss().then(value => {
        if (value.data === true) {
          this.dataService.reload$.next(null);
        }
      });

      await modal.present();
    } else {
      this.instanceService.openInstance('instance', instanceId, null, 'builder', null, true);
    }
  }

  async onWindowScroll(event: any) {
    const scrollTop = event.detail.scrollTop;
    if (scrollTop > 0 && this.viewTypeSignal() !== ViewType.Player) {
      this.minimized = true;
    } else {
      this.minimized = false;
    }
  }

  tabChanged(index: number) {
    this.selectedIndex.set(index);
  }

  onShare() {
    if (this.share) {
      this.share();
    }
  }

  async onLike() {
    this.like.next(!this.liked);
  }
}
