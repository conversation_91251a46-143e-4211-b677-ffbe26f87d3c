import { <PERSON><PERSON><PERSON>, ENTER } from '@angular/cdk/keycodes';
import { Component, Input, OnDestroy, OnInit, forwardRef } from '@angular/core';
import { FormControl, FormGroup, NG_VALIDATORS, NG_VALUE_ACCESSOR, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatChipInputEvent, MatChipGrid, MatChipRow, MatChipRemove, MatChipInput } from '@angular/material/chips';
import { IGuestContext, ILinkedUserEmails, IUserContext } from '@app/core/contracts/contract';
import { AuthService } from '@app/core/services/auth-service';
import { DataService } from '@app/core/services/data-service';
import { Subject, takeUntil } from 'rxjs';
import { NgClass, AsyncPipe } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { MatIcon } from '@angular/material/icon';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-email-chip',
    templateUrl: './email-chip.component.html',
    styleUrls: ['./email-chip.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => EmailChipComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => EmailChipComponent),
        },
    ],
    imports: [NgClass, IonicModule, MatChipGrid, MatChipRow, MatChipRemove, MatIcon, FormsModule, ReactiveFormsModule, MatChipInput, AsyncPipe, TranslatePipe]
})
export class EmailChipComponent extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() override disabled = false;
  @Input() noContainer = false;
  @Input() sidePanelPadding = false;
  componentDestroyed$: Subject<boolean> = new Subject();
  emailForm: FormGroup;
  email: FormControl;
  addOnBlur = true;
  listItems: ILinkedUserEmails[];
  readonly separatorKeysCodes = [ENTER, COMMA] as const;

  constructor(
    private dataService: DataService,
    private authService: AuthService
  ) {
    super();
  }

  get userLocation(): IUserContext {
    return { ...this.authService.userContext } as IUserContext;
  }

  get guest(): IGuestContext {
    return { ...this.authService.guestUserContext } as IGuestContext;
  }

  ngOnInit() {
    this.createFormControls();
    this.dataService
      .getUserLinkedEmails()
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe((linkedEmails: any) => {
        this.listItems = linkedEmails;
      });
  }

  createFormControls() {
    this.emailForm = new FormGroup({
      email: new FormControl('', [Validators.required, Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+.[a-z]{2,4}$')]),
    });
  }

  emitRemovedSelected(selected: any) {
    const index = this.listItems.indexOf(selected);

    if (index >= 0) {
      this.listItems.splice(index, 1);
      this.dataService
        .removeLinkedEmail(selected.email, this.userLocation.userTrackingId)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {});
    }
  }

  addLinkedEmail(event: MatChipInputEvent): void {
    const value = (event.value || '').trim();
    if (value && this.emailForm.valid) {
      this.listItems.push({ email: value, userId: null, userTrackingId: null });
      this.dataService
        .addLinkedEmail(value, this.userLocation.country, this.userLocation.userTrackingId)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(result => {
          if (result == null) {
            const index = this.listItems.findIndex(x => x.email === value);
            if (index >= 0) {
              this.listItems.splice(index, 1);
            }
          }
        });
    } else {
      this.emailForm.reset();
    }
    event.chipInput.clear();
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
