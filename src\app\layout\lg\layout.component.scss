:host {
  display: flex;
  flex-flow: column;
  height: 100%;
  width: 100%;
  z-index: 1;

  .top-nav {
    z-index: 100;
    position: relative;
    display: block;
    background: url('/assets/images/top100H.png') no-repeat scroll center center / cover transparent;
    color: black;
    width: 100%;
    height: 90px;
    flex-basis: 90px;

    ion-searchbar {
      --font-family: Roboto;
      font-weight: 900;
      --color: #aaa;
      letter-spacing: 0.01em;
      line-height: 1.1;
      --background: #1c1c1c;
      --border-radius: 4px;
      --box-shadow: none;
      padding: 0;
    }

    .button {
      cursor: pointer;
      font-family: Roboto;
      font-weight: 600;
      font-size: 1.125em;
      color: #fff;
      background-color: #3d2e00;
      letter-spacing: 0.01em;
      line-height: 1.1;
      border-radius: 3px;
      padding-left: 15px;
      padding-right: 15px;
    }

    .button:hover {
      background-color: black;
    }

    @media (min-width: 1501px) {
      .logo {
        cursor: pointer;
        position: absolute;
        left: 20px;
        top: 5px;
        width: 300px;
        line-height: 80px;
      }

      .search-bar-and-join-container {
        position: absolute;
        display: flex;
        width: 420px;
        left: 365px;
        padding-top: 20px;

        ion-searchbar {
          font-size: 1em;
        }

        .button {
          padding-top: 12px;
          height: 100%;
          margin-left: 15px;
        }

        .search-bar {
          width: 300px;
          box-shadow: none;

          ion-searchbar {
            font-size: 1em;
          }
        }
      }

      .controls {
        position: absolute;
        display: flex;
        right: 20px;
        top: 0px;
        max-width: 500px;
        min-width: 100px;
        height: 80px;

        .control-persona {
          padding-top: 20px;

          .top-bar-person-name {
            font-family: Roboto;
            font-size: 1em;
            font-weight: 700;
            color: #000;
            letter-spacing: 0.01em;
            line-height: 1.1;
          }

          .top-bar-org-name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: Roboto;
            color: #536471;
            font-style: italic;
            font-size: 1em;
            font-weight: 300;
            width: 150px;
          }
        }

        .control-buttons {
          display: flex;

          .control-button {
            padding-right: 15px;
          }

          .notification-button {
            margin-top: 20px;
            width: 36px;
            height: 36px;
            color: black;
            cursor: pointer;
          }

          .settings-button {
            cursor: pointer;
            margin-top: 20px;
            width: 36px;
            height: 36px;
            color: black;
          }

          .button {
            padding-top: 12px;
            padding-bottom: 9px;
            margin-top: 17px;
            margin-left: 20px;
            margin-bottom: 20px;
          }
        }
      }
    }

    @media (min-width: 1281px) and (max-width: 1500px) {
      .logo {
        cursor: pointer;
        position: absolute;
        left: 20px;
        top: 5px;
        width: 270px;
      }

      .search-bar-and-join-container {
        position: absolute;
        display: flex;
        width: 420px;
        left: 335px;
        padding-top: 18px;

        .button {
          padding-top: 12px;
          height: 100%;
          margin-left: 15px;
        }

        .search-bar {
          width: 300px;
          box-shadow: none;
        }
      }

      .controls {
        position: absolute;
        display: flex;
        right: 20px;
        top: 0px;
        max-width: 500px;
        min-width: 100px;
        height: 80px;

        .control-persona {
          padding-top: 20px;

          .top-bar-person-name {
            font-family: Roboto;
            font-size: 1em;
            font-weight: 700;
            color: #000;
            letter-spacing: 0.01em;
            line-height: 1.1;
          }

          .top-bar-org-name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: Roboto;
            color: #536471;
            font-style: italic;
            font-size: 1em;
            font-weight: 300;
            width: 150px;
          }
        }

        .control-buttons {
          display: flex;

          .control-button {
            padding-right: 15px;
          }

          .notification-button {
            margin-top: 20px;
            width: 36px;
            height: 36px;
            color: black;
          }

          .settings-button {
            cursor: pointer;
            margin-top: 20px;
            width: 36px;
            height: 36px;
            color: black;
          }

          .button {
            padding-top: 12px;
            padding-bottom: 9px;
            margin-left: 20px;
            margin-top: 17px;
            margin-bottom: 20px;
          }
        }
      }
    }

    @media (min-width: 993px) and (max-width: 1280px) {
      .logo {
        cursor: pointer;
        position: absolute;
        left: 15px;
        top: 15px;
        width: 225px;
        line-height: 80px;
      }

      .search-bar-and-join-container {
        position: absolute;
        display: flex;
        width: 380px;
        left: 270px;
        padding-top: 19px;

        .button {
          padding-top: 12px;
          height: 100%;
          margin-left: 15px;
        }

        .search-bar {
          width: 270px;
          box-shadow: none;
        }
      }

      .controls {
        position: absolute;
        display: flex;
        right: 20px;
        top: 0px;
        max-width: 450px;
        min-width: 100px;
        height: 80px;

        .control-persona {
          padding-top: 20px;

          .top-bar-person-name {
            font-family: Roboto;
            font-size: 1em;
            font-weight: 700;
            color: #000;
            letter-spacing: 0.01em;
            line-height: 1.1;
          }

          .top-bar-org-name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: Roboto;
            color: #536471;
            font-style: italic;
            font-size: 1em;
            font-weight: 300;
            width: 150px;
          }
        }

        .control-buttons {
          display: flex;

          .control-button {
            padding-right: 15px;
          }

          .notification-button {
            margin-top: 25px;
            width: 28px;
            height: 28px;
            color: black;
          }

          .settings-button {
            cursor: pointer;
            margin-top: 25px;
            width: 28px;
            height: 28px;
            color: black;
          }

          .button {
            padding-top: 12px;
            padding-bottom: 9px;
            margin-left: 20px;
            margin-top: 17px;
            margin-bottom: 20px;
          }
        }
      }
    }

    @media (min-width: 769px) and (max-width: 993px) {
      .logo {
        cursor: pointer;
        position: absolute;
        left: 15px;
        top: 15px;
        width: 225px;
        line-height: 80px;
      }

      .search-bar-and-join-container {
        position: absolute;
        display: flex;
        width: 380px;
        left: 270px;
        padding-top: 20px;

        .button {
          padding-top: 12px;
          height: 100%;
          margin-left: 15px;
        }

        .search-bar {
          width: 270px;
          box-shadow: none;
        }
      }

      .controls {
        position: absolute;
        display: flex;
        right: 20px;
        top: 0px;
        max-width: 450px;
        min-width: 100px;
        height: 80px;

        .control-persona {
          padding-top: 20px;

          .top-bar-person-name {
            font-family: Roboto;
            font-size: 1em;
            font-weight: 700;
            color: #000;
            letter-spacing: 0.01em;
            line-height: 1.1;
          }

          .top-bar-org-name {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: Roboto;
            color: #536471;
            font-style: italic;
            font-size: 1em;
            font-weight: 300;
            width: 150px;
          }
        }

        .control-buttons {
          display: flex;

          .control-button {
            padding-right: 15px;
          }

          .notification-button {
            margin-top: 25px;
            width: 28px;
            height: 28px;
            color: black;
          }

          .settings-button {
            cursor: pointer;
            margin-top: 25px;
            width: 28px;
            height: 28px;
            color: black;
          }

          .button {
            padding-top: 12px;
            padding-bottom: 9px;
            margin-left: 20px;
            margin-top: 17px;
            margin-bottom: 20px;
          }
        }
      }
    }
  }

  .side-panel {
    background-color: black;
    padding-top: 16px;
    min-width: 100px !important;
    max-width: 100px !important;

    .menu-item {
      margin: 8px 0 16px;
      text-align: center;
      color: #fff;
      cursor: pointer;
      font-size: 16px;

      p {
        margin: 0;
        opacity: 0.65;
      }

      mat-icon {
        height: 50px;
        width: 50px;
        opacity: 0.65;
      }
    }

    .menu-item:hover {
      color: #f99e00;
    }

    .active {
      color: #f99e00;

      p {
        opacity: 1;
      }

      mat-icon {
        opacity: 1;
      }
    }

    .help-icon {
      position: absolute;
      bottom: 100px;
      width: 100%;
      text-decoration: none;
    }

    .translate-icon {
      position: absolute;
      bottom: 0px;
      width: 100%;
      text-decoration: none;
    }

    .language-selector {
      position: absolute;
      bottom: 50px;
      left: 100px;
      width: 120px;
      background-color: #333;
      border-radius: 4px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
      z-index: 1000;

      .language-option {
        padding: 10px;
        text-align: left;
        color: #fff;
        cursor: pointer;
        font-size: 14px;
        border-bottom: 1px solid #444;

        &:hover {
          background-color: #444;
          color: #f99e00;
        }

        &:last-child {
          border-bottom: none;
        }
      }
    }
  }

  .notification-menu {
    --min-height: 100%;
    --width: 450px;
    outline-style: none !important;
    background-color: transparent !important;
  }

  .item-background {
    --ion-item-background: transparent;
  }

  app-badge-notification {
    width: 95%;
  }
}

.no-notifications-popup {
  position: relative;
  width: 414px;
  border-radius: 10px;
  background-color: #171717;
  border: 1.5px solid #6565654d;

  .inner {
    margin-top: 37px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    p {
      font-family: Inter;
      font-size: 18px;
      font-weight: 400;
      color: #AAAAAA;
      letter-spacing: 0.03em;
      margin: 17px 0 25px 0;
    }

    img {
      width: 43px;
      height: 43px;
    }
  }
}

.close-icon {
  position: absolute;
  top: 15px;
  right: 15px;

  mat-icon {
    color: #fff;
    width: 22px;
    height: 22px;
  }
}
