<div class="top-nav">
  <img class="logo" routerLink="/splash" src="/assets/images/EFLogo_Black.png" />
  <div class="search-bar-and-join-container">
    <div class="search-bar">
      <ion-searchbar
        placeholder="{{ 'Site Search' | translate | async }}"
        showCancelButton="focus"
        cancelButtonText="{{ 'Cancel' | translate | async }}"
        debounce="1000"
        inputmode="text"
        #globalSearchText
        (ionChange)="setGlobalSearchValueLG(globalSearchText.value)"></ion-searchbar>
    </div>
    @if (authService.user) {
      <div class="control-buttons">
        @if (authService.user) {
          <div class="button" matTooltip="{{ 'Join Tooltip' | translate | async }}" [attr.aria-label]="'Join Tooltip' | translate | async" (click)="openJoin()">{{ 'Join' | translate | async }}</div>
        }
      </div>
    }
  </div>
  <div class="controls">
    @if (!authService.guestUserContext) {
      <div class="control-persona">
        <div>
          @if (authService.userContext) {
            <div class="top-bar-person-name">{{ authService?.userContext?.fullName }}</div>
          }
          @if (authService.user && authService.userContext) {
            <div class="top-bar-org-name">
              @for (o of organizations; track o; let last = $last) {
                <span>{{ o.name }}{{ !last ? ',' : '' }}</span>
              }
            </div>
          }
        </div>
      </div>
    }
    @if (authService.guestUserContext && authService.user) {
      <div class="control-persona">
        <div style="cursor: pointer">
          @if (authService.userContext) {
            <div class="top-bar-person-name">{{ authService?.userContext?.fullName }}</div>
          }
          <div class="top-bar-person-name" (click)="removeBrowsingAs()">{{ 'Browsing As' | translate | async }}</div>
          <div class="top-bar-org-name" (click)="removeBrowsingAs()">{{ authService?.guestUserContext?.browsingAs }}</div>
        </div>
      </div>
    }
    @if (authService.guestUserContext && !authService.user) {
      <div class="control-persona" style="right: 100px">
        <div style="cursor: pointer">
          <div class="top-bar-person-name">{{ 'Browsing As Guest' | translate | async }}</div>
        </div>
      </div>
    }
    @if (authService.user) {
      <div class="control-buttons">
        <div class="control-button">
          @if (!notificationService.checkNotRead()) {
            <mat-icon class="notification-button" svgIcon="notification" (click)="toggleNotifications()"></mat-icon>
          }
          @if (notificationService.checkNotRead()) {
            <mat-icon class="notification-button" svgIcon="notification-alert" (click)="toggleNotifications()"></mat-icon>
          }
        </div>
        <mat-icon class="settings-button" svgIcon="settings" (click)="presentSettingsPopover($event)"></mat-icon>
        @if (!authService.user) {
          <div class="button" (click)="openAuth()">{{ 'Login' | translate | async }}</div>
        }
      </div>
    }
    @if (!authService.user) {
      <div class="control-buttons">
        @if (!authService.user) {
          <div class="button" (click)="openAuth()">{{ 'Login' | translate | async }}</div>
        }
      </div>
    }
  </div>
</div>
<ion-grid style="position: relative; z-index: 2; margin-top: -16px; display: flex; flex-flow: column">
  <ion-row style="flex-grow: 1">
    <ion-col id="sidebarCol" class="side-panel" size="1">
      <div
        [routerLink]="['/my-journey']"
        routerLinkActive="active"
        (click)="!authService.user && authService.guestUserContext ? openLockedModalOnClick() : openFeature('my-journey')"
        [ngClass]="{ active: featureClicked === 'my-journey' }"
        class="menu-item">
        <mat-icon svgIcon="journey"></mat-icon>
        <p>{{ 'My Journey' | translate | async }}</p>
      </div>
      <div [routerLink]="['/library']" routerLinkActive="active" (click)="openFeature('library')" [ngClass]="{ active: featureClicked === 'library' }" class="menu-item">
        <mat-icon svgIcon="library"></mat-icon>
        <p>{{ 'Library' | translate | async }}</p>
      </div>
      <div
        [routerLink]="['/workspace']"
        routerLinkActive="active"
        (click)="!authService.user && authService.guestUserContext ? openLockedModalOnClick() : openFeature('workspace')"
        [ngClass]="{ active: featureClicked === 'workspace' }"
        class="menu-item">
        <mat-icon svgIcon="workspace"></mat-icon>
        <p>{{ 'Workspace' | translate | async }}</p>
      </div>
      @if (authService.userContext?.canManage && !authService.guestUserContext) {
        <div [routerLink]="['/admin']" (click)="selectedTab('admin')" routerLinkActive="active" [ngClass]="{ active: featureClicked === 'admin' }" class="menu-item">
          <mat-icon svgIcon="admin"></mat-icon>
          <p>{{ 'Admin' | translate | async }}</p>
        </div>
      }
      <a [routerLink]="['/help']" (click)="selectedTab('help')" routerLinkActive="active" class="help-icon menu-item">
        <mat-icon svgIcon="help"></mat-icon>
        <p>{{ 'Help' | translate | async }}</p>
      </a>
      <div class="menu-item translate-icon" (click)="toggleLanguageSelector($event)">
        <mat-icon svgIcon="translate"></mat-icon>
        <p>{{ 'Translate' | translate | async }}</p>
      </div>
      <div *ngIf="showLanguageSelector" class="language-selector">
        <div *ngFor="let lang of translateService.languages" class="language-option" (click)="selectLanguage(lang.code)">
          {{ lang.name }}
        </div>
      </div>
    </ion-col>
    <ion-col>
      <ion-menu class="notification-menu" side="end" menuId="notifications" contentId="main">
        <ion-list style="height: 100%; width: 500px; background-color: transparent">
          <ion-content id="notificationsList" [scrollEvents]="true" (ionScroll)="notificationListScrollEvent()" style="height: 100%; background-color: transparent">
            @for (notification of notificationService.getBellNotifications(); track notification) {
              <ion-item class="item-background">
                <app-badge-notification
                  [iconAssetId]="notification?.notificationIcon"
                  [messageText]="notification?.notificationText"
                  [hasRead]="notification?.hasRead"
                  (remove)="removeNotification(notification.id)">
                </app-badge-notification>
              </ion-item>
            }
            @if (notificationService.getBellNotifications()?.length === 0) {
              <ion-item class="item-background">
                <div class="no-notifications-popup">
                  <div class="close-icon">
                    <mat-icon style="cursor: pointer" svgIcon="close-solid" (click)="clearPopup()"></mat-icon>
                  </div>
                  <div class="inner">
                    <img src="https://ef-staging-f5esbhcphbhcg0fz.z03.azurefd.net/v1/asset/d0855d40-eebe-44ee-9178-fa3a4f0f853a/content" />
                    <p>{{ 'No notifications, you’re all caught up!' | translate | async }}</p>
                  </div>
                </div>
              </ion-item>
            }
          </ion-content>
        </ion-list>
      </ion-menu>
      <div class="outlet-container">
        <ion-router-outlet id="main"> </ion-router-outlet>
      </div>
    </ion-col>
  </ion-row>
</ion-grid>
