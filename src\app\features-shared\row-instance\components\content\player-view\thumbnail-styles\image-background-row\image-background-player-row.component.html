<div
  class="player-parent-container"
  [ngClass]="{
    'grading-view': selectedUserId && (content.isGraded !== false || content.containsGrading === false || content.status !== 'Completed'),
    'not-graded-view': selectedUserId && content.isGraded === false && content.containsGrading === true && content.status === 'Completed',
    'normal-view': !selectedUserId,
  }">
  <mat-expansion-panel #expansionPanel [expanded]="isExpanded()" [hideToggle]="!selectedUserId" disabled="true">
    <mat-expansion-panel-header
      [class.mat-expanded]="
        (content.actionBW === actionTypes.View && (selectedContentId === content.id || selectedContentId === content.slug) && selectedContentRowId === contentRowId) || isExpanded()
      "
      [ngClass]="{
        completed: content.status === 'Completed' && !(content.isGraded === false && content.status === 'Completed' && showGradingView === true && content.containsGrading === true),
        ungraded: content.isGraded === false && content.containsGrading === true && content.status === 'Completed',
      }">
      <ion-grid>
        <ion-row class="top-row-container items" (click)="route($event)">
          <ion-col class="header-container">
            <div class="img-col">
              <img
                class="img"
                [ngClass]="{
                  'img-not-graded': content.isGraded === false && content.status === 'Completed' && showGradingView === true && content.containsGrading === true,
                  'img-in-progress': content.status === 'InProgress',
                  'img-completed': content.status === 'Completed' && !(content.isGraded === false && content.status === 'Completed' && showGradingView === true && content.containsGrading === true),
                }"
                ngSrc="{{ iconUrl }}"
                fill="true"
                (error)="noImage()" />
            </div>
            <div class="heading-col">
              <div class="inner-container">
                <div class="heading">{{ content?.instanceName }}</div>
                @if (content?.instanceDescriptors) {
                  <div>
                    <div class="status-container">
                      @if (showGradingView && content.containsGrading === true) {
                        @if (content.status === 'Completed' && content.isGraded === true) {
                          <span class="instance-status complete">{{ 'GRADED' | translate | async }}</span>
                        } @else if (content.isGraded === false && content.containsGrading === true && content.status === 'Completed') {
                          <span class="instance-status incomplete">{{ 'UNGRADED' | translate | async }}</span>
                        } @else if (content.status === 'NotStarted') {
                          <span class="instance-status in-progress">{{ 'STUDENT NOT STARTED' | translate | async }}</span>
                        } @else if (content.status === 'InProgress') {
                          <span class="instance-status in-progress">{{ 'STUDENT IN PROGRESS' | translate | async }}</span>
                        }
                      } @else {
                        <span
                          [ngClass]="{
                            'inner-completed': content.status === 'Completed',
                            'inner-in-progress': content.status === 'InProgress',
                          }"
                          >{{ content.status === 'Completed' ? 'COMPLETED' : content.status === 'InProgress' ? 'IN PROGRESS' : ('' | translate | async) }}</span
                        >
                      }
                    </div>
                    <span class="intance-descripitor" [innerHTML]="content?.instanceDescriptors ?? '' | parsePipe: content?.id : null : false | async | translate | async"></span>
                  </div>
                }
              </div>
            </div>
            @if (content.actionBW === actionTypes.View) {
              <div>
                <mat-icon class="icon" svgIcon="locked-instance"></mat-icon>
              </div>
            }
          </ion-col>
        </ion-row>
      </ion-grid>
    </mat-expansion-panel-header>
    <ng-template matExpansionPanelContent>
      <div class="inner-block">
        @if (instanceSections) {
          <ion-grid>
            @for (instanceSection of instanceSections; track instanceSection; let i = $index) {
              <ng-container class="sub-menu">
                <!-- NOT GRADING -->
                @if (!selectedUserId && instanceSection?.section?.showOnPlayerSidepanel && instanceSection?.section?.title) {
                  <ion-row
                    class="item"
                    [ngClass]="{ focused: instanceSection.active === true, 'focused-completed': instanceSection.active && instanceSection.complete === true }"
                    [ngStyle]="{ background: instanceSection.complete === true || instanceSection.active === true ? '#555555' : '#3d3d3c' }"
                    (click)="scrollToSection(instanceSection.id)">
                    <ion-col class="start-col" size="12">
                      <div class="icon-container">
                        @if (instanceSection.active === false && instanceSection.complete === true) {
                          <mat-icon class="icon" svgIcon="complete-section"></mat-icon>
                        } @else if (instanceSection.active === true) {
                          <mat-icon class="icon" svgIcon="in-progress-section"></mat-icon>
                        } @else if (instanceSection.section.isCompletable === true && !instanceSection.complete && !instanceSection.active) {
                          <mat-icon class="icon" style="color: #cccccc" svgIcon="locked-instance"></mat-icon>
                        }
                      </div>
                      <div class="heading" [ngStyle]="{ color: instanceSection?.active === true ? '#fff' : '#cccccc' }">{{ instanceSection.section.title | translate | async }}</div>
                    </ion-col>
                  </ion-row>
                }
                <!-- GRADING -->
                @if (selectedUserId && instanceSection?.section?.showOnPlayerSidepanel && instanceSection?.section?.title) {
                  <ion-row
                    class="item"
                    [ngClass]="{
                      focused: instanceSection.active === true,
                      'complete-background': instanceSection.gradingComplete === true && content.status === 'Completed',
                      'incomplete-background': instanceSection.gradingComplete === false && instanceSection.containsGrading === true && content.status === 'Completed',
                    }"
                    (click)="scrollToSection(instanceSection.id)">
                    <ion-col class="start-col" size="12">
                      <div class="icon-container">
                        @if (instanceSection.active === false && instanceSection.complete === true) {
                          <mat-icon class="icon" svgIcon="complete-section"></mat-icon>
                        } @else if (instanceSection.active === true) {
                          <mat-icon class="icon" svgIcon="in-progress-section"></mat-icon>
                        } @else if (instanceSection.section.isCompletable === true && !instanceSection.complete && !instanceSection.active) {
                          <mat-icon class="icon" style="color: #cccccc" svgIcon="locked-instance"></mat-icon>
                        }
                      </div>
                      <div class="heading" [ngStyle]="{ color: instanceSection?.active === true ? '#fff' : '#cccccc' }">{{ instanceSection.section.title | translate | async }}</div>
                    </ion-col>
                    <div class="section-status-container">
                      @if (instanceSection.gradingComplete === true && instanceSection.containsGrading === true && content.status === 'Completed') {
                        <span class="section-status-small complete">{{ 'GRADED' | translate | async }}</span>
                      }
                      <!-- <span class="section-status-small in-progress">IN PROGRESS</span> -->
                      @if (instanceSection.gradingComplete === false && instanceSection.containsGrading === true && content.status === 'Completed') {
                        <span class="section-status-small incomplete">{{ 'UNGRADED' | translate | async }}</span>
                      }
                    </div>
                  </ion-row>
                }
              </ng-container>
            }
          </ion-grid>
        }
      </div>
    </ng-template>
  </mat-expansion-panel>
</div>
