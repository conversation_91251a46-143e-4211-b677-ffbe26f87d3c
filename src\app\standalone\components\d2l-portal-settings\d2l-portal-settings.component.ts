import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IOrganizationSsoAuth, IOrganizationSsoAuthIn, IOrganizationSsoRoleMapping, IOrganizationSsoRoleMappingIn, IProductSetting, IUserRole } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { LayoutService } from '@app/core/services/layout-service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { IonicModule } from '@ionic/angular';
import { MatSlideToggle } from '@angular/material/slide-toggle';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-d2l-portal-settings',
    templateUrl: './d2l-portal-settings.component.html',
    styleUrls: ['./d2l-portal-settings.component.scss'],
    imports: [IonicModule, FormsModule, ReactiveFormsModule, TextInputControlComponent, MatSlideToggle, AsyncPipe, TranslatePipe]
})
export class D2lPortalSettingsComponent implements OnInit, OnDestroy {
  @Input() organizationId: string;
  @Input() productSetting: IProductSetting;
  @Input() userRoles: IUserRole[] = [];
  @Output() orgSsoAuthChange: EventEmitter<IOrganizationSsoAuthIn> = new EventEmitter();
  orgSsoAuth: IOrganizationSsoAuth;
  orgSsoRoleMappingIn: IOrganizationSsoRoleMappingIn[] = [];
  orgSsoAuthGroupForm: UntypedFormGroup;
  dataLoaded = false;

  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private dataService: DataService,
    private formBuilder: UntypedFormBuilder,
    public layoutService: LayoutService
  ) {}

  ngOnInit() {
    if (this.organizationId != null) {
      this.getOrgSsoAuthById(this.organizationId);
    }
  }

  getOrgSsoAuthById(organizationId: string) {
    this.dataService
      .getOrgSsoAuthById(organizationId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(orgAuth => {
        this.dataLoaded = true;
        if (orgAuth != null) {
          this.orgSsoAuth = orgAuth;
        }

        this.createForm();
      });
  }

  createForm() {
    this.orgSsoAuthGroupForm = this.formBuilder.group({
      externalId: [this.orgSsoAuth?.externalId],
      externalAuthId: [this.orgSsoAuth?.externalAuthId],
      externalAuthSecret: [this.orgSsoAuth?.externalAuthSecret],
      externalUrl: [this.orgSsoAuth?.externalUrl],
      syncPeople: [this.orgSsoAuth?.syncPeople],
      syncClasses: [this.orgSsoAuth?.syncClasses],
      syncCourses: [this.orgSsoAuth?.syncCourses],
    });

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.orgSsoAuthGroupForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.setObjectValues();
    });
  }

  //SsoRoleMappings
  manageChange(event: any, d2lRole: IOrganizationSsoRoleMapping) {
    let isRemoved = false;
    if (event.detail.value === '') {
      isRemoved = true;
    } else {
      d2lRole.roleId = event.detail.value;
    }

    const roleMappingIn = { id: d2lRole.id, roleId: d2lRole.roleId, isRemoved: isRemoved } as IOrganizationSsoRoleMappingIn;
    const index = this.orgSsoRoleMappingIn.findIndex(x => x.id === d2lRole.id);
    if (index === -1) {
      this.orgSsoRoleMappingIn.push(roleMappingIn);
    } else {
      this.orgSsoRoleMappingIn[index] = roleMappingIn;
    }

    this.setObjectValues();
  }

  setObjectValues() {
    if (this.orgSsoAuthGroupForm.valid) {
      const orgSsoAuthIn = {
        id: this.orgSsoAuth?.id ? this.orgSsoAuth.id : null,
        organizationId: this.organizationId,
        externalId: this.orgSsoAuthGroupForm.controls['externalId'].value,
        externalAuthId: this.orgSsoAuthGroupForm.controls['externalAuthId'].value,
        externalAuthSecret: this.orgSsoAuthGroupForm.controls['externalAuthSecret'].value,
        externalUrl: this.orgSsoAuthGroupForm.controls['externalUrl'].value,
        syncPeople: this.orgSsoAuthGroupForm.controls['syncPeople'].value,
        syncClasses: this.orgSsoAuthGroupForm.controls['syncClasses'].value,
        syncCourses: this.orgSsoAuthGroupForm.controls['syncCourses'].value,
        organizationSsoRoleMapping: this.orgSsoRoleMappingIn as IOrganizationSsoRoleMappingIn[],
      } as IOrganizationSsoAuthIn;

      this.orgSsoAuthChange.emit(orgSsoAuthIn);
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
