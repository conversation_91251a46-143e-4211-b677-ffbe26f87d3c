<ion-card class="card-container">
  @if (dataLoaded) {
    <div class="top-form-container">
      <h4>{{ 'Manage your LMS URL\'s' | translate | async }}</h4>
      @if (orgSsoAuth?.organizationSsoauthExternalUrls) {
        @for (url of orgSsoAuth.organizationSsoauthExternalUrls; track url) {
          <ion-row>
            <ion-col size="10">
              <p class="externalUrl">{{ url.externalUrl }}</p>
            </ion-col>
            <ion-col size="2" class="center-col">
              <ion-checkbox class="checkbox" color="primary" [(ngModel)]="url.isEnabled" (ngModelChange)="updateExternalUrl($event, url)">{{ 'Enabled' | translate | async }}</ion-checkbox>
            </ion-col>
          </ion-row>
        }
      }
    </div>
  }
</ion-card>
