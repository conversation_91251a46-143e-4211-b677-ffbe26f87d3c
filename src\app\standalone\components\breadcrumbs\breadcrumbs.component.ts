import { CommonModule, AsyncPipe } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { MatTooltip } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { IBreadcrumb, IRouteParams } from '@app/core/contracts/contract';
import { ViewType } from '@app/core/enums/view-type';
import { BreadcrumbService } from '@app/core/services/breadcrumb-service';
import { InstanceService } from '@app/core/services/instance-service';
import { LayoutService } from '@app/core/services/layout-service';
import { IonicModule } from '@ionic/angular';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-breadcrumbs',
    templateUrl: './breadcrumbs.component.html',
    styleUrls: ['./breadcrumbs.component.scss'],
    imports: [IonicModule, MatTooltip, CommonModule, AsyncPipe, TranslatePipe]
})
export class BreadcrumbsComponent implements OnInit {
  @Input() breadcrumbs: IBreadcrumb[];
  @Input() isScorm: boolean;
  @Input() routeParams: IRouteParams;
  @Input() onlyContent = false;
  @Input() hasPageTitle: boolean;
  @Input() isFirstLevelView: boolean;

  viewTypes = ViewType;
  isMobile = false;

  constructor(
    public layoutService: LayoutService,
    private breadcrumbService: BreadcrumbService,
    private instanceService: InstanceService,
    private router: Router
  ) {}

  ngOnInit() {
    if (this.layoutService.currentScreenSize === 'xs') {
      this.isMobile = true;
    }
  }

  goToInstance(breadcrumb: IBreadcrumb, index: number, event: any) {
    event.stopPropagation();

    if (index + 1 === this.breadcrumbs.length && index !== 0 && breadcrumb.orgId === null) {
      this.goBack();
      return;
    } else if (index === 0 && breadcrumb.url.includes('player')) {
      this.breadcrumbService.updateCurrentBreadcrumbUrl(breadcrumb.url.replace('player', 'grid'));
    }

    this.openInstance(breadcrumb);
  }

  openInstance(breadcrumb: IBreadcrumb) {
    this.breadcrumbService.removeBreadCrumb(breadcrumb.id);
    this.instanceService.openInstance(breadcrumb.url, null, null, null, null, this.breadcrumbService.hardRefresh, true);
  }

  goBack() {
    if (this.routeParams.viewType === ViewType.Grid) {
      if (this.breadcrumbs.length > 0) {
        const lastBreadcrumb = this.breadcrumbs[this.breadcrumbs.length - 1];
        this.breadcrumbService.removeBreadCrumb(lastBreadcrumb.id);

        this.instanceService.openInstance(lastBreadcrumb.url, null, null, null, null, this.breadcrumbService.hardRefresh, true);
        return;
      } else {
        this.router.navigate(['/library']);
        return;
      }
    }

    this.routeParams.viewType = ViewType.Grid;
    const prevInstanceSlug = this.instanceService.getPrevInstanceSlug();
    this.routeParams.instanceSlug = prevInstanceSlug?.length > 0 ? prevInstanceSlug : this.routeParams.instanceSlug;
    this.instanceService.openInstance(this.routeParams.featureSlug, prevInstanceSlug?.length > 0 ? prevInstanceSlug : this.routeParams.instanceSlug, this.routeParams.tabName, 'grid');
  }
}
