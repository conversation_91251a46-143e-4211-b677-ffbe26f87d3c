import { Component, Input } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { MatTooltipModule } from '@angular/material/tooltip';
import { GlobalToastService } from '@app/core/services/global-toast.service';
import { Share } from '@capacitor/share';
import { IonicModule, ModalController } from '@ionic/angular';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
  templateUrl: 'share-modal.component.html',
  styleUrls: ['./share-modal.component.scss'],
  imports: [IonicModule, MatIconModule, MatButtonModule, MatTooltipModule, MatFormFieldModule, MatInput, AsyncPipe, TranslatePipe],
})
export class ShareModalComponent {
  @Input() shareUrl: string;

  constructor(
    private modalController: ModalController,
    private toast: GlobalToastService
  ) {}

  async shareToPlatform(platform: string) {
    switch (platform) {
      case 'x':
        window.open(`https://x.com/intent/tweet?url=${encodeURIComponent(this.shareUrl)}`, '_blank');
        break;
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(this.shareUrl)}`, '_blank');
        break;
      case 'linkedin':
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(this.shareUrl)}`, '_blank');
        break;
      case 'more':
        await Share.share({
          url: this.shareUrl,
        });
        break;
      case 'whatsapp':
        window.open(`https://wa.me/?text=${encodeURIComponent(this.shareUrl)}`, '_blank');
        break;
      case 'messenger':
        window.open(`fb-messenger://share/?link=${encodeURIComponent(this.shareUrl)}`, '_blank');
        break;
      case 'signal':
        window.open(`https://signal.me/#p/${encodeURIComponent(this.shareUrl)}`, '_blank');
        break;
      case 'text':
        window.open(`sms:?body=${encodeURIComponent(this.shareUrl)}`, '_blank');
        break;
    }
  }

  async copyToClipboard() {
    await navigator.clipboard.writeText(this.shareUrl);
    this.toast.presentLinkCopiedToast();
  }

  dismiss() {
    this.modalController.dismiss();
  }
}
