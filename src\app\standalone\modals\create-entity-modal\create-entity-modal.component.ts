import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IUserSetup, IProductOrganization, IUser, IUserRole } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { ModalController, IonicModule } from '@ionic/angular';
import { Subject, takeUntil } from 'rxjs';
import { NgClass, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-create-entity-modal',
    templateUrl: './create-entity-modal.component.html',
    styleUrls: ['./create-entity-modal.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, NgClass, AsyncPipe, TranslatePipe]
})
export class CreateEntityModalComponent implements OnInit, OnDestroy {
  @Input() id: string;
  @Input() type: string;
  @Input() instanceId: string;
  @Input() orgId: string;
  @Input() productOrg: IProductOrganization | undefined | null;

  componentDestroyed$: Subject<boolean> = new Subject();
  userForm: UntypedFormGroup;
  email: UntypedFormControl;
  username: UntypedFormControl;
  firstName: UntypedFormControl;
  lastName: UntypedFormControl;
  role: UntypedFormControl;
  notifyNewUser: UntypedFormControl;
  entities: any[] = [];
  selectedEntities: any[] = [];
  userRoles: IUserRole[];
  organizationId: string;
  step = 1;
  steps = 2;
  emailInfo = 'Used to login and authenticate their account.';
  usernameInfo = 'Allows someone without an email to login.';
  title = 'Create a new user';
  userEmailExists = false;
  usernameExists = false;

  constructor(
    private modalController: ModalController,
    private dataService: DataService
  ) {}

  ngOnInit() {
    this.setupModal();
    this.createFormControls();
    this.createForm();
    this.getAvailableRoles();
  }

  createFormControls() {
    this.email = new UntypedFormControl('', Validators.required);
    this.username = new UntypedFormControl('', Validators.required);
    this.firstName = new UntypedFormControl('', Validators.required);
    this.lastName = new UntypedFormControl('', Validators.required);
    this.role = new UntypedFormControl('', Validators.required);
    this.notifyNewUser = new UntypedFormControl(false, Validators.required);
  }

  createForm() {
    this.userForm = new UntypedFormGroup({
      email: this.email,
      username: this.username,
      firstName: this.firstName,
      lastName: this.lastName,
      role: this.role,
      notifyNewUser: this.notifyNewUser,
    });
  }

  setupModal() {
    if (this.type === 'Organization Manager') {
      this.organizationId = this.id;
    }
    if (this.type === 'Product Manager') {
      if (this.productOrg?.orgId) {
        this.organizationId = this.productOrg?.orgId;
      }
    }
    if (this.type === 'Modifiable Learning Container Pages') {
      if (this.orgId) {
        this.organizationId = this.orgId;
      }
      this.steps = 1;
    }
  }

  getAvailableRoles() {
    this.dataService
      .getUserOrganizationRoles(this.organizationId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(roles => {
        if (roles) {
          this.userRoles = roles;
        }
      });
  }

  userEmailLookup() {
    const newUser = this.userForm.value as IUser;
    if (newUser) {
      this.userEmailExists = true;
      this.emailInfo = 'Performing lookup.';
      this.dataService
        .userLookupByEmail(newUser)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(exists => {
          if (exists) {
            this.emailInfo = 'A user with this email address already exists.';
            this.userEmailExists = true;
          } else {
            this.emailInfo = 'Used to login and authenticate their account.';
            this.userEmailExists = false;
          }
        });
    }
  }

  usernameLookup() {
    const newUser = this.userForm.value as IUser;
    if (newUser) {
      this.usernameInfo = 'Performing lookup.';
      this.usernameExists = true;
      this.dataService
        .userLookupByUsername(newUser)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(exists => {
          if (exists) {
            this.usernameInfo = 'A user with this username already exists';
            this.usernameExists = true;
          } else {
            this.usernameInfo = 'Allows someone without an email to login.';
            this.usernameExists = false;
          }
        });
    }
  }

  next() {
    if (this.step === 1) {
      if (!this.userForm.valid) {
        this.userForm.markAllAsTouched();
        return;
      }
    }
    if (this.userEmailExists || this.usernameExists) {
      return;
    }

    this.step++;
    if (this.step === 2) {
      if (this.type === 'Product Manager') {
        this.title = 'Classroom Access';
        this.getProductClassrooms();
      }
      if (this.type === 'Organization Manager') {
        this.title = 'Product Access';
        this.getOrganizationProducts();
      }
    }
    if (this.step === 3) {
      this.title = 'Invite new user';
      this.selectedEntities = this.entities.filter(x => x.selected === true);
    }
  }

  getOrganizationProducts() {
    this.dataService
      .getOrganizationProducts(this.organizationId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(products => {
        if (products) {
          this.entities = products.map(p => ({
            name: p.name,
            id: p.id,
            selected: false,
          }));
        }
      });
  }

  getProductClassrooms() {
    if (this.productOrg?.id) {
      this.dataService
        .getProductClassrooms(this.productOrg?.id)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(classrooms => {
          if (classrooms) {
            this.entities = classrooms.map(c => ({
              name: c.title,
              id: c.id,
              selected: false,
            }));
          }
        });
    }
  }

  back() {
    this.step--;
  }

  addUser() {
    const user = this.userForm.value;
    const orgUser = {
      email: user.email,
      username: user.username,
      firstName: user.firstName,
      lastName: user.lastName,
      password: user.password,
      organizationId: this.organizationId,
      productIds: this.getProductIds(),
      instanceId: this.type === 'Modifiable Learning Container Pages' ? this.instanceId : null,
      roleId: user.role,
      instanceIds: this.type === 'Product Manager' ? this.selectedEntities.map(x => x.id) : null,
      notifyNewUser: user.notifyNewUser,
    } as IUserSetup;    
    this.modalController.dismiss(orgUser);
  }

  getProductIds(): string[] {
    if (this.type === 'Organization Manager' && this.selectedEntities.length > 0) {
      return this.selectedEntities.map(x => x.id);
    }
    if (this.type === 'Product Manager' && this.productOrg?.productId) {
      return [this.productOrg?.productId];
    }
    return [];
  }

  close() {
    this.modalController.dismiss();
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }

  setNotifyUser($event: any, notifyNewUser: UntypedFormControl) {
    this.notifyNewUser.setValue(!notifyNewUser.value);
  }
}
