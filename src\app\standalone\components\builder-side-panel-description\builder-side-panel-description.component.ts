import { NgClass, AsyncPipe } from '@angular/common';
import { Component, Input } from '@angular/core';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-builder-side-panel-description',
    templateUrl: './builder-side-panel-description.component.html',
    styleUrls: ['./builder-side-panel-description.component.scss'],
    imports: [NgClass, AsyncPipe, TranslatePipe]
})
export class BuilderSidePanelDescriptionComponent {
  @Input() text: string | null;
  @Input() noPadding = false;
  constructor() {}
}
