import { Component, Input, On<PERSON><PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { MatCheckbox } from '@angular/material/checkbox';
import { MatAccordion, MatExpansionPanel, MatExpansionPanelHeader } from '@angular/material/expansion';
import { MatRadioButton, MatRadioGroup } from '@angular/material/radio';
import { MatTab, MatTabChangeEvent, MatTabContent, MatTabGroup } from '@angular/material/tabs';
import { IOrganization, IOrganizationSearch, IProductOrganization, IProductRenew, IRole, IUserOrganizationProduct } from '@app/core/contracts/contract';
import { UserOrganizationProductRole, UserOrgProdRoles } from '@app/core/dtos/UserOrganizationProductRoles';
import { ActionTypes } from '@app/core/enums/action-types.enum';
import { DataService } from '@app/core/services/data-service';
import { InstanceService } from '@app/core/services/instance-service';
import { IonicModule, ModalController } from '@ionic/angular';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { AddOrgProductModalComponent } from '../add-org-product-modal/add-org-product-modal.component';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-user-organizations-modal',
    templateUrl: './user-organizations-modal.component.html',
    styleUrls: ['./user-organizations-modal.component.scss'],
    encapsulation: ViewEncapsulation.None,
    imports: [IonicModule, MatAccordion, MatExpansionPanel, MatExpansionPanelHeader, MatTabGroup, MatTab, MatTabContent, MatRadioGroup, MatRadioButton, MatCheckbox, AsyncPipe, TranslatePipe]
})
export class UserOrganizationsModalComponent implements OnInit, OnDestroy {
  @Input() organizationId: string;
  @Input() featureType: string;
  @Input() userId: string;
  @Input() name: string;
  organizations: IOrganization[] = [];
  currentAmount = 0;
  getAmount = 25;
  moreResults = false;
  actionTypes = ActionTypes;
  orgRoles: { organizationId: string; roles: UserOrganizationProductRole[] }[] = [];
  prodRoles: { organizationId: string; productId: string; roles: UserOrganizationProductRole[] }[] = [];
  componentDestroyed$: Subject<boolean> = new Subject();
  loading = false;
  constructor(
    private dataService: DataService,
    private instanceService: InstanceService,
    private modalController: ModalController,
    private AddProductModalController: ModalController
  ) {}

  ngOnInit() {
    this.getOrganizationsById(this.organizationId, false);
    if (this.name === ('' || ' ' || undefined)) {
      this.name = 'Unnamed User';
    }
  }

  getOrgUserRoleById(organization: IOrganization) {
    if (organization != null) {
      this.loading = true;
      this.dataService.getOrgUserRoleById(organization.id, this.userId).subscribe((orgUserRole: IRole) => {
        if (orgUserRole != null) {
          this.orgRoles
            .filter(orgRole => orgRole.organizationId === organization.id)
            .forEach(orgRole => {
              orgRole.roles.forEach(role => {
                if (role.id === orgUserRole.id) {
                  role.isSelected = true;
                }
              });
            });
        }
        this.loading = false;
      });
    }
  }

  getOrganizationsById(organizationId: string, loadMore: boolean) {
    if (organizationId !== undefined) {
      this.loading = true;
      this.dataService
        .getOrganizationsById(this.organizationId, this.featureType, this.currentAmount, this.getAmount, this.userId)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((organizations: IOrganization[]) => {
          if (organizations.length > 0) {
            //OnLoadMoreData
            if (!loadMore) {
              this.organizations = organizations;
              this.currentAmount += organizations.length;
            } else {
              organizations.forEach(organization => {
                this.organizations = [...this.organizations, organization];
              });
              this.currentAmount += organizations.length;
            }

            this.moreResults = organizations.length >= this.getAmount;

            this.organizations.forEach(organization => {
              organization.organizationProducts = [];
              this.dataService
                .getUserOrganizationProducts(this.userId, organization.id)
                .pipe(takeUntil(this.componentDestroyed$))
                .subscribe((userOrganizationProduct: IUserOrganizationProduct[]) => {
                  organization.organizationProducts = userOrganizationProduct;
                  organization.organizationProducts.forEach(product => {
                    this.prodRoles.push({ organizationId: organization.id, productId: product.id, roles: new UserOrgProdRoles().userOrganizationProductRoles });
                    this.getOrgProductUserRoleById(organization.id, product);
                  });
                });

              this.orgRoles.push({ organizationId: organization.id, roles: new UserOrgProdRoles().userOrganizationProductRoles });
              this.getOrgUserRoleById(organization);
            });
          }
        });
    }
  }

  openGroup(panelState: boolean, orgId: string) {
    this.organizations.forEach(org => {
      if (org.id === orgId) {
        org.panelState = panelState;
      }
    });
  }

  openProductGroup(panelState: boolean, product: IUserOrganizationProduct) {
    product.panelState = panelState;
  }
  routeToOrg(orgId: string) {
    this.instanceService.openInstance('my-organization', orgId);
  }

  tabSelected($event: MatTabChangeEvent, id: string) {
    this.currentAmount = 0;
    this.moreResults = false;
    switch ($event.index) {
      case 0: {
        this.prodRoles.filter(product => product.organizationId === id).pop();
        break;
      }
      case 1: {
        break;
      }
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }

  getOrganizationRoles(id: string) {
    const orgRoles = this.orgRoles.filter(orgRole => orgRole.organizationId === id);
    if (orgRoles && orgRoles.length > 0) {
      return orgRoles[0].roles;
    } else {
      return new UserOrgProdRoles().userOrganizationProductRoles;
    }
  }

  getProductRoles(orgId: string, product: IUserOrganizationProduct) {
    const prodRoles = this.prodRoles.filter(prodRole => prodRole.organizationId === orgId && prodRole.productId === product.id);
    if (prodRoles && prodRoles.length > 0) {
      return prodRoles[0].roles;
    } else {
      return new UserOrgProdRoles().userOrganizationProductRoles;
    }
  }

  manageOrgRoleChange(orgId: string, roleId: string, userId: string) {
    this.loading = true;
    this.dataService
      .organizationuser(orgId, roleId, userId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.loading = false;
      });
  }

  manageProductOrgRoleChange(userId: string, role: UserOrganizationProductRole, product: IUserOrganizationProduct) {
    product.userHasProduct = true;
    this.loading = true;
    this.dataService
      .updateProductOrganizationUserRole(product.id, product.organizationId, role.id, userId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.loading = false;
      });
  }

  complete() {
    this.ngOnDestroy();
    return this.modalController.dismiss(null, 'confirm');
  }

  async addProduct(id: string) {
    const modal = await this.AddProductModalController.create({
      component: AddOrgProductModalComponent,
      componentProps: { orgId: id },
      cssClass: 'add-org-product-modal.component',
    });

    modal.onDidDismiss().then(value => {
      if (value.data) {
        if (id) {
          const org: IOrganizationSearch = {
            id: id,
            name: '',
            country: '',
          };
          this.saveOrganizationToProduct(value.data, org);
        }
      }
    });

    await modal.present();
  }

  saveOrganizationToProduct(productRenew: IProductRenew, organizationSearch: IOrganizationSearch) {
    if (productRenew) {
      this.loading = true;
      this.dataService
        .addOrganizationToProduct(productRenew, organizationSearch)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((addedOrg: IProductOrganization[]) => {
          if (addedOrg.length > 0) {
            addedOrg.forEach(organization => {
              this.organizations
                .filter(org => org.id === organization.orgId)
                .forEach(org => {
                  org.organizationProducts = [];
                  this.dataService
                    .getUserOrganizationProducts(this.userId, org.id)
                    .pipe(takeUntil(this.componentDestroyed$))
                    .subscribe((userOrganizationProduct: IUserOrganizationProduct[]) => {
                      org.organizationProducts = userOrganizationProduct;
                      org.organizationProducts.forEach(product => {
                        this.prodRoles.push({ organizationId: org.id, productId: product.id, roles: new UserOrgProdRoles().userOrganizationProductRoles });
                        this.getOrgProductUserRoleById(org.id, product);
                        this.loading = false;
                      });
                    });
                });
            });
          }
        });
    }
  }

  private getOrgProductUserRoleById(orgId: string, product: IUserOrganizationProduct) {
    if (product != null) {
      this.prodRoles
        .filter(prodRole => prodRole.organizationId === orgId && prodRole.productId === product.id)
        .forEach(prodRole => {
          prodRole.roles.forEach(role => {
            if (role.id === product.userRoleId) {
              role.isSelected = true;
            }
          });
        });
    }
  }
}
