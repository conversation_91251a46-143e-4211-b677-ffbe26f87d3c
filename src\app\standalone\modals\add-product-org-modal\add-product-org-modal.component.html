<div class="user-search-container">
  <form [formGroup]="searchForm">
    <ion-grid>
      <ion-row>
        <ion-col class="header-col">
          <div class="top-heading">{{ 'Select the organization(s) you want to add to this product' | translate | async }}</div>
        </ion-col>
      </ion-row>
      <ion-row class="search-bar-row">
        <ion-col size="12">
          <ion-searchbar
            color="dark"
            formControlName="orgSearch"
            (ionChange)="searchOrganizationById()"
            type="search"
            placeholder="{{ 'Search Organizations' | translate | async }}"
            showCancelButton="focus"
            debounce="600">
          </ion-searchbar>
        </ion-col>
      </ion-row>
    </ion-grid>
  </form>
  <ion-content>
    <ion-grid>
      @if (organizations !== null && organizations.length > 0) {
        <ion-list>
          @for (org of organizations; track org) {
            <ion-item (ionChange)="addOrgToList($event, org)">
              <ion-checkbox slot="start"></ion-checkbox>
              <ion-label>
                <span class="heading">{{ org.name | translate | async }}</span>
                <div class="sub-heading">
                  <span>{{ org.country | translate | async }}</span>
                </div>
              </ion-label>
            </ion-item>
          }
        </ion-list>
      }
    </ion-grid>
  </ion-content>

  <ion-footer>
    <div class="footer-content">
      <ion-row>
        <ion-col class="cancel-col">
          <ion-button fill="clear" (click)="close()">{{ 'Cancel' | translate | async }}</ion-button>
        </ion-col>
        <ion-col class="add-col">
          <div>
            <ion-button (click)="add()">{{ 'Add' | translate | async }}</ion-button>
          </div>
        </ion-col>
      </ion-row>
    </div>
  </ion-footer>
</div>
