<div class="parent-container">
  <ion-item class="search-item" [ngClass]="{ 'side-panel-input-padding': sidePanelPadding }" [title]="toolTip" #control>
    @if (showSelected !== true) {
      <ion-label position="stacked" class="label-header">
        {{ label | translate | async }}
        <span title="text" class="reqAsterisk">
          @if (required) {
            <span>* </span>
          }
          <ion-icon name="information-circle-outline"></ion-icon>
        </span>
        @if (identifierText) {
          <span class="identifier">{{ identifierText }}</span>
        }
      </ion-label>
    }
    <ion-input
      style="--background-color:{{ backgroundColor }};"
      (ionChange)="updateSearchResults()"
      [placeholder]="placeHolder"
      [disabled]="disabled"
      [value]="textValue"
      [type]="'text'"
      [(ngModel)]="autocomplete.input"></ion-input>
    @if (errorMessage && touched) {
      <ion-text>
        {{ errorMessage | translate | async }}
      </ion-text>
    }
  </ion-item>
  <ion-list [hidden]="autocompleteItems.length === 0">
    @for (item of autocompleteItems; track item) {
      <ion-item color="dark" tappable (click)="selectSearchResult(item)">
        {{ item.description | translate | async }}
      </ion-item>
    }
  </ion-list>
  <div #postalCode></div>
  @if (addressForm) {
    <form [formGroup]="addressForm">
      <ion-grid>
        @if (!hideCity) {
          <ion-row>
            <ion-col size="12">
              <app-text-input-control
                [itemBackgroundColor]="backgroundColor"
                [sidePanelPadding]="sidePanelPadding"
                [label]="'Address Line 1'"
                [placeHolder]="'Enter your address'"
                [toolTip]="''"
                formControlName="addressLine1"></app-text-input-control>
            </ion-col>
          </ion-row>
        }
        @if (hideCity) {
          <ion-row>
            <ion-col size="6">
              <app-text-input-control
                [itemBackgroundColor]="backgroundColor"
                [sidePanelPadding]="sidePanelPadding"
                [label]="'Address Line 1'"
                [placeHolder]="'Enter your address'"
                [toolTip]="''"
                formControlName="addressLine1"></app-text-input-control>
            </ion-col>
            <ion-col size="6">
              <app-text-input-control
                [itemBackgroundColor]="backgroundColor"
                [sidePanelPadding]="sidePanelPadding"
                [label]="'Country'"
                [placeHolder]="'Country'"
                [toolTip]="''"
                formControlName="country"></app-text-input-control>
            </ion-col>
          </ion-row>
        }
        @if (!hideCity) {
          <ion-row>
            <ion-col size="6">
              <app-text-input-control
                [itemBackgroundColor]="backgroundColor"
                [sidePanelPadding]="sidePanelPadding"
                [label]="'City'"
                [placeHolder]="'City'"
                [toolTip]="''"
                formControlName="city"></app-text-input-control>
            </ion-col>
            <ion-col size="6">
              <app-text-input-control
                [itemBackgroundColor]="backgroundColor"
                [sidePanelPadding]="sidePanelPadding"
                [label]="'Country'"
                [placeHolder]="'Country'"
                [toolTip]="''"
                formControlName="country"></app-text-input-control>
            </ion-col>
          </ion-row>
        }
        <ion-row>
          <ion-col size="6">
            <app-text-input-control
              [itemBackgroundColor]="backgroundColor"
              [sidePanelPadding]="sidePanelPadding"
              [label]="'Province / State'"
              [placeHolder]="'Province / State'"
              [toolTip]="''"
              formControlName="state"></app-text-input-control>
          </ion-col>
          <ion-col size="6">
            <app-text-input-control
              [itemBackgroundColor]="backgroundColor"
              [sidePanelPadding]="sidePanelPadding"
              [label]="'Postal / Zip Code'"
              [placeHolder]="'Postal / Zip Code'"
              [toolTip]="''"
              formControlName="zip"></app-text-input-control>
          </ion-col>
        </ion-row>
      </ion-grid>
    </form>
  }
</div>
