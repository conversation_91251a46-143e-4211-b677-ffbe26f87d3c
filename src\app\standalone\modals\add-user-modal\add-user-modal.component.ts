import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IUserClaimSearch } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { ModalController, IonicModule } from '@ionic/angular';
import { debounceTime, Subject, takeUntil } from 'rxjs';
import { NgClass, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-add-user-modal',
    templateUrl: './add-user-modal.component.html',
    styleUrls: ['./add-user-modal.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, NgClass, AsyncPipe, TranslatePipe]
})
export class AddUserModalComponent implements OnInit, OnD<PERSON>roy {
  @Input() instanceId: string;
  @Input() id: string;
  @Input() type: string;
  searchForm: UntypedFormGroup;
  userSearch: UntypedFormControl;
  users: IUserClaimSearch[] = [];
  selectedUsers: IUserClaimSearch[] = [];
  selectedStyle = 'selected-item';

  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(
    private modalController: ModalController,
    private dataService: DataService
  ) {}

  async ngOnInit() {
    this.createFormControls();
    this.createForm();
    this.searchUsers();
  }

  createFormControls() {
    this.userSearch = new UntypedFormControl('');
  }

  createForm() {
    this.searchForm = new UntypedFormGroup({
      userSearch: this.userSearch,
    });
  }

  searchUsers() {
    switch (this.type) {
      case 'Product Manager':
        this.dataService
          .searchProductOrgUsersExclExisting(this.userSearch.value, this.id)
          .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
          .subscribe((users: IUserClaimSearch[]) => {
            this.users = users;
          });
        break;
      case 'Accredited Package':
      case 'Modifiable Package':
        this.dataService
          .searchRowInstanceUsersExclExisting(this.userSearch.value, this.instanceId, this.id)
          .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
          .subscribe((users: IUserClaimSearch[]) => {
            this.users = users;
          });
        break;
      case 'Organization Manager':
        if (this.userSearch.value !== '') {
          this.dataService
            .searchOrgUsersExclExisting(this.userSearch.value, this.id)
            .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
            .subscribe((users: IUserClaimSearch[]) => {
              this.users = users;
            });
        }
        break;
      default:
        this.dataService
          .searchInstanceOrgUsersExclExisting(this.userSearch.value, this.id)
          .pipe(takeUntil(this.componentDestroyed$), debounceTime(1000))
          .subscribe((users: IUserClaimSearch[]) => {
            this.users = users;
          });
        break;
    }
  }

  addAllUsersToList(event: any, selectedUsers: IUserClaimSearch[]) {
    if (event.detail.checked) {
      selectedUsers.map((user: IUserClaimSearch) => {
        if (!user.selected) {
          user.selected = true;
          this.selectedUsers.push(user);
        }
      });
    } else {
      selectedUsers.map((networkOrg: IUserClaimSearch) => {
        networkOrg.selected = false;
      });
      this.selectedUsers = [];
    }
  }

  addUserToList(event: any, selectedUser: IUserClaimSearch) {
    if (event.detail.checked) {
      selectedUser.selected = true;
      this.selectedUsers.push(selectedUser);
    } else {
      const index = this.selectedUsers.findIndex(x => x.id === selectedUser.id);
      selectedUser.selected = true;
      this.selectedUsers.splice(index, 1);
    }
  }

  close() {
    this.modalController.dismiss();
  }

  add() {
    this.modalController.dismiss(this.selectedUsers);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
