<div id="screen-canvas" class="parent-container">
  @if (badgeImageOverlay) {
    <div class="image-header-gradient" [style]="'--background-image:url(' + badgeImageOverlay + ');'"></div>
  }
  <ion-row data-html2canvas-ignore="true" class="top-row">
    <ion-col class="top-options-col">
      @if (myPortfolioInstance && !hidePortfolioAdd) {
        <ion-button (click)="addToMyProfileInstance()" class="add-to-button" size="small"> {{ 'Add To Portfolio' | translate | async }}</ion-button>
      }
      <ion-button (click)="downloadSnippet()" class="export-button" size="small"><ion-icon name="cloud-download-outline"></ion-icon> {{ 'Download' | translate | async }}</ion-button>
      @if (!badgePngGenerated) {
        <div class="badge-loader">
          <ion-spinner color="medium" name="circular"></ion-spinner>
        </div>
      }
      <div (click)="close()" class="close-button-container">
        <mat-icon svgIcon="close_icon_solid"></mat-icon>
      </div>
    </ion-col>
  </ion-row>
  <ion-row>
    <ion-col size="3" class="image-col">
      <div class="inner-image-container">
        <div class="image-gradient" [style]="'--background-image:url(' + assetImageIconUrl + ');'">
          @if (badgeImageIcon) {
            <span class="badge-icon">
              <img class="icon" src="{{ badgeImageIcon }}" />
            </span>
          }
        </div>
      </div>
    </ion-col>
    <ion-col size="9" class="details-col">
      <div class="inner-container">
        <div class="completion-header">
          <div class="feature-name">{{ achievementCompletion.featureName | translate | async | uppercase }}</div>
          <div class="badge-name">{{ achievementCompletion.badgeName | translate | async }}</div>
          <div class="issued-container">
            <div class="awarded">
              {{ 'Awarded to' | translate | async }}&nbsp;<span class="inner-sub">{{ authService.userContext?.fullName }}</span>
            </div>
            <div class="issued-by">
              {{ 'Issued by' | translate | async }}&nbsp;<span class="inner-sub">{{ achievementCompletion.issuedBy | translate | async }}</span> {{ 'on' | translate | async }}
              {{ achievementCompletion.issuedDate | date }}
            </div>
          </div>
        </div>

        <div class="completed-instance">
          <div [innerHTML]="achievementCompletion?.badgeDescription ?? '' | parsePipe: instanceId : null : false | async | translate | async"></div>
        </div>

        @if (achievementCompletion?.learningOutcomeDescription) {
          <div class="learning-outcome">
            <div [innerHTML]="achievementCompletion?.learningOutcomeDescription ?? '' | parsePipe: instanceId : null : false | async | translate | async"></div>
          </div>
        }
      </div>
    </ion-col>
  </ion-row>
</div>
