import { Ng<PERSON><PERSON>, NgTemplateOutlet, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { Component, CUSTOM_ELEMENTS_SCHEMA, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { IComponent, IMediaAndText } from '@app/core/contracts/contract';
import { AuthService } from '@app/core/services/auth-service';
import { LayoutService } from '@app/core/services/layout-service';
import { VideoPlayerComponent } from '@app/standalone/components/video-player/video-player.component';
import { environment } from '@env/environment';
import { IonicModule } from '@ionic/angular';
import { QuillViewComponent } from 'ngx-quill';
import { Subject } from 'rxjs';
import { Swiper } from 'swiper/types';
import { BaseValueComponent } from '@app/standalone/components/base-control/base-value.component';


@Component({
    selector: 'app-media-and-text-value',
    templateUrl: './media-and-text-value.component.html',
    styleUrls: ['./media-and-text-value.component.scss'],
    imports: [NgTemplateOutlet, NgClass, VideoPlayerComponent, QuillViewComponent, IonicModule, AsyncPipe, TranslatePipe],
    schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class MediaAndTextValueComponent extends BaseValueComponent implements OnInit {
  @Input() component!: IComponent;
  @Input() inheritedPropertyValue: string | null;
  @ViewChild('swiper') swiperRef: ElementRef;
  componentDestroyed$: Subject<boolean> = new Subject();
  swiper: Swiper;
  imageUrl = 'assets/images/no-image.png';
  imageTextList: IMediaAndText[] = [];
  stylingDirection: string;
  darkText: boolean;

  constructor(
    private layoutService: LayoutService,
    private authService: AuthService
  ) {
    super();
  }

  ngOnInit(): void {
    this.setData();
  }

  override setData() {    
    if (this.instanceComponent?.value) {
      this.imageTextList = JSON.parse(this.instanceComponent.value) as IMediaAndText[];
      if (this.imageTextList.length === 0) {
        this.imageTextList.push({
          sortOrder: 0,
          heading: 'Heading',
          paragraph: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Cras dolor libero, rutrum id hendrerit eget, dignissim at est. Vestibulum congue lectus in turpis aliquet fringilla.',
          stylingDirection: 'Left',
        } as IMediaAndText);
      }
      this.stylingDirection = this.instanceComponent?.component.templateField.stylingDirection ?? 'Left';
      this.darkText = this.instanceComponent?.component.templateField.darkText ?? false;
    }

    for (let i = 0; i < this.imageTextList.length; i++) {
      if (!this.imageTextList[i].assetType?.includes('video')) {
        if (this.imageTextList[i].asset) {
          this.imageTextList[i].assetUrl = `${environment.contentUrl}asset/${this.imageTextList[i].asset}/content`;
        } else {
          this.imageTextList[i].assetUrl = 'assets/images/no-image.png';
        }
      } else {
        this.imageTextList[i].assetUrl = 'assets/images/no-image.png';
      }
    }
  }

  openLink(item: IMediaAndText) {
    if (item?.buttonUrl && item.sameUrlNavigation === false && this.layoutService.currentScreenSize !== 'xs') {
      if (item?.buttonUrl?.startsWith('https://')) {
        window.open(item.buttonUrl, '_blank')?.focus();
        return;
      } else {
        window.open(`https://${item.buttonUrl}`, '_blank')?.focus();
        return;
      }
    } else if (item?.buttonUrl) {
      if (this.authService.isGuest() && item.buttonUrl.includes('user/joincode') && this.layoutService.currentScreenSize === 'xs') {
        const joinCode = item.buttonUrl.split('/').pop() ?? '';
        sessionStorage.setItem('returnUrl', `/user/joincode/${joinCode}`);
        this.authService.startAuthenticationJoinCode(joinCode);
        return;
      }

      if (item.buttonUrl.startsWith('https://')) {
        window.open(item.buttonUrl, '_self')?.focus();
        return;
      } else {
        window.open(`https://${item.buttonUrl}`, '_self')?.focus();
        return;
      }
    }
  }

  toggleDrag(criteria: string) {
    if (criteria && criteria.includes('video')) {
      this.swiperRef.nativeElement.swiper.allowTouchMove = false;
    } else this.swiperRef.nativeElement.swiper.allowTouchMove = true;
  }

  slideNext() {
    this.swiperRef.nativeElement.swiper.slideNext(100);
  }

  slidePrev() {
    this.swiperRef.nativeElement.swiper.slidePrev(100);
  }
}
