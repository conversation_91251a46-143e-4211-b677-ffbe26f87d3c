<form [formGroup]="userForm" class="container">
  <div class="inner-container">
    @if (step < 3) {
      <ion-row class="headings">
        <ion-label class="step">{{ 'Step' | translate | async }} {{ step }}/{{ steps }}</ion-label>
      </ion-row>
    }

    <ion-row class="headings">
      <ion-label class="title">{{ title | translate | async }}</ion-label>
    </ion-row>
    @if (step === 3) {
      <ion-row class="selected-products">
        @if (type === 'Organization Manager') {
          <ion-label>{{ 'You are about to add them to' | translate | async }} {{ selectedEntities.length }} {{ 'new product/s' | translate | async }}</ion-label>
        }
        @if (type === 'Product Manager') {
          <ion-label>{{ 'You are about to add them to' | translate | async }} {{ selectedEntities.length }} {{ 'new classroom/s' | translate | async }}</ion-label>
        }
        @if (type === 'Modifiable Learning Container Pages') {
          <ion-label>{{ 'You are about to add them to' | translate | async }} {{ selectedEntities.length }} {{ 'new assignment/s' | translate | async }}</ion-label>
        }
      </ion-row>
    }
    @if (step === 1) {
      <div class="input">
        <ion-item>
          <ion-label position="floating">{{ 'Email' | translate | async }}</ion-label>
          <ion-input (ionChange)="userEmailLookup()" type="email" formControlName="email"></ion-input>
        </ion-item>
        <ion-label class="information" [ngClass]="{ 'information-warn': userEmailExists }">{{ emailInfo | translate | async }}</ion-label>
      </div>
      <div class="input">
        <ion-item>
          <ion-label position="floating">{{ 'Username' | translate | async }}</ion-label>
          <ion-input (ionChange)="usernameLookup()" formControlName="username"></ion-input>
        </ion-item>
        <ion-label class="information" [ngClass]="{ 'information-warn': usernameExists }">{{ usernameInfo | translate | async }}</ion-label>
      </div>
      <ion-item class="input">
        <ion-label position="floating">{{ 'First Name' | translate | async }}</ion-label>
        <ion-input formControlName="firstName"></ion-input>
      </ion-item>
      <ion-item class="input">
        <ion-label position="floating">{{ 'Last Name' | translate | async }}</ion-label>
        <ion-input formControlName="lastName"></ion-input>
      </ion-item>
      <ion-item class="input">
        <ion-select placeholder="{{ 'Select Role' | translate | async }}" formControlName="role">
          @for (item of userRoles; track item) {
            <ion-select-option [value]="item.id">
              {{ item.name | translate | async }}
            </ion-select-option>
          }
        </ion-select>
      </ion-item>
      <div class="input">
        <ion-item>
          <ion-col size="1">
            <ion-checkbox [checked]="notifyNewUser.value" (ionChange)="setNotifyUser($event, notifyNewUser)"></ion-checkbox>
          </ion-col>
          <ion-col size="10" (click)="setNotifyUser($event, notifyNewUser)">
            <div class="heading">{{ 'Notify New User' | translate | async }}</div>
          </ion-col>
        </ion-item>
        @if (notifyNewUser.value) {
          <ion-label class="information">{{ 'The system default password will be applied' | translate | async }}</ion-label>
        }
      </div>
    }
    @if (step === 2) {
      <div>
        <ion-list>
          @for (product of entities; track product) {
            <div>
              <div [ngClass]="{ 'products-container': !product.selected, 'products-container-selected': product.selected }" (click)="product.selected = !product.selected">
                <label> {{ product.name | translate | async }}</label>
              </div>
            </div>
          }
        </ion-list>
      </div>
    }
    @if (step === 3) {
      <div>
        <ion-list>
          @for (product of selectedEntities; track product) {
            <div>
              <div class="products-container-selected">
                <label> {{ product.name | translate | async }}</label>
              </div>
            </div>
          }
        </ion-list>
      </div>
    }
    <div class="line"></div>

    <div class="footer-content">
      <ion-row>
        <ion-col class="cancel-col">
          @if (step === 1) {
            <ion-button fill="clear" (click)="close()">{{ 'Cancel' | translate | async }}</ion-button>
          }
          @if (step > 1) {
            <ion-button fill="clear" (click)="back()">{{ 'Back' | translate | async }}</ion-button>
          }
        </ion-col>
        <ion-col class="add-col">
          @if (step < 3 && type !== 'Modifiable Learning Container Pages') {
            <ion-button (click)="next()">{{ 'Next' | translate | async }}</ion-button>
          }
          @if (step === 3 || type === 'Modifiable Learning Container Pages') {
            <ion-button (click)="addUser()">{{ 'Finish' | translate | async }}</ion-button>
          }
        </ion-col>
      </ion-row>
    </div>
  </div>
</form>
