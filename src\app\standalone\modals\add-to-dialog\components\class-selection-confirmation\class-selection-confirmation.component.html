<ion-header class="ion-no-border">
  <ion-toolbar mode="ios">
    <ion-title>{{ 'You\'ve selected' | translate | async }}</ion-title>
  </ion-toolbar>
  <ion-toolbar>
    @if (instances) {
      <app-instance-card
        [instance]="selectedInstance"
        [isModalOpen]="isModalOpen"
        (instanceClicked)="selectInstanceClicked($event)"
        [isDropdown]="true"
        [isModalOpen]="isInstanceModalOpen"></app-instance-card>
    }
  </ion-toolbar>
</ion-header>
<ion-content class="ion-padding" [style.height.vh]="height" [style.min-height.vh]="height" [style.max-height.vh]="height"></ion-content>
<ion-footer>
  <ion-toolbar mode="ios">
    <ion-buttons slot="start">
      <ion-button fill="clear" color="medium" (click)="close()">{{ 'Cancel' | translate | async }}</ion-button>
    </ion-buttons>
    <ion-buttons slot="end">
      <ion-button fill="solid" color="primary" (click)="assign(selectedInstance)">{{ 'Assign' | translate | async }}</ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-footer>
