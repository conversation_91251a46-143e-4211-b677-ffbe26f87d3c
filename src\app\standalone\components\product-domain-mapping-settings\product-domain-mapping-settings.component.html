@for (domain of productOrgDomains; track domain) {
  <ion-card class="content-card">
    @if (!domain.isDeleted) {
      <ion-row>
        <ion-col size="9">
          <div class="inner-container">
            {{ 'People with the domain @' | translate | async }}
            <div class="input-container">
              <ion-input [placeholder]="'Domain' | translate | async" (ionChange)="manageChange($event, domain, 'domain')" [value]="domain.domainName" type="text"></ion-input>
            </div>
            {{ 'will receive the role' | translate | async }}
            <div class="user-roles-container">
              <ion-select [placeholder]="'Role' | translate | async" (ionChange)="manageChange($event, domain, 'role')" [value]="domain.roleId" interface="popover">
                @for (role of filteredRoles; track role) {
                  <ion-select-option [value]="role.id"> {{ (role.name ?? '') | translate | async }} </ion-select-option>
                }
              </ion-select>
            </div>
          </div>
        </ion-col>
        <ion-col size="3" class="remove-button-col">
          <div class="remove-button-container">
            <mat-icon (click)="removeProductDomain(domain)">remove_circle_outline</mat-icon>
          </div>
        </ion-col>
      </ion-row>
    }
  </ion-card>
}
<ion-col size="3" class="add-button-col">
  <div class="add-button-container">
    <mat-icon (click)="addNewProductDomain()">add_circle_outline</mat-icon>
    <div class="button-text">{{ 'Add domain' | translate | async }}</div>
  </div>
</ion-col>
