import { After<PERSON>iewInit, ChangeDetectorRef, Component, ElementRef, forwardRef, Input, NgZone, On<PERSON>est<PERSON>, ViewChild } from '@angular/core';
import { NG_VALIDATORS, NG_VALUE_ACCESSOR, UntypedFormControl, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IGoogleMapsAddress } from '@app/core/contracts/contract';
import { GoogleMapsService } from '@app/core/services/google-maps.service';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { NgClass, AsyncPipe } from '@angular/common';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-google-maps-autocomplete',
    templateUrl: './google-maps-autocomplete.component.html',
    styleUrls: ['./google-maps-autocomplete.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => GoogleMapsAutcompleteComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => GoogleMapsAutcompleteComponent),
        },
    ],
    imports: [IonicModule, NgClass, FormsModule, ReactiveFormsModule, TextInputControlComponent, AsyncPipe, TranslatePipe]
})
export class GoogleMapsAutcompleteComponent extends BaseControlComponent implements AfterViewInit, OnDestroy {
  @Input() toolTip!: string;
  @Input() placeHolder!: string;
  @Input() override label!: string;
  @Input() backgroundColor = '#181818';
  @Input() itemBackgroundColor = '';
  @Input() identifierText: string;
  @Input() sectionId: string;
  @Input() featureType!: string | undefined;
  @Input() showSelected = false;
  @Input() sidePanelPadding = false;
  @ViewChild('postalCode', { read: ElementRef }) postalCode: ElementRef;

  googleAutocomplete: google.maps.places.AutocompleteService;
  placesService: google.maps.places.PlacesService;
  geocoder: google.maps.Geocoder;
  componentDestroyed$: Subject<boolean> = new Subject();
  autocomplete: { input: string } = { input: '' };
  autocompleteItems: any[] = [];
  addressForm: UntypedFormGroup;
  formValueChanges$: Subscription;
  constructor(
    public zone: NgZone,
    private cdr: ChangeDetectorRef,
    private systemPropertiesService: SystemPropertiesService,
    private googleMapsService: GoogleMapsService
  ) {
    super();
  }

  get hideCity() {
    return this.featureType === 'User Manager';
  }

  ngAfterViewInit() {
    this.registerGoogle();
    this.createForm();
  }

  createForm() {
    if (this.featureType === 'Organization Manager') {
      const i1 = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('AddressLine1'));
      const i2 = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('City'));
      const i5 = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('Province'));
      const i6 = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('Country'));
      const i7 = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('PostalCode'));

      this.addressForm = new UntypedFormGroup({
        addressLine1: new UntypedFormControl(i1?.value),
        city: new UntypedFormControl(i2?.value),
        country: new UntypedFormControl(i6?.value),
        state: new UntypedFormControl(i5?.value),
        zip: new UntypedFormControl(i7?.value),
      });

      this.addressForm.get('addressLine1')?.disable();
      this.addressForm.get('city')?.disable();
      this.addressForm.get('country')?.disable();
      this.addressForm.get('state')?.disable();
      this.addressForm.get('zip')?.disable();
    }

    if (
      this.featureType === 'Internal' ||
      this.featureType === 'Learning Objects' ||
      this.featureType === 'Marketing Objects' ||
      this.featureType === 'Training Objects' ||
      this.featureType === 'Landing Pages'
    ) {
      const i1 = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('AddressLine1'));
      const i2 = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('City'));
      const i5 = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('Province'));
      const i6 = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('Country'));
      const i7 = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('PostalCode'));

      this.addressForm = new UntypedFormGroup({
        addressLine1: new UntypedFormControl(i1?.value),
        city: new UntypedFormControl(i2?.value),
        country: new UntypedFormControl(i6?.value),
        state: new UntypedFormControl(i5?.value),
        zip: new UntypedFormControl(i7?.value),
      });

      this.addressForm.get('addressLine1')?.disable();
      this.addressForm.get('city')?.disable();
      this.addressForm.get('country')?.disable();
      this.addressForm.get('state')?.disable();
      this.addressForm.get('zip')?.disable();
    }

    if (this.featureType === 'User Manager') {
      const i1 = this.systemPropertiesService.userProperties.find(x => x.key.includes('address'));
      const i2 = this.systemPropertiesService.userProperties.find(x => x.key.includes('stateorprovince'));
      const i3 = this.systemPropertiesService.userProperties.find(x => x.key.includes('country'));
      const i4 = this.systemPropertiesService.userProperties.find(x => x.key.includes('postalcode'));

      this.addressForm = new UntypedFormGroup({
        addressLine1: new UntypedFormControl(i1?.value),
        country: new UntypedFormControl(i3?.value),
        state: new UntypedFormControl(i2?.value),
        zip: new UntypedFormControl(i4?.value),
      });

      this.addressForm.get('addressLine1')?.disable();
      this.addressForm.get('country')?.disable();
      this.addressForm.get('state')?.disable();
      this.addressForm.get('zip')?.disable();
    }
    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    if (!this.addressForm) {
      return;
    }

    this.formValueChanges$ = this.addressForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      if (this.featureType === 'Organization Manager') {
        const i1 = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('AddressLine1'));
        this.systemPropertiesService.organizationProperties[i1].value = this.addressForm.controls.addressLine1.value;
        const i2 = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('City'));
        this.systemPropertiesService.organizationProperties[i2].value = this.addressForm.controls.city.value;
        const i5 = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('Province'));
        this.systemPropertiesService.organizationProperties[i5].value = this.addressForm.controls.state.value;
        const i6 = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('Country'));
        this.systemPropertiesService.organizationProperties[i6].value = this.addressForm.controls.country.value;
        const i7 = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('PostalCode'));
        this.systemPropertiesService.organizationProperties[i7].value = this.addressForm.controls.zip.value;
      }

      if (
        this.featureType === 'Internal' ||
        this.featureType === 'Learning Objects' ||
        this.featureType === 'Marketing Objects' ||
        this.featureType === 'Training Objects' ||
        this.featureType === 'Landing Pages'
      ) {
        const i1 = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('AddressLine1'));
        this.systemPropertiesService.instanceProperties[i1].value = this.addressForm.controls.addressLine1.value;
        const i2 = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('City'));
        this.systemPropertiesService.instanceProperties[i2].value = this.addressForm.controls.city.value;
        const i5 = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('Province'));
        this.systemPropertiesService.instanceProperties[i5].value = this.addressForm.controls.state.value;
        const i6 = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('Country'));
        this.systemPropertiesService.instanceProperties[i6].value = this.addressForm.controls.country.value;
        const i7 = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('PostalCode'));
        this.systemPropertiesService.instanceProperties[i7].value = this.addressForm.controls.zip.value;
      }

      if (this.featureType === 'User Manager') {
        const i1 = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('address'));
        this.systemPropertiesService.userProperties[i1].value = this.addressForm.controls.addressLine1.value;
        const i2 = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('stateorprovince'));
        this.systemPropertiesService.userProperties[i2].value = this.addressForm.controls.state.value;
        const i3 = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('country'));
        this.systemPropertiesService.userProperties[i3].value = this.addressForm.controls.country.value;
        const i4 = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('postalcode'));
        this.systemPropertiesService.userProperties[i4].value = this.addressForm.controls.zip.value;
      }
      this.systemPropertiesService.reload$.next(this.sectionId);
    });
  }

  setFormControlValues() {
    this.formValueChanges$.unsubscribe();
    if (this.featureType === 'Organization Manager') {
      const i1 = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('AddressLine1'));
      const i2 = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('City'));
      const i5 = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('Province'));
      const i6 = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('Country'));
      const i7 = this.systemPropertiesService.organizationProperties.find(x => x.key.includes('PostalCode'));

      this.addressForm.controls.addressLine1.setValue(i1?.value);
      this.addressForm.controls.city.setValue(i2?.value);
      this.addressForm.controls.country.setValue(i6?.value);
      this.addressForm.controls.state.setValue(i5?.value);
      this.addressForm.controls.zip.setValue(i7?.value);
    }

    if (
      this.featureType === 'Internal' ||
      this.featureType === 'Learning Objects' ||
      this.featureType === 'Marketing Objects' ||
      this.featureType === 'Training Objects' ||
      this.featureType === 'Landing Pages'
    ) {
      const i1 = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('AddressLine1'));
      const i2 = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('City'));
      const i5 = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('Province'));
      const i6 = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('Country'));
      const i7 = this.systemPropertiesService.instanceProperties.find(x => x.key.includes('PostalCode'));

      this.addressForm.controls.addressLine1.setValue(i1?.value);
      this.addressForm.controls.city.setValue(i2?.value);
      this.addressForm.controls.country.setValue(i6?.value);
      this.addressForm.controls.state.setValue(i5?.value);
      this.addressForm.controls.zip.setValue(i7?.value);
    }

    if (this.featureType === 'User Manager') {
      const i1 = this.systemPropertiesService.userProperties.find(x => x.key.includes('address'));
      const i2 = this.systemPropertiesService.userProperties.find(x => x.key.includes('stateorprovince'));
      const i3 = this.systemPropertiesService.userProperties.find(x => x.key.includes('country'));
      const i4 = this.systemPropertiesService.userProperties.find(x => x.key.includes('postalcode'));

      this.addressForm.controls.addressLine1.setValue(i1?.value);
      this.addressForm.controls.country.setValue(i3?.value);
      this.addressForm.controls.state.setValue(i2?.value);
      this.addressForm.controls.zip.setValue(i4?.value);
    }
    this.subscribeToFormChanges();
  }

  registerGoogle() {
    this.googleMapsService.registerGoogleAsync().then(() => {
      this.googleAutocomplete = new google.maps.places.AutocompleteService();
      this.geocoder = new google.maps.Geocoder();
      this.placesService = new google.maps.places.PlacesService(this.postalCode.nativeElement);
    });
  }

  updateSearchResults() {
    this.autocompleteItems = [];

    if (this.autocomplete.input === '') {
      return;
    }

    this.googleSearchByAddress();
  }

  googleSearchByAddress() {
    if (!this.googleAutocomplete) {
      this.registerGoogle();
    }

    this.googleAutocomplete.getPlacePredictions(
      {
        input: this.autocomplete.input,
        types: [], // 'establishment' / 'address' / 'geocode'
      },
      (predictions: any) => {
        this.zone.run(() => {
          if (!predictions) {
            this.cdr.markForCheck();
            return;
          }

          predictions.forEach((prediction: any) => {
            if (prediction.types.indexOf('establishment') === -1 && prediction.types.indexOf('point_of_interest') === -1) {
              this.autocompleteItems.push(prediction);
            }
          });
          this.cdr.markForCheck();
        });
      }
    );
  }

  async getAddressByPlaceId(placeId: any) {
    return new Promise((resolve, reject) => {
      const address: IGoogleMapsAddress = {
        addressLine1: '',
        addressLine2: '',
        suburb: '',
        city: '',
        province: '',
        postalCode: '',
        country: '',
      };

      if (!placeId) {
        reject('placeId not provided');
      }

      try {
        this.placesService.getDetails(
          {
            placeId,
            fields: ['address_components', 'geometry'],
          },
          (details: any) => {
            address.lat = details.geometry.location.lat();
            address.long = details.geometry.location.lng();
            details?.address_components?.forEach((entry: any) => {
              if (entry.types?.[0] === 'street_number') {
                address.addressLine1 = entry.long_name;
              } else if (entry.types?.[0] === 'route') {
                address.addressLine1 = address.addressLine1 + ' ' + entry.long_name;
              } else if (entry.types?.[0]?.startsWith('sublocality')) {
                address.suburb = entry.long_name;
              } else if (entry.types?.[0] === 'locality') {
                address.city = entry.long_name;
              } else if (entry.types?.[0]?.startsWith('administrative')) {
                address.province = entry.long_name;
              } else if (entry.types?.[0] === 'country') {
                address.country = entry.long_name;
              } else if (entry.types?.[0] === 'postal_code') {
                address.postalCode = entry.long_name;
              }
            });

            // postal code not set, get postal code using reverse geocoding
            if (!address.postalCode) {
              const latlng = {
                lat: details.geometry.location.lat(),
                lng: details.geometry.location.lng(),
              };

              this.geocoder.geocode({ location: latlng }, (results: google.maps.GeocoderResult[], status: google.maps.GeocoderStatus) => {
                if (status === 'OK') {
                  if (results[0]) {
                    address.postalCode = results[0].address_components.find((a: any) => a.types[0] === 'postal_code')?.long_name;
                  }
                  return resolve(address);
                } else {
                  return resolve(address);
                }
              });
            } else {
              return resolve(address);
            }
          }
        );
      } catch (e) {
        reject(e);
      }
    });
  }

  async selectSearchResult(item: any) {
    const placeid = item.place_id;

    await this.getAddressByPlaceId(placeid).then((res: any) => {
      this.autocompleteItems = [];
      this.autocomplete = { input: '' };
      if (this.featureType === 'Organization Manager') {
        const i1 = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('AddressLine1'));
        this.systemPropertiesService.organizationProperties[i1].value = res.addressLine1;
        const i2 = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('AddressLine2'));
        this.systemPropertiesService.organizationProperties[i2].value = res.addressLine2;
        const i3 = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('Suburb'));
        this.systemPropertiesService.organizationProperties[i3].value = res.suburb;
        const i4 = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('City'));
        this.systemPropertiesService.organizationProperties[i4].value = res.city;
        const i5 = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('Province'));
        this.systemPropertiesService.organizationProperties[i5].value = res.province;
        const i6 = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('Country'));
        this.systemPropertiesService.organizationProperties[i6].value = res.country;
        const i7 = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('PostalCode'));
        this.systemPropertiesService.organizationProperties[i7].value = res.postalCode;
        const i8 = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('Latitude'));
        this.systemPropertiesService.organizationProperties[i8].value = res.lat.toString();
        const i9 = this.systemPropertiesService.organizationProperties.findIndex(x => x.key.includes('Longitude'));
        this.systemPropertiesService.organizationProperties[i9].value = res.long.toString();
      }

      if (
        this.featureType === 'Internal' ||
        this.featureType === 'Learning Objects' ||
        this.featureType === 'Marketing Objects' ||
        this.featureType === 'Training Objects' ||
        this.featureType === 'Landing Pages'
      ) {
        const i1 = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('AddressLine1'));
        this.systemPropertiesService.instanceProperties[i1].value = res.addressLine1;
        const i2 = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('AddressLine2'));
        this.systemPropertiesService.instanceProperties[i2].value = res.addressLine2;
        const i3 = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('Suburb'));
        this.systemPropertiesService.instanceProperties[i3].value = res.suburb;
        const i4 = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('City'));
        this.systemPropertiesService.instanceProperties[i4].value = res.city;
        const i5 = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('Province'));
        this.systemPropertiesService.instanceProperties[i5].value = res.province;
        const i6 = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('Country'));
        this.systemPropertiesService.instanceProperties[i6].value = res.country;
        const i7 = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('PostalCode'));
        this.systemPropertiesService.instanceProperties[i7].value = res.postalCode;
        const i8 = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('Latitude'));
        this.systemPropertiesService.instanceProperties[i8].value = res.lat.toString();
        const i9 = this.systemPropertiesService.instanceProperties.findIndex(x => x.key.includes('Longitude'));
        this.systemPropertiesService.instanceProperties[i9].value = res.long.toString();
      }

      if (this.featureType === 'User Manager') {
        const i1 = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('address'));
        this.systemPropertiesService.userProperties[i1].value = res.addressLine1 + ' ' + res.addressLine2;
        const i2 = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('stateorprovince'));
        this.systemPropertiesService.userProperties[i2].value = res.province;
        const i3 = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('country'));
        this.systemPropertiesService.userProperties[i3].value = res.country.toString();
        const i4 = this.systemPropertiesService.userProperties.findIndex(x => x.key.includes('postalcode'));
        this.systemPropertiesService.userProperties[i4].value = res.postalCode.toString();
      }

      this.systemPropertiesService.reload$.next(this.sectionId);
    });

    this.cdr.markForCheck();
    this.setFormControlValues();
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
