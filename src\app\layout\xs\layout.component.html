<div class="top-nav">
  <img class="logo" routerLink="/splash" src="/assets/images/EFLogo_Black.png" />
  <div class="controls">
    <div class="control-persona">
      @if (!authService.guestUserContext) {
        <div>
          @if (authService.userContext) {
            <div class="top-bar-person-name">{{ authService?.userContext?.fullName }}</div>
          }
          @if (authService.user && authService.userContext) {
            <div class="top-bar-org-name">
              @for (o of organizations; track o; let last = $last) {
                <span>{{ o.name }}{{ !last ? ',' : '' }}</span>
              }
            </div>
          }
        </div>
      }
      @if (authService.guestUserContext) {
        <div style="cursor: pointer">
          @if (authService.userContext) {
            <div class="top-bar-person-name">{{ authService?.userContext?.fullName }}</div>
          }
          @if (authService.guestUserContext && !authService.user) {
            <div class="top-bar-person-name">{{ 'Browsing As Guest' | translate | async }}</div>
          }
          @if (authService.guestUserContext && authService.user) {
            <div class="top-bar-person-name" (click)="removeBrowsingAs()">{{ 'Browsing As' | translate | async }}</div>
          }
          @if (authService.guestUserContext && authService.user) {
            <div class="top-bar-org-name" (click)="removeBrowsingAs()">
              {{ authService?.guestUserContext?.browsingAs }}
            </div>
          }
        </div>
      }
    </div>
    @if (authService.user) {
      <div class="control-buttons">
        <div>
          @if (!notificationService.checkNotRead()) {
            <mat-icon class="notification-button" svgIcon="notification" (click)="toggleNotifications()"></mat-icon>
          }
          @if (notificationService.checkNotRead()) {
            <mat-icon class="notification-button" svgIcon="notification-alert" (click)="toggleNotifications()"></mat-icon>
          }
          <mat-icon class="settings-button" svgIcon="settings" (click)="presentSettingsPopover($event)"></mat-icon>
        </div>
        @if (authService.user) {
          <div
            class="join button"
            matTooltip="{{ 'Join a new class or an organization.' | translate | async }}"
            [attr.aria-label]="'Join a new class or an organization.' | translate | async"
            (click)="openJoin()">
            {{ 'Join' | translate | async }}
          </div>
        }
        @if (!authService.user) {
          <div class="login button" (click)="openAuth()">{{ 'Login' | translate | async }}</div>
        }
      </div>
    }
    @if (!authService.user) {
      <div class="control-buttons">
        @if (!authService.user) {
          <div class="login button" (click)="openAuth()">{{ 'Login' | translate | async }}</div>
        }
      </div>
    }
  </div>
</div>
@if (showSearch) {
  <ion-toolbar class="searchbar-container" color="primary">
    <ion-searchbar
      placeholder="{{ 'SITE_SEARCH' | translate | async }}"
      class="searchbar"
      showCancelButton="focus"
      cancelButtonText="{{ 'CANCEL' | translate | async }}"
      debounce="1000"
      inputmode="text"
      #globalSearchText
      (ionChange)="setGlobalSearchValueXS(globalSearchText.value)"></ion-searchbar>
  </ion-toolbar>
}

<ion-menu class="notification-menu" side="end" menuId="notifications" contentId="main">
  <ion-list style="height: 100%; background-color: transparent">
    <ion-content id="notificationsList" [scrollEvents]="true" (ionScroll)="notificationListScrollEvent()" style="height: 100%; background-color: transparent">
      @for (notification of notificationService.getBellNotifications(); track notification) {
        <ion-item class="item-background">
          <app-badge-notification
            [iconAssetId]="notification?.notificationIcon"
            [messageText]="notification?.notificationText"
            [hasRead]="notification?.hasRead"
            (remove)="removeNotification(notification.id)">
          </app-badge-notification>
        </ion-item>
      }
      @if (notificationService.getBellNotifications()?.length === 0) {
        <ion-item class="item-background">
          <div class="no-notifications-popup">
            <div class="close-icon">
              <mat-icon style="cursor: pointer" svgIcon="close-solid" (click)="clearPopup()"></mat-icon>
            </div>
            <div class="inner">
              <img src="https://ef-staging-f5esbhcphbhcg0fz.z03.azurefd.net/v1/asset/d0855d40-eebe-44ee-9178-fa3a4f0f853a/content" />
              <p>{{ "No notifications, you're all caught up!" | translate | async }}</p>
            </div>
          </div>
        </ion-item>
      }
    </ion-content>
  </ion-list>
</ion-menu>
<ion-tabs id="main" [ngClass]="{ 'bottom-nav-bar-tablet': this.layoutService.isTablet }" class="bottom-nav-bar">
  <ion-tab-bar slot="bottom" color="secondary">
    <ion-tab-button
      [routerLink]="['/my-journey']"
      routerLinkActive="active"
      (click)="!authService.user && authService.guestUserContext ? openLockedModalOnClick() : openFeature('my-journey')"
      [ngClass]="{ active: featureClicked === 'my-journey' }"
      class="menu-item">
      <mat-icon svgIcon="journey"></mat-icon>
      <ion-label>{{ 'Journey' | translate | async }}</ion-label>
    </ion-tab-button>
    <ion-tab-button [routerLink]="['/library']" routerLinkActive="active" (click)="openFeature('library')" [ngClass]="{ active: featureClicked === 'library' }" class="menu-item">
      <mat-icon svgIcon="library"></mat-icon>
      <ion-label>{{ 'Library' | translate | async }}</ion-label>
    </ion-tab-button>
    <ion-tab-button (click)="toggleSearchbar()" [ngClass]="{ active: featureClicked === 'library' }" class="menu-item">
      <mat-icon svgIcon="search"></mat-icon>
      <ion-label>{{ 'Search' | translate | async }}</ion-label>
    </ion-tab-button>
    <ion-tab-button [routerLink]="['/workspace']" routerLinkActive="active" (click)="openFeature('workspace')" [ngClass]="{ active: featureClicked === 'workspace' }" class="menu-item">
      <mat-icon svgIcon="workspace"></mat-icon>
      <ion-label>{{ 'Workspace' | translate | async }}</ion-label>
    </ion-tab-button>
    @if (authService.userContext?.canManage && authService.guestUserContext === null) {
      <ion-tab-button [routerLink]="['/admin']" routerLinkActive="active" (click)="selectedTab('admin')" [ngClass]="{ active: featureClicked === 'admin' }" class="menu-item">
        <mat-icon svgIcon="admin"></mat-icon>
        <ion-label>{{ 'Admin' | translate | async }}</ion-label>
      </ion-tab-button>
    }
  </ion-tab-bar>
</ion-tabs>
