import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { INetworkOrganization } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { PopoverController, IonicModule } from '@ionic/angular';
import { Subject } from 'rxjs';
import { MatIcon } from '@angular/material/icon';
import { MatAccordion, MatExpansionPanel, MatExpansionPanelHeader } from '@angular/material/expansion';
import { AddSearchModalComponent } from '@app/standalone/modals/add-search-modal/add-search-modal.component';

@Component({
    selector: 'app-network-organization-expansion-accordion',
    templateUrl: './network-organization-expansion-accordion.component.html',
    styleUrls: ['./network-organization-expansion-accordion.component.scss'],
    imports: [IonicModule, MatIcon, MatA<PERSON>rdion, Mat<PERSON>xpansionPanel, MatExpansionPanelHeader, AsyncPipe, TranslatePipe]
})
export class NetworkOrganizationExpansionAccordionComponent implements OnInit, OnDestroy {
  @Input() id: string | null | undefined;
  @Input() type: string;
  networkOrganizations: INetworkOrganization[] = [];
  currentAmount = 0;
  getAmount = 25;
  moreResults = false;
  isParentPanelClosed = false;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private dataService: DataService,
    private popoverController: PopoverController
  ) {}

  ngOnInit() {
    if (this.id) {
      this.getNetworkOrganizationsById(this.id, false);
    }
  }

  getNetworkOrganizationsById(id: string, loadMore: boolean) {
    if (id !== undefined && this.id) {
      this.dataService.getNetworkOrganizationsById(this.id, this.type, this.currentAmount, this.getAmount).subscribe((networkOrganizations: INetworkOrganization[]) => {
        if (networkOrganizations.length > 0) {
          //OnLoadMoreData
          if (!loadMore) {
            this.networkOrganizations = networkOrganizations;
            this.currentAmount += networkOrganizations.length;
          } else {
            networkOrganizations.forEach(networkOrg => {
              this.networkOrganizations = [...this.networkOrganizations, networkOrg];
            });
            this.currentAmount += networkOrganizations.length;
          }

          if (networkOrganizations.length < this.getAmount) {
            this.moreResults = false;
          } else {
            this.moreResults = true;
          }
        }
      });
    }
  }

  openGroup(panelState: boolean) {
    this.isParentPanelClosed = panelState;
  }

  removeNetworkOrg(org: INetworkOrganization) {
    if (this.id) {
      this.dataService.removeNetworkOrganizationById(this.id, org.organizationId).subscribe(
        res => {
          if (res) {
            const index = this.networkOrganizations.findIndex(x => x.organizationId === org.organizationId);
            this.networkOrganizations.splice(index, 1);
          }
        },
        err => {
          console.error(err.message);
        }
      );
    }
  }

  async addSearchModalOpen(event: any) {
    const popover = await this.popoverController.create({
      component: AddSearchModalComponent,
      cssClass: 'add-search-modal',
      componentProps: { linkTypeName: 'Organizations' },
      event: event,
      side: 'bottom',
    });

    popover.onDidDismiss().then(result => {
      if (result.data) {
        const newOrg: INetworkOrganization = {
          organizationId: result.data.id,
          name: result.data.name,
        };
        this.addNetworkOrg(newOrg);
      }
    });

    await popover.present();
  }

  addNetworkOrg(org: INetworkOrganization) {
    if (this.id) {
      this.dataService.addNetworkOrganizationById(this.id, org).subscribe(
        res => {
          if (res) {
            this.networkOrganizations.unshift(org);
          }
        },
        err => {
          console.error(err.message);
        }
      );
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
