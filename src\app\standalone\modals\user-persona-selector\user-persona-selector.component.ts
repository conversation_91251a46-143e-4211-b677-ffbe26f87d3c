import { Component, OnDestroy, OnInit } from '@angular/core';
import { ModalController, IonicModule } from '@ionic/angular';
import { ITag } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { AuthService } from '@app/core/services/auth-service';
import { Subject, takeUntil } from 'rxjs';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-user-persona-selector',
    templateUrl: './user-persona-selector.component.html',
    styleUrls: ['./user-persona-selector.component.scss'],
    imports: [IonicModule, AsyncPipe, TranslatePipe]
})
export class UserPersonaSelectorComponent implements OnInit, OnDestroy {
  tags: ITag[] = [];
  tagTreeLevel = 2;
  componentDestroyed$: Subject<boolean> = new Subject();
  userMessage?: string | undefined;
  userName: string | undefined = '';
  finished: boolean | undefined = false;
  icon: string | undefined = '';
  finishMessage: string | undefined = '';
  tagId: string | undefined = '';
  userLocation: string | undefined = '';
  selectedUserId: string | undefined = '';
  highSchoolTags: string[] = [];
  selectedTags: ITag[] = [];
  rowTitles: string[] = [];

  constructor(
    private modalController: ModalController,
    private dataService: DataService,
    private authService: AuthService
  ) {}

  ngOnInit() {
    this.getUserPersonaTags();
    if (!this.selectedUserId) {
      this.userName = this.authService.userContext?.fullName?.slice(0, this.authService.userContext?.fullName.indexOf(' '));
      this.userLocation = this.authService.userContext?.country ?? '';
      this.finishMessage = `Ok, ${this.userName} it looks like you're a `;
    }
  }

  onFinish() {
    const isDone = true;
    if (this.authService.userContext && !this.selectedUserId) {
      const userContext = this.authService.userContext;
      userContext.hasPersonaTags = true;
      this.authService.userContext = userContext;
      localStorage.setItem('user_context', JSON.stringify(userContext));
    }
    this.modalController.dismiss(isDone);
    window.location.reload();
    this.rowTitles = [];
  }

  skip() {
    if (this.authService.userContext && !this.selectedUserId) {
      const userContext = this.authService.userContext;
      userContext.hasPersonaTags = true;
      this.authService.userContext = userContext;
    }
    this.modalController.dismiss(true);
  }

  onBack() {
    this.dataService
      .deleteUserPersonaTag(this.tagId, this.selectedUserId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        this.highSchoolTags = [];
        this.rowTitles.pop();
        this.getUserPersonaTags();
      });
  }

  getUserPersonaTags() {
    this.dataService
      .getUserPersonaTags(this.selectedUserId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(tags => {
        this.setData(tags);
      });
  }

  setData(tags: ITag[]) {
    const currentParent = tags[tags.length === 0 ? tags.length : tags.length - 1];
    this.tagTreeLevel = currentParent.treeLevel;
    const tagParent = currentParent.parent;

    if (this.userLocation === 'Canada' && (tagParent?.name === 'High School Educator' || tagParent?.name === 'School District Lead')) {
      tags = tags.filter(tag => tag.name !== 'CTE Instructor' && tag.name !== 'CTE Director');
    }
    if (this.userLocation === 'United States' && (tagParent?.name === 'High School Educator' || tagParent?.name === 'School District Lead')) {
      tags = tags.filter(tag => tag.name !== 'SHSM Instructor' && tag.name !== 'OYAP Educator' && tag.name !== 'TAS Teacher' && tag.name !== 'OYAP Recruiter' && tag.name !== 'SHSM Lead');
    }

    if (tagParent) {
      this.tagNameCheck(currentParent);
    }

    this.tagId = tagParent?.id || '';

    this.tags = tags.map(tag => {
      if (this.selectedTags?.some(x => x.id === tag.id)) {
        this.selectTag(tag);
      }
      return tag;
    });

    this.finished = false;
  }

  setRow(tag: ITag) {
    this.rowTitles.push(tag.name.replace(/( Educator| Lead)/, ''));
    this.dataService
      .updateUserPersonaTag(JSON.stringify(tag.id), this.selectedUserId)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(() => {
        if (!tag.inverseParent || tag.inverseParent.length === 0) {
          this.tagTreeLevel++;
          this.finished = true;
          this.tagNameCheck(tag as ITag);
          this.tagId = tag.parentId;
        } else {
          this.getUserPersonaTags();
        }
      });
  }

  selectTag(tag: ITag) {
    tag.hasUserTags = !tag.hasUserTags;

    if (tag.hasUserTags) {
      if (!this.selectedTags?.some(x => x.id === tag.id)) {
        this.selectedTags.push(tag);
      }
      this.dataService
        .updateUserPersonaTag(JSON.stringify(tag.id), this.selectedUserId)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          this.highSchoolTags.push(tag.name);
          let finishTagsMessage = '';
          if (this.rowTitles.length > 1) {
            for (let i = 0; i < this.highSchoolTags.length; i++) {
              if (
                !finishTagsMessage.includes('and a') &&
                (this.selectedTags.find(x => x.name === this.highSchoolTags[i])?.parent?.name === 'High School Educator' || this.highSchoolTags[i].includes('Teacher'))
              ) {
                finishTagsMessage = 'and a ' + this.highSchoolTags[i];
              } else if (this.selectedTags.find(x => x.name === this.highSchoolTags[i])?.parent?.name === 'High School Educator' || this.highSchoolTags[i].includes('Teacher')) {
                finishTagsMessage = finishTagsMessage + ' and a ' + this.highSchoolTags[i];
              } else if (!finishTagsMessage.includes('who teaches')) {
                finishTagsMessage = finishTagsMessage + ' who teaches ' + this.highSchoolTags[i];
              } else {
                finishTagsMessage = finishTagsMessage + ' and ' + this.highSchoolTags[i];
              }
            }
          } else {
            finishTagsMessage = this.highSchoolTags.join(' and a ');
          }
          this.finishMessage = `Ok ${this.userName}, it looks like you're a ${this.rowTitles.join(' ')} ${finishTagsMessage}.`;
          this.tagId = tag.parentId;
        });
    } else {
      this.selectedTags = this.selectedTags.filter(x => x.id !== tag.id);
      this.dataService
        .deleteUserPersonaTag(tag.id, this.selectedUserId)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          this.highSchoolTags = this.highSchoolTags.filter(x => x !== tag.name);
          let finishTagsMessage = '';
          if (this.rowTitles.length > 1) {
            for (let i = 0; i < this.highSchoolTags.length; i++) {
              if (
                !finishTagsMessage.includes('and a') &&
                (this.selectedTags.find(x => x.name === this.highSchoolTags[i])?.parent?.name === 'High School Educator' || this.highSchoolTags[i].includes('Teacher'))
              ) {
                finishTagsMessage = 'and a ' + this.highSchoolTags[i];
              } else if (this.selectedTags.find(x => x.name === this.highSchoolTags[i])?.parent?.name === 'High School Educator' || this.highSchoolTags[i].includes('Teacher')) {
                finishTagsMessage = finishTagsMessage + ' and a ' + this.highSchoolTags[i];
              } else if (!finishTagsMessage.includes('who teaches')) {
                finishTagsMessage = finishTagsMessage + ' who teaches ' + this.highSchoolTags[i];
              } else {
                finishTagsMessage = finishTagsMessage + ' and ' + this.highSchoolTags[i];
              }
            }
          } else {
            finishTagsMessage = this.highSchoolTags.join(' and a ');
          }
          this.finishMessage = `Ok ${this.userName}, it looks like you're a ${this.rowTitles.join(' ')} ${finishTagsMessage}.`;
        });
    }
  }

  setTag(tag: ITag) {
    this.selectTag(tag);
  }

  next() {
    this.tagTreeLevel++;
    this.finished = true;
  }

  tagNameCheck(tag: ITag) {
    const findTerm = (term: string) => {
      if (tag.parent?.name.toLocaleLowerCase().includes(term) && tag.parent?.name !== 'Persona') {
        return tag.parent?.name;
      } else if (tag.name.toLocaleLowerCase().includes(term) && tag.parent?.name === 'Persona') {
        return tag.name;
      } else {
        return '';
      }
    };
    const switchVar = tag.parent?.name === 'Persona' ? tag.name : tag.parent?.name;
    switch (switchVar) {
      case findTerm('student'):
        this.finishMessage = `Ok ${this.userName}, it looks like you're a ${tag.parent?.name} in ${tag.name}.`;
        this.userMessage = 'Cool, what grade?';
        break;
      case findTerm('school'):
        this.finishMessage = `Ok ${this.userName}, it looks like you're a School ${tag.name}.`;
        this.userMessage = 'What best describes you?';
        break;
      case findTerm('k-8 educator'):
        this.finishMessage = `Ok ${this.userName}, it looks like you're a K-8 ${tag.name}.`;
        this.userMessage = 'What best describes you?';
        break;
      case findTerm('cte instructor'):
        this.finishMessage = `Ok ${this.userName}, it looks like you're a High School CTE Instructor in the field of ${tag.name}.`;
        this.userMessage = 'What cluster?';
        break;
      case findTerm('shsm instructor'):
        this.finishMessage = `Ok ${this.userName}, it looks like you're a High School SHSM Instructor in the field of ${tag.name}.`;
        this.userMessage = 'Which programs?';
        break;
      case findTerm('technology teacher'):
        this.finishMessage = `Ok ${this.userName}, it looks like you're a Technology Teacher in the field of ${tag.name}.`;
        this.userMessage = 'Which programs?';
        break;
      case findTerm('tas teacher'):
        this.finishMessage = `Ok ${this.userName}, it looks like you're a High School TAS Teacher in the field of ${tag.name}.`;
        this.userMessage = 'Which programs?';
        break;
      case findTerm('postsecondary'):
        this.finishMessage = `Ok ${this.userName}, it looks like you work as a ${tag.name}.`;
        break;
      case findTerm('jobseeker'):
        this.finishMessage = `Ok ${this.userName}, it looks like you're a ${tag.name}.`;
        break;
      case findTerm('parent'):
        this.finishMessage = `Ok ${this.userName}, it looks like you're a ${tag.name}.`;
        break;
      case findTerm('company'):
        this.finishMessage = `Ok ${this.userName}, it looks like you work at a ${tag.name}.`;
        break;
      case findTerm('workforce'):
        this.finishMessage = `Ok ${this.userName}, it looks like you're a ${tag.parent?.name} at the ${tag.name} level.`;
        this.userMessage = 'OK, at what level?';
        break;
      default:
        this.finishMessage = ``;
        this.userMessage = '';
        break;
    }

    this.icon = tag.name.includes('student') ? 'student-icon' : tag.name.includes('parent') ? 'parent-icon' : tag.name.includes('educator') ? 'educators-icon' : 'public-stakeholder';
  }

  ngOnDestroy(): void {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
