<div class="org-networks-parent-container">
  @if (type === 'Organization Manager' || type === 'Network Manager') {
    <ion-button (click)="addSearchModalOpen($event)"> <mat-icon svgIcon="add"></mat-icon> {{ 'Add' | translate | async }} </ion-button>
    <mat-accordion multi>
      @for (org of networkOrganizations; track org) {
        <mat-expansion-panel (opened)="openGroup(true)" (closed)="openGroup(false)" hideToggle="true">
          <mat-expansion-panel-header class="expansion-panel-header">
            <div class="inner-panel">
              <div class="heading">{{ org.name }}</div>
              <div><ion-icon (click)="removeNetworkOrg(org)" color="primary" name="remove-circle-outline"></ion-icon></div>
            </div>
          </mat-expansion-panel-header>
        </mat-expansion-panel>
      }
      @if (moreResults && !isParentPanelClosed) {
        <div (click)="getNetworkOrganizationsById(id ?? '', true)" class="load-more">
          <ion-row>
            <ion-col size="12">
              <div>{{ 'Load More' | translate | async }}</div>
              <div><ion-icon name="chevron-down-outline"></ion-icon></div>
            </ion-col>
          </ion-row>
        </div>
      }
    </mat-accordion>
  }
</div>
