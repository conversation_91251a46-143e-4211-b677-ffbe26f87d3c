import { SelectionModel } from '@angular/cdk/collections';
import { Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { MatSort, MatSortHeader } from '@angular/material/sort';
import { MatTableDataSource, MatTable, MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, MatCell, MatHeaderRowDef, MatHeaderRow, MatRowDef, MatRow } from '@angular/material/table';
import { IResults } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { Subject, takeUntil } from 'rxjs';
import { IonicModule } from '@ionic/angular';
import { MatCheckbox } from '@angular/material/checkbox';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
  selector: 'app-results-table',
  templateUrl: './results-table.component.html',
  styleUrls: ['./results-table.component.scss'],
  standalone: true,
  imports: [IonicModule, MatTable, MatSort, MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCheckbox, MatCellDef, MatCell, MatSortHeader, MatHeaderRowDef, MatHeaderRow, MatRowDef, MatRow, AsyncPipe, TranslatePipe],
})
export class ResultsTableComponent implements OnInit, OnDestroy {
  @ViewChild('sort', { static: true }) sort: MatSort;
  @Input() id: string;
  @Input() instanceId: string;
  @Input() orgId: string;
  @Input() type: string | undefined;
  dataSource = new MatTableDataSource<IResults>();
  displayedColumns: string[] = ['select', 'name', 'progress', 'grade'];
  currentAmount = 0;
  getAmount = 25;
  moreResults = false;
  selection = new SelectionModel<any>(true, []);
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private dataService: DataService) {}

  ngOnInit() {
    this.getResultsTableById(this.id, false);
  }

  getResultsTableById(id: string, loadMore: boolean) {
    if (id !== undefined) {
      if (this.type === 'Accredited Package' || this.type === 'Modifiable Package') {
        this.dataService
          .getResultsTableByRowId(id, this.instanceId, this.orgId, this.currentAmount, this.getAmount)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe((rowUsers: IResults[]) => {
            if (rowUsers.length > 0) {
              //LoadMoreData
              if (!loadMore) {
                this.setDataSource(rowUsers);
                this.currentAmount += rowUsers.length;
              } else {
                rowUsers.forEach(user => {
                  this.dataSource.data = [...this.dataSource.data, user];
                });
                this.currentAmount += rowUsers.length;
              }

              if (rowUsers.length < this.getAmount) {
                this.moreResults = false;
              } else {
                this.moreResults = true;
              }
            }
          });
      } else {
        this.dataService
          .getResultsTableByInstanceId(id, this.currentAmount, this.getAmount)
          .pipe(takeUntil(this.componentDestroyed$))
          .subscribe((instanceUsers: IResults[]) => {
            if (instanceUsers.length > 0) {
              //LoadMoreData
              if (!loadMore) {
                this.setDataSource(instanceUsers);
                this.currentAmount += instanceUsers.length;
              } else {
                instanceUsers.forEach(user => {
                  this.dataSource.data = [...this.dataSource.data, user];
                });
                this.currentAmount += instanceUsers.length;
              }

              if (instanceUsers.length < this.getAmount) {
                this.moreResults = false;
              } else {
                this.moreResults = true;
              }
            }
          });
      }
    }
  }

  isAllSelected() {
    const numSelected = this.selection.selected.length;
    const numRows = this.dataSource.data.length;
    return numSelected === numRows;
  }

  masterToggle() {
    if (this.isAllSelected() === false) {
      this.dataSource.data.forEach(row => this.selection.select(row));
    } else {
      this.selection.clear();
    }
  }

  setDataSource(users: IResults[]) {
    this.dataSource = new MatTableDataSource(users);
    this.dataSource.sort = this.sort;
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
