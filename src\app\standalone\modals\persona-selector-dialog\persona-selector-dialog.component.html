<div class="container">
  <h6>{{ 'How are you browsing the platform?' | translate | async }}</h6>
  <label>{{ 'This helps us recommend content to you.' | translate | async }}</label>
  <div class="accordion-container">
    <ion-accordion-group (ionChange)="change($event)">
      @for (option of options; track option) {
        <ion-accordion value="{{ option.id }}" toggleIcon="none">
          <ion-item slot="header">
            <!-- <mat-icon class="icon" svgIcon="{{ option.icon }}"></mat-icon> -->
            <img class="icon" src="assets/images/{{ option.icon }}.png" />
            <h4>{{ option.name | translate | async }}</h4>
          </ion-item>
          @if (option.children.length > 0) {
            <ion-list slot="content">
              <div style="width: 100%; text-align: center">
                <p style="font-size: 14px; color: #aaa; margin-top: -10px; border-bottom: 1px #333 solid">{{ 'Great, tell us where...' | translate | async }}</p>
              </div>
              <ion-radio-group (ionChange)="change($event)">
                @for (child of option.children; track child) {
                  <ion-item>
                    <ion-radio value="{{ child.id }}"></ion-radio>
                    <ion-label>{{ child.name | translate | async }}</ion-label>
                  </ion-item>
                }
              </ion-radio-group>
            </ion-list>
          }
        </ion-accordion>
      }
    </ion-accordion-group>
  </div>
  <ion-grid style="width: 100%">
    <ion-row>
      <ion-col>
        <ion-button fill="solid" color="warning" expand="block" (click)="openAuth()">{{ 'Login' | translate | async }}</ion-button>
      </ion-col>
    </ion-row>
  </ion-grid>
</div>
