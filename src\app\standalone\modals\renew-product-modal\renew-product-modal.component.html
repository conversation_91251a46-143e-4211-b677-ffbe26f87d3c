<ion-grid class="grid-container">
  <app-renew-product
    (productRenew)="updateProduct($event)"
    [currentSubscriptionExpires]="currentSubscriptionExpires"
    [featureType]="featureType"
    [newProduct]="false"
    [networkOrgCount]="networkOrgCount"></app-renew-product>
  <ion-row><hr /></ion-row>
  <ion-row class="view-options-row">
    <a class="cancel-button" (click)="close()">{{ 'Cancel' | translate | async }}</a>
    <ion-button (click)="renewProduct()" [disabled]="!productRenew">{{ 'Finish' | translate | async }}</ion-button>
  </ion-row>
</ion-grid>
