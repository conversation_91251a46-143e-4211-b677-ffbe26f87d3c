import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { IUserClaimSearch, IUserRole } from '@app/core/contracts/contract';
import { DataService } from '@app/core/services/data-service';
import { ModalController, IonicModule } from '@ionic/angular';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { Subject } from 'rxjs';

@Component({
    selector: 'app-confirm-add-user-modal',
    templateUrl: './confirm-add-user-modal.component.html',
    styleUrls: ['./confirm-add-user-modal.component.scss'],
    imports: [IonicModule, AsyncPipe, TranslatePipe]
})
export class ConfirmAddUserModalComponent implements OnInit, OnDestroy {
  @Input() selectedUsers: IUserClaimSearch[] = [];
  @Input() name: string;
  @Input() type: string;
  userRoles: IUserRole[] = [];
  defaultRoleId: string;
  rolesDisabled = false;

  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(
    private modalController: ModalController,
    private dataService: DataService
  ) {}

  ngOnInit() {
    //--Ref: #38285-->
    //ExcludeRoles For ClassRooms
    // if (this.type === 'Modifiable Learning Container Pages' || this.type === 'Accredited Learning Container Pages') {
    //   this.rolesDisabled = true;
    // }

    this.getUserRoles();
  }

  getUserRoles() {
    if (!this.rolesDisabled) {
      this.dataService.getUserRoles().subscribe((userRoles: IUserRole[]) => {
        if (userRoles.length > 0) {
          this.userRoles = userRoles;
          this.defaultRoleId = '52dc3b0e-4d9e-40fc-adf5-40e813f7a055';
          this.selectedUsers.forEach(x => {
            if (!x.roleId) {
              x.roleId = this.defaultRoleId;
            }
          });
        }
      });
    }
  }

  roleSelected(event: any, selectedUserId: string) {
    if (event.detail.value) {
      const selectedUser = this.selectedUsers.find(x => x.id === selectedUserId);
      if (selectedUser != null) {
        selectedUser.roleId = event.detail.value;
      }
    }
  }

  addUser(event: any, selectedUser: IUserClaimSearch) {
    if (event.detail.checked) {
      this.selectedUsers.push(selectedUser);
    } else {
      const index = this.selectedUsers.findIndex(x => x.id === selectedUser.id);
      this.selectedUsers.splice(index, 1);
    }
  }

  close() {
    this.modalController.dismiss();
  }

  add() {
    this.modalController.dismiss(this.selectedUsers);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
