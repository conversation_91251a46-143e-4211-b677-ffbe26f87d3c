import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { UntypedFormBuilder, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ICommunicationBlock, ITag } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { DataService } from '@app/core/services/data-service';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { AsyncPipe } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { TextInputControlComponent } from '@app/standalone/components/text-input-control/text-input-control.component';
import { ContentQuillEditorComponent } from '../../content-quill-editor/content-quill-editor.component';
import { SelectOptionControlComponent } from '../../select-option-control/select-option-control.component';

@Component({
    selector: 'app-push-block',
    templateUrl: './push-block.component.html',
    styleUrls: ['./push-block.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, TextInputControlComponent, ContentQuillEditorComponent, SelectOptionControlComponent, AsyncPipe, TranslatePipe]
})
export class PushBlockComponent implements OnInit, OnDestroy {
  @Input() communicationId: string;
  @Input() pushBlock: ICommunicationBlock | null;
  @Output() communicationBlockUpdated: EventEmitter<ICommunicationBlock> = new EventEmitter();
  @Output() formValidityChanged: EventEmitter<boolean> = new EventEmitter();
  pushBlockForm: UntypedFormGroup;
  formValueChanges$: Subscription;
  backgroundColor = '#181818';
  hideQuillPersonalize = true;
  behaviours: KeyValue[];
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    private dataService: DataService,
    private formBuilder: UntypedFormBuilder
  ) {}

  ngOnInit() {
    this.getBehaviours();
  }

  getBehaviours() {
    this.dataService
      .getTagChildrenByParentName('Behaviours')
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(data => {
        if (data) {
          this.behaviours = (data as ITag[]).map(x => ({ id: x.id, value: x.name }) as KeyValue);
        }

        this.createForm();
      });
  }

  createForm() {
    this.pushBlockForm = this.formBuilder.group({
      title: [this.pushBlock?.title],
      subTitle: [this.pushBlock?.subTitle],
      message: [this.pushBlock?.message],
      behaviour: [this.pushBlock?.actionTypeId],
    });

    this.subscribeToFormChanges();
  }

  setMessageValue(quillData: any) {
    this.pushBlockForm.controls.message.setValue(quillData);
  }

  setObjectValues() {
    if (this.pushBlockForm.valid) {
      let communicationBlock = {
        id: this.pushBlock?.id,
        communicationId: this.communicationId,
        title: this.pushBlockForm.controls.title.value,
        subTitle: this.pushBlockForm.controls.subTitle.value,
        message: this.pushBlockForm.controls.message.value,
        actionTypeId: this.pushBlockForm.controls.behaviour.value,
        blockType: 'Push',
      } as ICommunicationBlock;

      //Merge
      if (this.pushBlock) {
        communicationBlock = { ...this.pushBlock, ...communicationBlock };
      }

      this.communicationBlockUpdated.emit(communicationBlock);
    }
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.pushBlockForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.formValidityChanged.emit(this.pushBlockForm.valid);
      this.setObjectValues();
    });
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
