<ion-content>
  <ion-grid>
    <ion-row>
      <ion-col class="info" size-md="6" size-xs="12">
        <div class="left-row-col">
          <ion-img src="\assets\images\Celebrate Icon.png"></ion-img>
          <div class="text">
            <h2 class="center-header">{{ 'Success!' | translate | async }}</h2>
        <ion-card-subtitle>
          {{ 'You have assigned this to' | translate | async }} <span class="white-text">{{ itemName }}</span>
        </ion-card-subtitle>
      </div>          
        </div>   
      </ion-col>
      <ion-col class="right-row-col left-mobile-col" size-md="6" size-xs="12">     
        <ion-buttons>
        <ion-button class="assign-button user-button" fill="solid" (click)="close('students')">{{ 'Add Users to class' | translate | async }}
        </ion-button>
        <ion-button class="assign-button" fill="solid" color="primary" (click)="close()">{{ 'Open' | translate | async }}
        </ion-button>   
      </ion-buttons>
      </ion-col>
    </ion-row>
  </ion-grid>
</ion-content>