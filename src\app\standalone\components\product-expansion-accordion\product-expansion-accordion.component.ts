import { DatePip<PERSON>, UpperCasePipe, AsyncPipe } from '@angular/common';
import { Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';
import { UntypedFormControl, UntypedFormGroup } from '@angular/forms';
import { MatChipListbox, MatChipOption } from '@angular/material/chips';
import { MatA<PERSON>rdion, MatExpansionPanel, MatExpansionPanelContent, MatExpansionPanelHeader } from '@angular/material/expansion';
import { MatTab, MatTabContent, MatTabGroup } from '@angular/material/tabs';
import { IOrganizationSearch, IOrganizationSsoAuthIn, IOrgPrivacyTypeIn, IProductJoinCodeSetting, IProductOrganization, IProductOrgDomain, IProductRenew, IRole } from '@app/core/contracts/contract';
import { ProductTabType } from '@app/core/enums/product-tab-type';
import { AlertService } from '@app/core/services/alert-service';
import { AuthService } from '@app/core/services/auth-service';
import { DataService } from '@app/core/services/data-service';
import { ProductHistoryService } from '@app/core/services/product-history.service';
import { OverlayEventDetail } from '@app/features-shared/row-instance/components/content/grid-view/thumbnail-styles/styles/image-background/image-background.component';
import { AddOrgProductModalComponent } from '@app/standalone/modals/add-org-product-modal/add-org-product-modal.component';
import { AddProductOrganizationModalComponent } from '@app/standalone/modals/add-product-org-modal/add-product-org-modal.component';
import { RenewProductModalComponent } from '@app/standalone/modals/renew-product-modal/renew-product-modal.component';
import { IonicModule, ModalController } from '@ionic/angular';
import { debounceTime, distinctUntilChanged, forkJoin, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/internal/operators/takeUntil';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { ConfirmationDialogComponent } from '../../modals/confirmation-dialog/confirmation-dialog.component';
import { JoinCodeComponent } from '../join-code/join-code.component';
import { OrganizationsHistoryComponent } from '../organizations-history/organizations-history.component';
import { ProductDetailsComponent } from '../product-details/product-details.component';
import { UsersTableComponent } from '../users-table/users-table.component';

@Component({
    selector: 'app-product-expansion-accordion',
    templateUrl: './product-expansion-accordion.component.html',
    styleUrls: ['./product-expansion-accordion.component.scss'],
    imports: [
        IonicModule,
        MatAccordion,
        MatExpansionPanel,
        MatExpansionPanelHeader,
        MatChipListbox,
        MatChipOption,
        JoinCodeComponent,
        MatExpansionPanelContent,
        MatTabGroup,
        MatTab,
        MatTabContent,
        ProductDetailsComponent,
        UsersTableComponent,
        OrganizationsHistoryComponent,
        UpperCasePipe,
        DatePipe,
        AsyncPipe,
        TranslatePipe,
    ]
})
export class ProductExpansionAccordionComponent implements OnInit, OnDestroy, OnChanges {
  @Input() id: string | null | undefined;
  @Input() featureType: string;
  @Input() searchFilter?: string;
  hasAccess = false;
  productOrganizations: IProductOrganization[] = [];
  productTabType = ProductTabType;
  productOrgDomainsIn: IProductOrgDomain[] = [];
  productJoinCodesIn: IProductJoinCodeSetting[] = [];
  orgSsoAuthIn: IOrganizationSsoAuthIn;
  productOrgPrivacyTypeIn: IOrgPrivacyTypeIn;
  componentDestroyed$: Subject<boolean> = new Subject();
  currentAmount = 0;
  getAmount = 25;
  moreResults = false;
  isParentPanelClosed = false;
  hasChanges = false;
  qrCode: string | undefined;
  searchForm: UntypedFormGroup;
  searchControl: UntypedFormControl;

  constructor(
    private dataService: DataService,
    private alertService: AlertService,
    private authService: AuthService,
    private productHistoryService: ProductHistoryService,
    private modalController: ModalController
  ) {}

  ngOnInit() {
    this.createFormControls();
    this.createForm();
    //UniqueForUserManager:
    if (this.id === undefined && this.featureType === 'User Manager') {
      this.id = '00000000-0000-0000-0000-000000000000';
    }

    if (this.id) {
      this.getProductOrganizationsById(this.id, false);
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['searchFilter'].currentValue) {
      this.productOrganizations = [];
      this.currentAmount = 0;

      if (this.id) {
        this.getProductOrganizationsById(this.id, false, this.searchFilter);
      }
    }
  }

  get isEfAdmin() {
    return this.authService.userContext?.canManage;
  }

  createFormControls() {
    this.searchControl = new UntypedFormControl('');
  }

  createForm() {
    this.searchForm = new UntypedFormGroup({
      searchControl: this.searchControl,
    });

    this.searchForm.controls.searchControl.valueChanges.pipe(debounceTime(700), distinctUntilChanged()).subscribe(() => {
      if (this.id) {
        this.currentAmount = 0;
        this.getAmount = 25;
        this.getProductOrganizationsById(this.id, false, this.searchControl.value);
      }
    });
  }

  subscriptionStatus(date: number): string {
    return this.productHistoryService.subscriptionStatus(date);
  }

  getProductOrgUserRoleById(organization: IProductOrganization) {
    if (organization != null) {
      const accessRoles: string[] = ['Administrator'];
      this.dataService.getProductOrgUserRoleById(organization.id).subscribe((orgUserRole: IRole) => {
        if (this.featureType === 'User Manager') {
          this.hasAccess = false;
        } else if (this.featureType === 'Product Manager') {
          this.hasAccess = true;
        } else if (this.featureType === 'Organization Manager' && this.authService.userContext?.canManage) {
          this.hasAccess = true;
        }
        if (orgUserRole != null) {
          organization.orgUserRoleName = orgUserRole.name;
          if (accessRoles.includes(organization.orgUserRoleName)) {
            this.hasAccess = true;
          } else {
            this.hasAccess = false;
          }
        }
      });
    }
    return organization.orgUserRoleName;
  }

  getNetworkProductOrgUserRoleById(organization: IProductOrganization) {
    if (organization != null) {
      const accessRoles: string[] = ['Administrator'];
      this.dataService.getNetworkProductOrgUserRoleById(organization.id).subscribe((orgUserRole: IRole) => {
        if (orgUserRole != null) {
          organization.orgUserRoleName = orgUserRole.name;
          if (accessRoles.includes(organization.orgUserRoleName)) {
            this.hasAccess = true;
          }
        }
      });
    }
  }

  getProductOrganizationsById(id: string, loadMore: boolean, searchFilter?: string) {
    if (id !== undefined && this.id) {
      this.dataService.getProductOrganizationsById(this.id, this.featureType, this.currentAmount, this.getAmount, searchFilter).subscribe((productOrganizations: IProductOrganization[]) => {
        if (productOrganizations.length > 0) {
          //OnLoadMoreData
          if (!loadMore) {
            this.productOrganizations = productOrganizations.map(
              x =>
                ({
                  ...x,
                  productJoinCodeSettings: (x.productJoinCodeSettings = []),
                }) as IProductOrganization
            );
            this.currentAmount += productOrganizations.length;
          } else {
            productOrganizations
              .map(x => ({ ...x, productJoinCodeSettings: (x.productJoinCodeSettings = []) }) as IProductOrganization)
              .forEach(org => {
                this.productOrganizations = [...this.productOrganizations, org];
              });
            this.currentAmount += productOrganizations.length;
          }

          if (productOrganizations.length < this.getAmount) {
            this.moreResults = false;
          } else {
            this.moreResults = true;
          }

          this.productOrganizations.forEach(organization => {
            organization.orgUserRoleName = this.getProductOrgUserRoleById(organization);
            if (organization.networkName) {
              this.getNetworkProductOrgUserRoleById(organization);
            }
          });
        }
      });
    }
  }

  async openModal(productOrganizationId: string) {
    const modal = await this.modalController.create({
      component: RenewProductModalComponent,
      cssClass: 'product-history-modal',
      componentProps: {
        featureType: this.featureType,
      },
      backdropDismiss: false,
    });

    modal.onDidDismiss().then((detail: any) => {
      if (detail?.data) {
        const renewProduct: IProductRenew = detail?.data;

        this.dataService.updateProductOrganizationSubscription(productOrganizationId, renewProduct.period, renewProduct.expiryDate).subscribe((value: any) => {
          if (value) {
            this.productOrganizations = [];
            this.currentAmount = 0;
            if (this.id) {
              this.getProductOrganizationsById(this.id, false);
            }
          }
        });
      }
    });

    return await modal.present();
  }

  saveProductOrgDomains(domains: IProductOrgDomain[]) {
    this.hasChanges = true;
    this.productOrgDomainsIn = domains;
  }

  saveOrgSsoAuth(orgSsoAuthIn: IOrganizationSsoAuthIn) {
    this.hasChanges = true;
    this.orgSsoAuthIn = orgSsoAuthIn;
  }

  saveProductJoinCodes() {
    this.hasChanges = true;
  }

  openGroup(panelState: boolean) {
    this.isParentPanelClosed = panelState;
  }

  saveProductSettings() {
    const requestList = [];

    if (this.productOrgDomainsIn.length > 0) {
      //Validate
      const invalid = this.productOrgDomainsIn.find(x => x.domainName === '' || x.roleId === '');
      if (invalid) {
        this.showAlert('Please add a domain and role!');
      } else {
        requestList.push(this.dataService.updateProductOrgDomains(this.productOrgDomainsIn));
      }
    }

    if (this.orgSsoAuthIn != null) {
      requestList.push(this.dataService.updateOrgSsoAuth(this.orgSsoAuthIn));
    }

    this.productOrganizations?.forEach(organization => {
      if (organization?.productJoinCodeSettings && organization?.productJoinCodeSettings.length > 0) {
        requestList.push(this.dataService.setProductJoinCodeSettings(organization.id, organization.productJoinCodeSettings));
      }
    });

    forkJoin(requestList)
      .pipe(takeUntil(this.componentDestroyed$))
      .subscribe(result => {
        if (result) {
          this.showAlert('Product organization updated!');
          this.hasChanges = false;
          this.productOrgDomainsIn = [];
          this.currentAmount = 0;

          if (this.id) {
            this.getProductOrganizationsById(this.id, false);
          }
        }
      });
  }

  async addOrgToProduct() {
    const modal = await this.modalController.create({
      component: AddProductOrganizationModalComponent,
      componentProps: { id: this.id },
      cssClass: 'add-product-org-modal',
    });

    modal.onDidDismiss().then(value => {
      if (value.data && value.data.length > 0) {
        this.saveOrganizationsToProduct(value.data);
      }
    });

    await modal.present();
  }

  async addProductToOrg() {
    const modal = await this.modalController.create({
      component: AddOrgProductModalComponent,
      componentProps: { orgId: this.id },
      cssClass: 'add-product-org-modal',
    });

    modal.onDidDismiss().then(value => {
      if (value.data) {
        if (this.id) {
          const org: IOrganizationSearch = {
            id: this.id,
            name: '',
            country: '',
          };
          this.saveOrganizationToProduct(value.data, org);
        }
      }
    });

    await modal.present();
  }

  saveOrganizationToProduct(product: IProductRenew, organization: IOrganizationSearch) {
    if (product?.id) {
      this.dataService
        .addOrganizationToProduct(product, organization)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((addedOrg: IProductOrganization[]) => {
          if (addedOrg.length > 0) {
            addedOrg.forEach(org => {
              this.productOrganizations = [...this.productOrganizations, org];
            });
          }
        });
    }
  }

  saveOrganizationsToProduct(selectedOrgs: IOrganizationSearch[]) {
    if (this.id) {
      this.dataService
        .addOrganizationsToProduct(this.id, selectedOrgs)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((addedOrg: IProductOrganization[]) => {
          if (addedOrg.length > 0) {
            addedOrg.forEach(organization => {
              this.productOrganizations = [...this.productOrganizations, organization];
            });
          }
        });
    }
  }

  async removeProduct(productOrg: IProductOrganization) {
    const modal = await this.modalController.create({
      component: ConfirmationDialogComponent,
      cssClass: 'confirm-dialog',
      componentProps: {
        headerText: 'Expire product',
        bodyText: `Are you sure you would like to expire ${productOrg?.productName}?`,
        buttonText: 'Expire',
      },
    });

    modal.onDidDismiss().then((overlayEventDetail: OverlayEventDetail) => {
      if (overlayEventDetail.role === 'confirm') {
        if (this.id) {
          this.dataService
            .deleteProductOrganization(productOrg?.id)
            .pipe(takeUntil(this.componentDestroyed$))
            .subscribe(res => {
              if (res === true) {
                const indexToRemove = this.productOrganizations.findIndex(x => x.id === productOrg.id);
                if (indexToRemove !== -1) {
                  this.productOrganizations.splice(indexToRemove, 1);
                  this.productOrganizations = [...this.productOrganizations];
                }
              }
            });
        }
      }
    });

    await modal.present();
  }

  showAlert(alertText: string) {
    this.alertService.presentAlert('', alertText);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
