@if (completedForm) {
  <form [formGroup]="completedForm">
    <div class="parent-container">
      <ion-row>
        <ion-col size="auto" class="heading-col">{{ 'Complete rows in:' | translate | async }}</ion-col>
        <ion-col size="6">
          <app-select-option-control
            style="width: 100%"
            (emitSelected)="setSelectedContent($event)"
            formControlName="refId"
            [textValue]="getFormControlValue()"
            [multiple]="true"
            [backgroundColor]="'#242323'"
            [criteriaType]="type"
            [isCustom]="true"
            [noMargin]="true"
            [linkTypeName]="'Instances'">
          </app-select-option-control>
        </ion-col>
      </ion-row>
    </div>
  </form>
}
