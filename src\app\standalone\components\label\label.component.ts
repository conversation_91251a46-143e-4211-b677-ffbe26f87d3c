import { Component, Input, OnInit } from '@angular/core';
import { IInstanceSectionComponent } from '@app/core/contracts/contract';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { ParseContentPipe } from '../../../shared/pipes/parse-content';

@Component({
    selector: 'app-label',
    templateUrl: './label.component.html',
    styleUrls: ['./label.component.scss'],
    imports: [AsyncPipe, ParseContentPipe, TranslatePipe]
})
export class LabelComponent implements OnInit {
  @Input() instanceComponent: IInstanceSectionComponent | undefined;
  @Input() instanceId: string;
  @Input() inheritedPropertyValue: string | null;
  @Input() defaultValue: string | undefined;
  textValue: string;

  constructor() {}

  ngOnInit() {
    this.setTextValue();
  }

  setTextValue() {
    if (this.instanceComponent?.value && this.instanceComponent.value !== '') {
      this.textValue = this.instanceComponent.value;
    } else if (this.inheritedPropertyValue && this.inheritedPropertyValue !== '') {
      this.textValue = this.inheritedPropertyValue;
    } else if (this.defaultValue) {
      this.textValue = this.defaultValue;
    }
  }
}
