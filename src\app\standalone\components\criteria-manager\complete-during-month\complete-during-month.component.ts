import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IEarningCriteriaContentIn, IEarningCriteriaIn } from '@app/core/contracts/contract';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { AsyncPipe } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { MatFormField, MatLabel, MatSuffix } from '@angular/material/form-field';
import { MatDateRangeInput, MatStartDate, MatEndDate, MatDatepickerToggle, MatDateRangePicker } from '@angular/material/datepicker';

@Component({
    selector: 'app-complete-during-month',
    templateUrl: './complete-during-month.component.html',
    styleUrls: ['./complete-during-month.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, MatFormField, MatLabel, MatDateRangeInput, MatStartDate, MatEndDate, MatDatepickerToggle, MatSuffix, MatDateRangePicker, AsyncPipe, TranslatePipe]
})
export class CompleteDuringMonthComponent implements OnInit, OnDestroy {
  @Input() type: string;
  @Input() earningCriteria: IEarningCriteriaIn;
  @Output() criteriaUpdated: EventEmitter<IEarningCriteriaContentIn[]> = new EventEmitter();

  formValueChanges$: Subscription;
  completeDuringMonthForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private formBuilder: FormBuilder) {}

  ngOnInit() {
    this.createForm();
  }

  createForm() {
    const startDate = new Date(this.earningCriteria?.minValue ? this.earningCriteria?.minValue : '');
    const endDate = new Date(this.earningCriteria?.maxValue ? this.earningCriteria?.maxValue : '');
    this.completeDuringMonthForm = this.formBuilder.group({
      minValue: [startDate],
      maxValue: [endDate],
    });
    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.completeDuringMonthForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      this.setObjectValues();
    });
  }

  setObjectValues() {
    if (this.completeDuringMonthForm.valid) {
      if (this.completeDuringMonthForm.controls['minValue'].value) {
        this.earningCriteria.minValue = Date.parse(this.completeDuringMonthForm.controls.minValue.value);
        this.earningCriteria.maxValue = Date.parse(this.completeDuringMonthForm.controls.maxValue.value);
        this.criteriaUpdated.emit(this.earningCriteria.earningCriteriaContent);
      }
    }
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
