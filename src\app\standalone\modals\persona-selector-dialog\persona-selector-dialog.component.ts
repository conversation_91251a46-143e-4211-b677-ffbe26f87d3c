import { Component, OnInit, ViewChild } from '@angular/core';
import { IGuestContext, IPersonaOption } from '@app/core/contracts/contract';
import { AuthService } from '@app/core/services/auth-service';
import { IonAccordionGroup, IonRadioGroup, ModalController, IonicModule } from '@ionic/angular';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { Subject } from 'rxjs';

@Component({
    selector: 'app-persona-selector-dialog',
    templateUrl: './persona-selector-dialog.component.html',
    styleUrls: ['./persona-selector-dialog.component.scss'],
    imports: [IonicModule, AsyncPipe, TranslatePipe]
})
export class PersonaSelectorDialogComponent implements OnInit {
  @ViewChild(IonAccordionGroup, { static: true }) accordionGroup: IonAccordionGroup;
  @ViewChild(IonRadioGroup) radioGroup: IonRadioGroup;
  options: IPersonaOption[];
  selectedOption: string;
  guestContext: IGuestContext;
  componentDestroyed$: Subject<boolean> = new Subject();
  constructor(
    private modalController: ModalController,
    private authService: AuthService
  ) {}

  ngOnInit() {
    this.options = [this.authService.persona];
  }

  change(event: any) {
    if (!event.detail.value || event.detail.value === 'b6f193b9-06ea-4b7a-8ecf-99be9bff5e13') {
      this.radioGroup.value = null;
    } else if (event.detail.value !== 'ion-accordion-0') {
      let display = this.options.find(o => {
        return o.id === event.detail.value;
      })?.display;

      if (!display) {
        display = this.options
          .find(o => {
            return o.id === 'b6f193b9-06ea-4b7a-8ecf-99be9bff5e13';
          })
          ?.children.find(c => {
            return c.id === event.detail.value;
          })?.display;
      }

      this.guestContext = {
        id: event.detail.value,
        browsingAs: display,
        instanceId: '',
        impersonate: false,
      } as IGuestContext;

      this.modalController.dismiss(this.guestContext);
    }
  }

  openAuth() {
    this.authService.startAuthentication();
  }
}
