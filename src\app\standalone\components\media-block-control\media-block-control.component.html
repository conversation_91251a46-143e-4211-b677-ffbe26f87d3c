@if (!loading) {
  <div class="parent-container" [ngClass]="{ 'side-panel-input-padding': sidePanelPadding }">
    <ion-card class="card-content-container">
      @if (!mediaId && !media?.embedCode && !media?.urlUpload) {
        <ion-item class="inner-item">
          <ion-label position="stacked">
            {{ label | translate | async }}
          </ion-label>
          <ion-grid class="main-container" [ngClass]="{ disabled: getFormStatus() }">
            <ion-row>
              @if (component?.templateField?.isSrcDevice) {
                <ion-col class="button-container">
                  <ion-button [disabled]="disabled" (click)="openMediaTypeDialog('Upload')"> {{ 'Upload' | translate | async }} </ion-button>
                </ion-col>
              }
              @if (component?.templateField?.isSrcRepository) {
                <ion-col class="button-container">
                  <ion-button [disabled]="disabled" (click)="openMediaTypeDialog(null)"> {{ 'Use Existing' | translate | async }} </ion-button>
                </ion-col>
              }
              @if (component?.templateField?.isSrcEmbedCode) {
                <ion-col class="button-container">
                  <ion-button [disabled]="disabled" (click)="openMediaTypeDialog('Embed')"> {{ 'Embed' | translate | async }} </ion-button>
                </ion-col>
              }
            </ion-row>
          </ion-grid>
        </ion-item>
      }
      <ng-container [formGroup]="mediaFormGroup">
        @if (mediaId || media?.embedCode || media?.urlUpload) {
          <ng-container [formGroupName]="formGroupName">
            <app-media-block-type-control-selector
              [isDisabled]="getFormStatus()"
              [formControlName]="component.id"
              [component]="component"
              [assetId]="media?.id"
              [urlUpload]="media?.urlUpload"
              [embedCode]="media?.embedCode"
              [controlBackground]="controlBackground"
              [mediaUploadType]="mediaUploadType"
              (mediaRemoved)="mediaRemoved()"></app-media-block-type-control-selector>
          </ng-container>
        }
      </ng-container>
    </ion-card>
  </div>
}
