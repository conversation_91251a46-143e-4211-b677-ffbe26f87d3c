@if (instance || keyVal) {
  <ion-card (click)="clicked()" [ngClass]="{ dropdown: isDropdown, 'hover-card': isDropdown !== true, 'no-start-padding': hideIcon === false, selected: selected === true }">
    <ion-card-content>
      <ion-item>
        @if (!hideIcon) {
          <ion-thumbnail slot="start">
            <img [src]="iconUrl" onerror="this.src='assets/images/Default Icon.png'" />
          </ion-thumbnail>
        }
        <ion-grid>
          <ion-row>
            @if (instance) {
              <ion-col>
                <ion-label>{{ instance.title | parsePipe: instance?.id | async }}</ion-label>
                @if (!isDropdown) {
                  <ion-card-subtitle [innerHTML]="instance?.feature?.instanceDescriptors ?? '' | parsePipe: instance.id : null : false | async | translate | async"></ion-card-subtitle>
                }
              </ion-col>
            } @else if (keyVal) {
              <ion-col>
                <ion-label>{{ keyVal.value | translate | async }}</ion-label>
              </ion-col>
            }
          </ion-row>
        </ion-grid>
        @if (isDropdown && isModalOpen === false) {
          <ion-icon slot="end" name="chevron-down-outline"></ion-icon>
        } @else if (isDropdown) {
          <ion-icon slot="end" name="chevron-up-outline"></ion-icon>
        }
      </ion-item>
    </ion-card-content>
  </ion-card>
}
