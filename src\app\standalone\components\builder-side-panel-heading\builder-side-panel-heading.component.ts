import { Component, Input } from '@angular/core';
import { NgClass, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-builder-side-panel-heading',
    templateUrl: './builder-side-panel-heading.component.html',
    styleUrls: ['./builder-side-panel-heading.component.scss'],
    imports: [NgClass, AsyncPipe, TranslatePipe]
})
export class BuilderSidePanelHeadingComponent {
  @Input() text: string;
  @Input() isSectionHeading = false;
  constructor() {}
}
