import { ChangeDetectionStrategy, Component, OnDestroy } from '@angular/core';
import { IOrganizationLite } from '@app/core/contracts/contract';
import { KeyValue } from '@app/core/dtos/KeyValue';
import { IonicModule, PopoverController } from '@ionic/angular';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { Subject } from 'rxjs';

@Component({
    templateUrl: 'settings-popover.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [IonicModule, AsyncPipe, TranslatePipe]
})
export class SettingsPopoverComponent implements OnDestroy {
  componentDestroyed$: Subject<boolean> = new Subject();
  managedOrganization: IOrganizationLite[];
  data: KeyValue = {} as KeyValue;
  constructor(private popoverController: PopoverController) {}

  itemSelected(selected?: string, id?: string) {
    this.data = { id: id, value: selected };
    this.popoverController.dismiss(this.data);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
