import { NgClass, AsyncPipe } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { IButton } from '@app/core/contracts/contract';
import { AuthService } from '@app/core/services/auth-service';
import { LayoutService } from '@app/core/services/layout-service';
import { ParseContentPipe } from '@app/shared/pipes/parse-content';
import { IonicModule } from '@ionic/angular';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { first } from 'rxjs';
import { BaseValueComponent } from '../base-control/base-value.component';
import { DataService } from '@app/core/services/data-service';

@Component({
    selector: 'app-button-value',
    templateUrl: './button-value.component.html',
    styleUrls: ['./button-value.component.scss'],
    imports: [IonicModule, NgClass, AsyncPipe, TranslatePipe]
})
export class ButtonValueComponent extends BaseValueComponent implements OnInit {
  @Input() instanceId: string;
  button: IButton;
  stylingDirection: string;
  oldValue: any;

  constructor(
    private authService: AuthService,
    private layoutService: LayoutService,
    private parseContentPipe: ParseContentPipe,
    private dataService: DataService
  ) {
    super();
  }

  get mobileScreen() {
    return this.layoutService.currentScreenSize === 'xs';
  }

  ngOnInit() {
    this.init();
  }

  override setData(): void {
    if (this.instanceComponent?.value != this.oldValue) {
      this.oldValue = this.instanceComponent?.value;
      this.init();
    }
  }

  init() {
    if (this.instanceComponent?.value) {
      this.button = JSON.parse(this.instanceComponent?.value) as IButton;
    }
    this.stylingDirection = this.instanceComponent?.component.templateField.stylingDirection ?? 'Left';
  }

  open() {
    if (this.button?.tagId) {
      this.dataService.addUserTags([this.button.tagId]).subscribe(() => {
        this.navigateToUrl();
      });
    } else {
      this.navigateToUrl();
    }
  }

  private navigateToUrl() {
    this.parseContentPipe
      .transform(this.button.url, this.instanceId, null, true)
      .pipe(first())
      .subscribe((result: string) => {
        if (result === 'login') {
          this.authService.setUserContext(false).subscribe(() => {
            this.authService.startAuthentication();
          });
        } else {
          if (this.layoutService.currentScreenSize === 'xs') {
            window.open(result.startsWith('http') ? result : `https://${result}`, '_self');
          } else if (!this.button.sameUrlNavigation) {
            window.open(result.startsWith('http') ? result : `https://${result}`, '_blank');
          } else {
            window.open(result.startsWith('http') ? result : `https://${result}`, '_self');
          }
        }
      });
  }
}
