@if (emailBlockForm) {
  <form [formGroup]="emailBlockForm" class="parent-container">
    <ion-grid>
      <ion-card class="email-card-container">
        <ion-row>
          <ion-col>
            <div class="header-container">
              <h4>{{ 'Email' | translate | async }}</h4>
            </div>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <app-text-input-control formControlName="templateId" [backgroundColor]="backgroundColor" [label]="'Template Id'" [placeHolder]="'Sendgrid template id'"></app-text-input-control>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <app-text-input-control formControlName="fromAddress" [backgroundColor]="backgroundColor" [label]="'From Email'" [placeHolder]="'<EMAIL>'"></app-text-input-control>
          </ion-col>
        </ion-row>
        <ion-row>
          <ion-col>
            <div class="message-container">
              <div class="subject-container">
                <ion-label>{{ 'Subject:' | translate | async }}</ion-label>
                <ion-input formControlName="subject" placeholder="{{ 'Type here...' | translate | async }}"></ion-input>
              </div>
              <div>
                <app-content-quill-editor
                  [hideQuillPersonalize]="hideQuillPersonalize"
                  (dataChanged)="setMessageValue($event)"
                  [value]="emailBlockForm.controls['message'].value"
                  [placeHolder]="'Compose Body'">
                </app-content-quill-editor>
              </div>
            </div>
          </ion-col>
        </ion-row>
        <!-- <ion-row>
        <ion-col>
          <div class="signature-container">
            <div class="inner-container">
              <div>
                <h5>
                  Your signature will be included when you use this template.
                  <span class="ref-color">Edit signature </span>
                </h5>
              </div>
              <div class="icon-container">
                <a href=""><ion-icon name="open-outline"></ion-icon></a>
              </div>
            </div>
          </div>
        </ion-col>
      </ion-row> -->
      </ion-card>
    </ion-grid>
  </form>
}
