<ion-grid class="parent-container">
  <div class="top-header-container">
    @if (data.level) {
      @if (data?.featureTypeName !== 'Modifiable Learning Container Pages') {
        <ion-row>
          <ion-label class="step-label"> {{ 'Step' | translate | async }} {{ page }}/{{ data.level }} </ion-label>
        </ion-row>
      }
      @if (data?.featureTypeName !== 'Modifiable Learning Container Pages') {
        <ion-row>
          <ion-label class="description-label"> {{ 'Begin by selecting a parent tag.' | translate | async }} </ion-label>
        </ion-row>
      }
      @if (data?.featureTypeName === 'Modifiable Learning Container Pages') {
        <ion-row>
          <ion-label class="description-label"> {{ 'Select your grade:' | translate | async }} </ion-label>
        </ion-row>
      }
    } @else {
      <ion-row>
        <ion-label class="description-label"> {{ data.linkType | translate | async }} </ion-label>
      </ion-row>
    }
  </div>
  <div>
    <form [formGroup]="searchForm">
      <ion-grid style="padding: 0px 10px">
        <ion-row class="search-bar-row">
          <ion-col>
            <ion-searchbar color="dark" formControlName="searchControl" (ionChange)="searchResults()" type="search" placeholder="{{ 'Search' | translate | async }}" showCancelButton="focus" debounce="600"> </ion-searchbar>
          </ion-col>
        </ion-row>
      </ion-grid>
    </form>
  </div>
  <div class="group-container">
    @for (tag of tags$ | async; track tag; let i = $index) {
      <div class="property" (click)="setRow(i, tag)" [ngClass]="{ highlight: selectedIndex === i }">
        <div class="heading-container">
          <ion-grid>
            <ion-row class="checkbox-col">
              <ion-col size="1">
                @if (data.view === 'isTag') {
                  <ion-checkbox [checked]="tag.hasInstanceTags || tag.hasUserTags || tag.hasOrganizationTags" (ionChange)="tagSelection($event, tag)"></ion-checkbox>
                }
              </ion-col>
              @if (tag.inverseParent) {
                <ion-col size="10" (click)="nextTag(tag)">
                  <div class="heading">{{ tag?.name | translate | async }}</div>
                  @if (tag.tagRiasecDetails) {
                    <div class="riasecDetails">{{ tag?.tagRiasecDetails?.onetSocCode }} - {{ tag.tagRiasecDetails?.riasecCode }}</div>
                  }
                </ion-col>
              }
              @if (!tag.inverseParent) {
                <ion-col size="11" (click)="nextTag(tag)">
                  <div class="heading">{{ tag?.name | translate | async }}</div>
                  @if (tag.tagRiasecDetails) {
                    <div class="riasecDetails">{{ tag?.tagRiasecDetails?.onetSocCode }} - {{ tag.tagRiasecDetails?.riasecCode }}</div>
                  }
                </ion-col>
              }
              @if (tag.inverseParent) {
                <ion-col size="1" (click)="nextTag(tag)"> {{ '>' | translate | async }} </ion-col>
              }
            </ion-row>
          </ion-grid>
        </div>
      </div>
    }
  </div>
  @if (data.view === 'isFilter') {
    <div class="bottom-container">
      <ion-row>
        <ion-col size="7">
          <ion-button fill="clear" (click)="onClose()">{{ 'Cancel' | translate | async }}</ion-button>
        </ion-col>
        <ion-col style="display: flex; justify-content: flex-end" size="5">
          <ion-button (click)="onSave()">{{ 'Select' | translate | async }}</ion-button>
        </ion-col>
      </ion-row>
    </div>
  }
  @if (data.view === 'isOrganizations') {
    <div class="bottom-container">
      <ion-row>
        <ion-col size="7">
          <ion-button fill="clear" (click)="onBack()">{{ 'Back' | translate | async }}</ion-button>
        </ion-col>
        <ion-col style="display: flex; justify-content: flex-end" size="5">
          <ion-button (click)="onFinish()">{{ 'Finish' | translate | async }}</ion-button>
        </ion-col>
      </ion-row>
    </div>
  }
  @if (data.view === 'isInstances') {
    <div class="bottom-container">
      <ion-row>
        <ion-col size="7">
          <ion-button fill="clear" (click)="onBack()">{{ 'Back' | translate | async }}</ion-button>
        </ion-col>
        <ion-col style="display: flex; justify-content: flex-end" size="5">
          <ion-button (click)="onFinish()">{{ 'Finish' | translate | async }}</ion-button>
        </ion-col>
      </ion-row>
    </div>
  }
  @if (data.view === 'isTag') {
    <div class="bottom-container">
      <ion-row>
        <ion-col size="7">
          <ion-button fill="clear" (click)="onBack()">{{ 'Back' | translate | async }}</ion-button>
        </ion-col>
        <ion-col style="display: flex; justify-content: flex-end" size="5">
          <ion-button (click)="onFinish()">{{ 'Finish' | translate | async }}</ion-button>
        </ion-col>
      </ion-row>
    </div>
  }
</ion-grid>
