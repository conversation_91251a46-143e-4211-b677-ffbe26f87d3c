<div class="parent-container">
  @if (authService.user) {
    <div class="ion-padding">
      <h1 position="stacked">{{ "Oh no! You don't have access to this product." | translate | async }}</h1>
      <p>{{ "Please contact your Administrator or email Edge Factor's Support Team at " | translate | async }} <a href="mailto:<EMAIL>">support&#64;edgefactor.com</a></p>
    </div>
  }
  @if (!authService.user) {
    <div class="ion-padding">
      <h1 position="stacked">{{ 'Login or Create your Account' | translate | async }}</h1>
      <p>
        {{ 'Unlock your Career Journey by logging in or creating a free Edge Factor account!' | translate | async }} <br />
        {{ 'Questions? Email ' | translate | async }} <a href="mailto:<EMAIL>">support&#64;edgefactor.com</a>.
      </p>
    </div>
  }

  @if (authService.user) {
    <div class="button-container">
      <ion-buttons class="close-button">
        <ion-button (click)="confirm()">{{ 'Okay' | translate | async }}</ion-button>
      </ion-buttons>
    </div>
  }
  @if (!authService.user) {
    <div class="guest-button-container">
      <ion-buttons class="guest-buttons">
        <ion-button (click)="openAuth()" fill="clear">{{ 'Login' | translate | async }}</ion-button>
        <ion-button (click)="openRegistration()" fill="clear">{{ 'Create' | translate | async }}</ion-button>
      </ion-buttons>
    </div>
  }
</div>
