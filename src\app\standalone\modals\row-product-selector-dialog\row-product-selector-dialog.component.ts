import { Component, Inject, OnD<PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { Observable, Subject, takeUntil } from 'rxjs';
import { IProduct, IRowProductIn } from '@app/core/contracts/contract';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { DataService } from '@app/core/services/data-service';
import { IonicModule } from '@ionic/angular';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
    selector: 'app-row-product-selector-dialog',
    templateUrl: './row-product-selector-dialog.component.html',
    styleUrl: './row-product-selector-dialog.component.scss',
    encapsulation: ViewEncapsulation.None,
    imports: [IonicModule, AsyncPipe, TranslatePipe]
})
export class RowProductSelectorDialogComponent implements OnInit, OnD<PERSON>roy {
  products$: Observable<IProduct[]>;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(
    public dialogRef: MatDialogRef<RowProductSelectorDialogComponent>,
    private dataService: DataService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    dialogRef.disableClose = true;
    dialogRef.backdropClick().subscribe(() => {
      dialogRef.close();
    });
  }

  ngOnInit() {
    this.loadData();
  }

  loadData() {
    this.products$ = this.dataService.getRowAllProducts(this.data.rowId).pipe(takeUntil(this.componentDestroyed$));
  }

  productSelection(event: any, product: IProduct) {
    const rowProductIn: IRowProductIn = {
      rowId: this.data.rowId,
      productId: product.id,
    };
    const hasProduct = !product.hasRowProduct;
    if (hasProduct) {
      this.dataService
        .addRowProduct(rowProductIn)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          product.hasRowProduct = hasProduct;
        });
    } else {
      this.dataService
        .removeRowProduct(this.data.rowId, product.id)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe(() => {
          product.hasRowProduct = hasProduct;
        });
    }
  }

  onFinish() {
    const isDone = true;
    this.dialogRef.close(isDone);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
