<div class="chip-list-container">
  <mat-form-field>
    <mat-chip-grid #chipList aria-label="Tag selection">
      @for (tag of existingTags$ | async; track tag) {
        <mat-chip-option (removed)="removeDirect(tag)">
          <div class="name">{{ tag.name }}</div>
          <button matChipRemove [attr.aria-label]="('remove ' | translate | async) + ' ' + tag.name">
            <mat-icon>cancel</mat-icon>
          </button>
        </mat-chip-option>
      }
      <input [matChipInputFor]="chipList" [matChipInputSeparatorKeyCodes]="separatorKeysCodes" [matChipInputAddOnBlur]="addOnBlur" (matChipInputTokenEnd)="add($event)" />
      <mat-label (click)="openTagModal()">{{
        component?.templateField?.placeHolderText && component?.templateField?.placeHolderText?.length > 0
          ? (component?.templateField?.placeHolderText | translate | async)
          : ('Add tag' | translate | async)
      }}</mat-label>
    </mat-chip-grid>
  </mat-form-field>
</div>
