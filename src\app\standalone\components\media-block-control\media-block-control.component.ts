import { NgClass, AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, forwardRef } from '@angular/core';
import { FormsModule, NG_VALIDATORS, NG_VALUE_ACCESSOR, ReactiveFormsModule, UntypedFormGroup } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { IAsset, IComponent } from '@app/core/contracts/contract';
import { MediaUploadType } from '@app/core/enums/media-upload-type';
import { DataService } from '@app/core/services/data-service';
import { MediaTypeDialogComponent } from '@app/standalone/modals/media-type-dialog/media-type-dialog.component';
import { IonicModule, ModalController } from '@ionic/angular';
import { OverlayEventDetail } from '@ionic/core';
import { Subject, takeUntil } from 'rxjs';
import { BaseControlComponent } from '@app/standalone/components/base-control/base-control.component';
import { MediaBlockTypeControlSelectorComponent } from '@app/standalone/components/media-block-type-control-selector/media-block-type-control-selector.component';
@Component({
    selector: 'app-media-block-control',
    templateUrl: './media-block-control.component.html',
    styleUrls: ['./media-block-control.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => MediaBlockControlComponent),
        },
        {
            provide: NG_VALIDATORS,
            multi: true,
            useExisting: forwardRef(() => MediaBlockControlComponent),
        },
    ],
    imports: [NgClass, IonicModule, FormsModule, ReactiveFormsModule, MediaBlockTypeControlSelectorComponent, AsyncPipe, TranslatePipe]
})
export class MediaBlockControlComponent extends BaseControlComponent implements OnInit, OnDestroy {
  @Input() override label: string;
  @Input() component: IComponent;
  @Input() controlBackground: string;
  @Input() mediaId: string;
  @Input() mediaFormGroup!: UntypedFormGroup;
  @Input() formGroupName: string;
  @Input() sidePanelPadding = false;
  @Output() controlValueChanged: EventEmitter<string | null> = new EventEmitter();
  media: IAsset;
  mediaUploadType = 'Upload';
  componentDestroyed$: Subject<boolean> = new Subject();
  loading = true;

  constructor(
    private activatedRoute: ActivatedRoute,
    private dataService: DataService,
    private modalController: ModalController
  ) {
    super();
  }

  ngOnInit() {
    if (this.mediaFormGroup.get([this.formGroupName, this.component.id])?.value && !this.mediaId) {
      this.mediaId = this.mediaFormGroup.get([this.formGroupName, this.component.id])?.value;
    }

    this.mediaUploadType = this.activatedRoute.snapshot.queryParams['uploadType'] ?? this.component.templateField.fileTypeBw;
    this.mediaUploadType = MediaUploadType[+this.mediaUploadType] ?? MediaUploadType[MediaUploadType.Upload];

    if (this.mediaId) {
      this.getMedia();
    } else {
      this.media = {} as IAsset;
      this.loading = false;
    }
    //this.subscribeToChanges();
  }

  assetReset(assetId: string) {
    this.mediaId = assetId;
    this.getMedia();
  }

  mediaRemoved() {
    this.mediaId = '';
    this.media = {} as IAsset;
  }

  getMedia() {
    this.loading = true;
    if (this.mediaId && this.mediaId.length > 0) {
      this.dataService
        .getAssetDetailsById(this.mediaId)
        .pipe(takeUntil(this.componentDestroyed$))
        .subscribe((media: IAsset) => {
          if (media) {
            if ((media.urlUpload || media.embedCode) && !this.activatedRoute.snapshot.queryParams['uploadType']) {
              this.mediaUploadType = MediaUploadType[MediaUploadType.Upload];
            }
            this.media = media;
          } else {
            this.media = { mediaUploadType: this.mediaUploadType } as IAsset;
          }
          this.loading = false;
        });
    }
  }

  subscribeToChanges() {
    this.mediaFormGroup?.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {
      const assetId = this.mediaFormGroup.get([this.formGroupName, this.component.id])?.value;

      if (this.mediaId === assetId) {
        return;
      }
      this.mediaId = assetId;
      if (!assetId) {
        this.media = {} as IAsset;
      }
      this.setValue(assetId);
    });
  }

  getFormStatus() {
    return this.mediaFormGroup.status === 'DISABLED';
  }

  async openMediaTypeDialog(uploadType: string | null) {
    const modal = await this.modalController.create({
      component: MediaTypeDialogComponent,
      componentProps: {
        component: this.component,
        controlBackground: this.controlBackground,
        assetId: this.media?.id,
        urlUpload: this.media?.id,
        embedcode: this.media?.embedCode,
        mediaUploadType: uploadType,
      },
      cssClass: 'media-type-dialog',
    });

    modal.onDidDismiss().then((overlayEventDetail: OverlayEventDetail) => {
      if (overlayEventDetail.data) {
        this.media.id = overlayEventDetail.data;
        this.setValue(this.media.id);
      }
    });

    await modal.present();
  }

  override setValue(value: string | null): void {
    this.controlValueChanged.next(value);
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
