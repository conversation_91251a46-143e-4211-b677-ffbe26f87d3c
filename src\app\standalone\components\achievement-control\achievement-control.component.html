<ion-card class="card-container">
  <div class="hex-grid" style="float: left">
    <div class="hex">
      <div class="image" style="background-image:url('{{ imageUrl }}');"></div>
    </div>
  </div>

  <div class="title-container">
    <span class="title"> {{ achievementName | translate | async }}</span>
    <div>
      <span class="sub-title">{{ 'issued by' | translate | async }}: &nbsp;</span><span class="issued">{{ issuedBy }}</span>
    </div>
    <span class="description">{{ description | translate | async }}</span>
  </div>
</ion-card>
