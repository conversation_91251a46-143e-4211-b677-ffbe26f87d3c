<div class="parent-container">
  @if (filterForm) {
    <form [formGroup]="filterForm">
      <ion-grid>
        <ion-row>
          <ion-col size="3">
            @if (rowFilterFieldKeyValue && rowFilterFieldKeyValue.length > 0) {
              <ion-select (ionChange)="clearFilterField()" interface="popover" formControlName="rowFilterField">
                @for (field of rowFilterFieldKeyValue; track field) {
                  <ion-select-option [value]="field.id">
                    {{ field.value | translate | async }}
                  </ion-select-option>
                }
              </ion-select>
            }
          </ion-col>
          <ion-col size="2">
            @if (rowFilterConditionKeyValue && rowFilterConditionKeyValue.length > 0) {
              <ion-select (ionChange)="clearConditionField()" interface="popover" formControlName="rowFilterCondition">
                @for (condition of rowFilterConditionKeyValue; track condition) {
                  <ion-select-option [value]="condition.id">
                    {{ condition.value | translate | async }}
                  </ion-select-option>
                }
              </ion-select>
            }
          </ion-col>
          <ion-col size="6">
            @switch (getFilterInputType()) {
              @case ('access') {
                @if (accessOptions && accessOptions.length > 0) {
                  @if (!multipleOptions) {
                    <div>
                      <ion-select interface="popover" formControlName="rowFilterValue">
                        @for (option of accessOptions; track option) {
                          <ion-select-option [value]="option.value">
                            {{ option.name | translate | async }}
                          </ion-select-option>
                        }
                      </ion-select>
                    </div>
                  } @else {
                    <app-chip-option-select
                      (formControlUpdatedOut)="rowFilterValueFormControlUpdated($event)"
                      [formControlIn]="getFormControl('rowFilterValue')"
                      [listItems]="accessOptions"
                      [rowFilterCondition]="getFormControlValue('rowFilterCondition')">
                    </app-chip-option-select>
                  }
                }
              }
              @case ('role') {
                @if (userRoles && userRoles.length > 0) {
                  @if (!multipleOptions) {
                    <div>
                      <ion-select interface="popover" formControlName="rowFilterValue">
                        @for (role of userRoles; track role) {
                          <ion-select-option [value]="role.value">
                            {{ role.value | translate | async }}
                          </ion-select-option>
                        }
                      </ion-select>
                    </div>
                  } @else {
                    <app-chip-option-select
                      (formControlUpdatedOut)="rowFilterValueFormControlUpdated($event)"
                      [formControlIn]="getFormControl('rowFilterValue')"
                      [listItems]="userRoles"
                      [rowFilterCondition]="getFormControlValue('rowFilterCondition')">
                    </app-chip-option-select>
                  }
                }
              }
              @case ('featureType') {
                @if (featureTypes && featureTypes.length > 0) {
                  @if (!multipleOptions) {
                    <div>
                      <ion-select interface="popover" formControlName="rowFilterValue">
                        @for (type of featureTypes; track type) {
                          <ion-select-option [value]="type.id">
                            {{ type.value | translate | async }}
                          </ion-select-option>
                        }
                      </ion-select>
                    </div>
                  } @else {
                    <app-chip-option-select
                      (formControlUpdatedOut)="rowFilterValueFormControlUpdated($event)"
                      [formControlIn]="getFormControl('rowFilterValue')"
                      [listItems]="featureTypes"
                      [rowFilterCondition]="getFormControlValue('rowFilterCondition')">
                    </app-chip-option-select>
                  }
                }
              }
              @case ('instanceAssignmentFeatureType') {
                @if (featureTypes && featureTypes.length > 0) {
                  @if (!multipleOptions) {
                    <div>
                      <ion-select interface="popover" formControlName="rowFilterValue">
                        @for (type of featureTypes; track type) {
                          <ion-select-option [value]="type.id">
                            {{ type.value | translate | async }}
                          </ion-select-option>
                        }
                      </ion-select>
                    </div>
                  } @else {
                    <app-chip-option-select
                      (formControlUpdatedOut)="rowFilterValueFormControlUpdated($event)"
                      [formControlIn]="getFormControl('rowFilterValue')"
                      [listItems]="featureTypes"
                      [rowFilterCondition]="getFormControlValue('rowFilterCondition')">
                    </app-chip-option-select>
                  }
                }
              }
              @case ('status') {
                @if (statusOptions && statusOptions.length > 0) {
                  @if (!multipleOptions) {
                    <div>
                      <ion-select [multiple]="multipleOptions" interface="popover" formControlName="rowFilterValue">
                        @for (option of statusOptions; track option) {
                          <ion-select-option [value]="option.value">
                            {{ option.value | translate | async }}
                          </ion-select-option>
                        }
                      </ion-select>
                    </div>
                  } @else {
                    <app-chip-option-select
                      (formControlUpdatedOut)="rowFilterValueFormControlUpdated($event)"
                      [formControlIn]="getFormControl('rowFilterValue')"
                      [listItems]="statusOptions"
                      [rowFilterCondition]="getFormControlValue('rowFilterCondition')">
                    </app-chip-option-select>
                  }
                }
              }
              @case ('matches') {
                @if (rowFilterOptionKeyValue && rowFilterOptionKeyValue.length > 0) {
                  @if (!multipleOptions) {
                    <div>
                      <ion-select interface="popover" formControlName="rowFilterValue">
                        @for (type of rowFilterOptionKeyValue; track type) {
                          <ion-select-option [value]="type.id">
                            {{ type.value | translate | async }}
                          </ion-select-option>
                        }
                      </ion-select>
                    </div>
                  } @else {
                    <app-chip-option-select
                      (formControlUpdatedOut)="rowFilterValueFormControlUpdated($event)"
                      [formControlIn]="getFormControl('rowFilterValue')"
                      [listItems]="rowFilterOptionKeyValue"
                      [rowFilterCondition]="getFormControlValue('rowFilterCondition')">
                    </app-chip-option-select>
                  }
                }
              }
              @case ('productTags') {
                @if (products && products.length > 0) {
                  @if (!multipleOptions) {
                    <div>
                      <ion-select interface="popover" formControlName="rowFilterValue">
                        @for (product of products; track product) {
                          <ion-select-option [value]="product.id">
                            {{ product.value | translate | async }}
                          </ion-select-option>
                        }
                      </ion-select>
                    </div>
                  } @else {
                    <app-chip-option-select
                      (formControlUpdatedOut)="rowFilterValueFormControlUpdated($event)"
                      [formControlIn]="getFormControl('rowFilterValue')"
                      [listItems]="products"
                      [rowFilterCondition]="getFormControlValue('rowFilterCondition')">
                    </app-chip-option-select>
                  }
                }
              }
              @case ('sponsoredBy') {
                <ion-button (click)="addOrganizationsToNetworkModal()">Selected Organization(s) </ion-button>
              }
              @case ('progress') {
                <ion-select [multiple]="multipleOptions" interface="popover" formControlName="rowFilterValue">
                  <ion-select-option [value]="'NotStarted'"> {{ 'Not Started' | translate | async }}</ion-select-option>
                  <ion-select-option [value]="'InProgress'"> {{ 'In Progress' | translate | async }}</ion-select-option>
                  <ion-select-option [value]="'Completed'"> {{ 'Completed' | translate | async }}</ion-select-option>
                </ion-select>
              }
              @case ('enrollment') {
                <ion-input formControlName="rowFilterValue" [disabled]="true"
                  ><span style="margin-left: 10px">{{ 'Enrolled' | translate | async }}</span></ion-input
                >
              }
              @case ('roleTags') {
                @if (userRoles && userRoles.length > 0) {
                  @if (!multipleOptions) {
                    <div>
                      <ion-select interface="popover" formControlName="rowFilterValue">
                        @for (role of userRoles; track role) {
                          <ion-select-option [value]="role.value">
                            {{ role.value | translate | async }}
                          </ion-select-option>
                        }
                      </ion-select>
                    </div>
                  } @else {
                    <app-chip-option-select
                      (formControlUpdatedOut)="rowFilterValueFormControlUpdated($event)"
                      [formControlIn]="getFormControl('rowFilterValue')"
                      [listItems]="userRoles"
                      [rowFilterCondition]="getFormControlValue('rowFilterCondition')">
                    </app-chip-option-select>
                  }
                }
              }
              @case ('number') {
                <ion-input type="number" formControlName="rowFilterValue"></ion-input>
              }
              @case ('tag') {
                @if (tags$ | async; as tags) {
                  <app-tag-select-option-control
                    [isFilterRowContainer]="true"
                    [options]="tags"
                    [multiple]="multipleOptions"
                    [placeHolder]="'--Select--'"
                    [textValue]="getFormControlValue('rowFilterValue')"
                    [disableChipDelete]="true"
                    formControlName="rowFilterValue">
                  </app-tag-select-option-control>
                }
              }
              @case ('networkTag') {
                @if (networktags$ | async; as tags) {
                  <app-tag-select-option-control
                    [isFilterRowContainer]="true"
                    [options]="tags"
                    [multiple]="multipleOptions"
                    [placeHolder]="'--Select--'"
                    [textValue]="getFormControlValue('rowFilterValue')"
                    [disableChipDelete]="true"
                    formControlName="rowFilterValue">
                  </app-tag-select-option-control>
                }
              }
              @case ('instanceTag') {
                @if (instanceTags$ | async; as tags) {
                  <app-tag-select-option-control
                    [isFilterRowContainer]="true"
                    [options]="tags"
                    [multiple]="multipleOptions"
                    [placeHolder]="'--Select--'"
                    [textValue]="getFormControlValue('rowFilterValue')"
                    [disableChipDelete]="true"
                    formControlName="rowFilterValue">
                  </app-tag-select-option-control>
                }
              }
              @case ('journeyStage') {
                @if (journeyStages$ | async; as tags) {
                  <app-tag-select-option-control
                    [isFilterRowContainer]="true"
                    [options]="tags"
                    [multiple]="multipleOptions"
                    [placeHolder]="'--Select--'"
                    [textValue]="getFormControlValue('rowFilterValue')"
                    [disableChipDelete]="true"
                    formControlName="rowFilterValue">
                  </app-tag-select-option-control>
                }
              }
              @case ('organizationTag') {
                @if (organizationTags$ | async; as tags) {
                  <app-tag-select-option-control
                    [isFilterRowContainer]="true"
                    [options]="tags"
                    [multiple]="multipleOptions"
                    [placeHolder]="'--Select--'"
                    [textValue]="getFormControlValue('rowFilterValue')"
                    [disableChipDelete]="true"
                    formControlName="rowFilterValue">
                  </app-tag-select-option-control>
                }
              }
              @default {
                <ion-input formControlName="rowFilterValue"></ion-input>
              }
            }
          </ion-col>
          <ion-col class="remove-col" size="1">
            @if (allowDelete) {
              <ion-button color="primary" size="small" fill="clear" (click)="deleteFilter()">
                <ion-icon name="remove-circle-outline"></ion-icon>
              </ion-button>
            }
          </ion-col>
        </ion-row>
      </ion-grid>
    </form>
  }
</div>
