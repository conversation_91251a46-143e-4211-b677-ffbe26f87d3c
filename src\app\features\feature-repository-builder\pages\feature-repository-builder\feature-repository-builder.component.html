@if (feature$ | async; as feature) {
  <div class="repository">
    <div class="header-container" style="--background-image: url('assets/images/admin-header.jpg')">
      <h6>{{ 'You are' | translate | async }} {{ tabDescription | translate | async }} {{ 'the template for' | translate | async }}</h6>
      <h1>{{ feature.title }}</h1>
    </div>
    <div class="content">
      <mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start" (selectedTabChange)="tabSelected($event)">
        <mat-tab id="material-tab" [label]="('REPOSITORY' | translate | async) || 'REPOSITORY'">
          <ng-template matTabContent>
            <app-feature-repository-dashboard [feature]="feature"></app-feature-repository-dashboard>
          </ng-template>
        </mat-tab>
        @if (isEfManager === true) {
          <mat-tab class="material-tab" [label]="('TEMPLATE' | translate | async) || 'TEMPLATE'">
            <ng-template matTabContent>
              <app-feature-repository-template [feature]="feature" (featureUpdated)="updateFeature($event)"></app-feature-repository-template>
            </ng-template>
          </mat-tab>
        }
        @if (isEfManager === true) {
          <mat-tab class="material-tab" [label]="('COMMUNICATIONS' | translate | async) || 'COMMUNICATIONS'">
            <ng-template matTabContent>
              <app-feature-repository-communications [feature]="feature"></app-feature-repository-communications>
            </ng-template>
          </mat-tab>
        }
        @if (isEfManager === true) {
          <mat-tab class="material-tab" [label]="('ANALYTICS' | translate | async) || 'ANALYTICS'">
            <ng-template matTabContent>
              <app-feature-repository-analytics [feature]="feature"></app-feature-repository-analytics>
            </ng-template>
          </mat-tab>
        }
      </mat-tab-group>
    </div>
  </div>
}
