<div aria-hidden="true" class="layout" [style]="'--background-image:url(' + imageUrl + ');'" (click)="route()" (keyup)="route()">
  <div class="gradient">
    <div class="text-container">
      <ion-row>
        <app-thumbnail-icons
          [row]="row"
          [content]="content"
          [instance]="instance"
          [readingMode]="readingMode"
          [isDraggable]="isDraggable"
          [isEducator]="isEducator"
          [hasAdminAccess]="hasAdminAccess"
          [hideAddButton]="hideAddButton"
          [canHover]="false"
          [isAssignmentRow]="isAssignmentRow"
          (contentRemoved)="emitContentRemoved()"
          (editCustomRowContent)="editContent()">
        </app-thumbnail-icons>
      </ion-row>
      <h1>{{ title | translate | async }}</h1>
      @if (description) {
        <p>{{ description | translate | async }}</p>
      }
      @if (action) {
        <ion-button size="small">
          {{ action | translate | async }}
        </ion-button>
      }
    </div>
  </div>
</div>
