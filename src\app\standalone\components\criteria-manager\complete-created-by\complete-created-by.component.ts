import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, UntypedFormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IEarningCriteriaContentIn, IEarningCriteriaContentSearch, IEarningCriteriaIn } from '@app/core/contracts/contract';
import { Subject, Subscription, takeUntil } from 'rxjs';
import { AsyncPipe } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { TranslatePipe } from '@app/shared/pipes/translate';
import { SelectOptionControlComponent } from '../../select-option-control/select-option-control.component';

@Component({
    selector: 'app-complete-created-by',
    templateUrl: './complete-created-by.component.html',
    styleUrls: ['./complete-created-by.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, IonicModule, SelectOptionControlComponent, AsyncPipe, TranslatePipe]
})
export class CompleteCreatedByComponent implements OnInit, OnDestroy {
  @Input() type: string;
  @Input() earningCriteria: IEarningCriteriaIn;
  @Output() criteriaUpdated: EventEmitter<IEarningCriteriaContentIn[]> = new EventEmitter();

  formValueChanges$: Subscription;
  completeCreatedByForm: UntypedFormGroup;
  componentDestroyed$: Subject<boolean> = new Subject();

  constructor(private formBuilder: FormBuilder) {}

  ngOnInit() {
    this.createForm();
  }

  createForm() {
    this.completeCreatedByForm = this.formBuilder.group({
      refId: [this.earningCriteria.earningCriteriaContent?.[0]?.refId],
    });

    this.subscribeToFormChanges();
  }

  subscribeToFormChanges() {
    this.formValueChanges$ = this.completeCreatedByForm.valueChanges.pipe(takeUntil(this.componentDestroyed$)).subscribe(() => {});
  }

  setSelectedContent(data: IEarningCriteriaContentSearch) {
    const criteriaContentIn = {
      id: this.earningCriteria.earningCriteriaContent?.[0]?.id,
      earningCriteriaId: this.earningCriteria.id,
      refId: data.id,
      type: data.type,
      name: data.name,
    } as IEarningCriteriaContentIn;

    const index = this.earningCriteria.earningCriteriaContent?.findIndex(x => x.id === criteriaContentIn.id);

    if (!index || index === -1) {
      if (this.earningCriteria.earningCriteriaContent) {
        this.earningCriteria.earningCriteriaContent.push(criteriaContentIn);
      } else {
        this.earningCriteria.earningCriteriaContent = [criteriaContentIn];
      }
    } else {
      this.earningCriteria.earningCriteriaContent[0] = criteriaContentIn;
    }

    this.criteriaUpdated.emit(this.earningCriteria.earningCriteriaContent);
  }

  getFormControlValue() {
    return this.completeCreatedByForm.get('refId')?.value;
  }

  ngOnDestroy() {
    this.componentDestroyed$.next(true);
    this.componentDestroyed$.complete();
  }
}
