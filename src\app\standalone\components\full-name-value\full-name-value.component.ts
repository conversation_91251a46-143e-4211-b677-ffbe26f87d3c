import { Component, Input, OnInit } from '@angular/core';
import { IInstanceSectionComponent } from '@app/core/contracts/contract';
import { SystemPropertiesService } from '@app/core/services/system-properties.service';
import { AsyncPipe } from '@angular/common';
import { TranslatePipe } from '@app/shared/pipes/translate';

@Component({
  selector: 'app-full-name-value',
  templateUrl: './full-name-value.component.html',
  styleUrls: ['./full-name-value.component.scss'],
  imports: [AsyncPipe, TranslatePipe],
  standalone: true,
})
export class FullNameValueComponent implements OnInit {
  @Input() instanceSectionComponent: IInstanceSectionComponent | undefined;
  constructor(private systemPropertyService: SystemPropertiesService) {}

  ngOnInit() {
    this.getTemplateFieldValue();
  }

  getTemplateFieldValue() {
    if (!this.systemPropertyService.userProperties) {
      return null;
    }

    const fullName = this.systemPropertyService.userProperties.find(x => x.key.includes('name'));
    return fullName?.value;
  }
}
